/**
 * Pagination fix styles
 * These styles override the default pagination styles to fix alignment issues
 */

/* Fix for pagination container */
.pagination-container {
  text-align: center !important;
}

.pagination-wrapper {
  display: inline-block !important;
}

.pagination {
  display: flex !important;
  flex-wrap: wrap !important;
  justify-content: center !important;
  align-items: center !important;
  margin: 20px 0 !important;
  padding: 0 !important;
  max-width: 100% !important;
  gap: 5px !important; /* Add gap between pagination items */
}

/* Fix for WordPress pagination */
.pagination li {
  display: inline-block !important;
  margin: 0 !important; /* Removed margin since we're using gap */
  float: none !important;
  list-style: none !important;
}

.pagination li a,
.pagination li span.current,
.pagination li span.page-numbers {
  display: inline-block !important;
  float: none !important;
  padding: 8px 12px !important; /* Reduced padding for more links */
  text-decoration: none !important;
  border-radius: 5px !important;
  transition: all 0.3s ease !important;
  vertical-align: middle !important;
  line-height: 1.5 !important;
  margin: 0 !important;
  border: 1px solid #ddd !important;
  min-width: 40px !important; /* Set minimum width for consistency */
  text-align: center !important;
}

.pagination li span.current {
  background-color: rgb(54, 73, 226) !important;
  color: white !important;
  font-weight: bold !important;
  border-color: rgb(54, 73, 226) !important;
}

.pagination li a.prev,
.pagination li a.next {
  background-color: rgb(226, 207, 54) !important;
  color: white !important;
  font-weight: bold !important;
}

/* Fix for WordPress default pagination classes */
.page-numbers {
  display: inline-block !important;
  margin: 0 2px !important;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .pagination li a,
  .pagination li span.current,
  .pagination li span.page-numbers {
    padding: 6px 10px !important;
    font-size: 0.9rem !important;
    min-width: 35px !important;
  }
}

@media screen and (max-width: 480px) {
  .pagination li a,
  .pagination li span.current,
  .pagination li span.page-numbers {
    padding: 5px 8px !important;
    font-size: 0.85rem !important;
    min-width: 30px !important;
  }
  
  /* Hide some pagination items on very small screens */
  .pagination li:nth-child(n+8):nth-child(-n+12) {
    display: none !important;
  }
}