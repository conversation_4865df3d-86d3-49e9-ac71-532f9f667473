<?php 
/**
 * Page Template: Apply Online Page
 * Description: This template is intended for the online application form
 */

// Include the nationalities file at the top of the page
include(APPPATH . 'views/nationalities.php');
?>

<!-- MAIN -->
<main role="main">
  <!-- Content -->
  <article>
    <div class="line">
      <header class="rounded-div section background-primary background-transparent text-center"
        data-image-src="<?php echo base_url('assets/img/parallax-05.webp'); ?>">
        <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">Apply Online</h1>
      </header>
    </div>
    <div class="section background-white">
      <div class="line">
        <div class="margin">

          <!-- School Information -->
          <div class="s-12 m-12 l-4">
            <h2 class="text-uppercase text-strong">Application Information</h2>
            <hr class="break break-small background-primary">
            <p>We are excited that you would like to apply for a place at our School. Please fill out the Form below. We will get in touch at the earliest time possible. Please note:</p>
            <ul class='margin-left-20'>
                <li>A <span class='any-error'>red</span> left border signifies a required field which must be filled in</li>
                <li>use the tips in the input fields as guide as you fill out the form</li>
            </ul>

          <!-- Application Form -->
          <div class="s-12 m-12 l-12">
            <h2 class="text-uppercase text-strong margin-m-top-50">Application Form</h2>
            <hr class="break break-small background-primary">
            
            <?php
            if ($this->session->flashdata('email_not_sent')) {
               echo '<div class="any-error">'.$this->session->flashdata('email_not_sent').'</div><br />';
             }
            if ($this->session->flashdata('errors')) {
                echo '<div class="any-error">'.$this->session->flashdata('errors').'</div><br />';
            }
            if($this->session->flashdata('email_sent')){
                echo '<div class="any-success">'.$this->session->flashdata('email_sent').'</div><br />';
              }
              if($this->session->flashdata('email_failure')){
                echo '<div class="any-error">'.$this->session->flashdata('email_failure').'</div><br />';
              }
            ?>
            
            <!-- Application Form -->
            <?php $attributes = array("class" => "customform"); ?>
            <?php echo form_open("/pages/apply", $attributes); ?>
            
            <!-- Part 1: Pupil's Particulars -->
            <h3 class="application-form text-uppercase margin-bottom-10">Part 1: Pupil's Particulars</h3>
            <hr />
            
            <div class="grid margin">
              <div class="s-12 m-5 l-5">
                <?php echo form_label("<span class='margin-bottom-10'>Last Name</span>"); ?>
                <input name="lname" class="required" placeholder="Last Name" title="Pupil's Last Name" type="text" required />
              </div>
              <div class="s-12 m-5 l-5">
                <?php echo form_label("<span class='margin-bottom-10'>First Name</span>"); ?>
                <input name="fname" class="required" placeholder="First Name" title="Pupil's First Name" type="text" required />
              </div>
              <div class="s-12 m-2 l-2">
                <?php echo form_label("<span class='margin-bottom-10'>Sex</span>"); ?>
                <select name="sex" class="required" required>
                  <option value="">Select</option>
                  <option value="M">Male</option>
                  <option value="F">Female</option>
                </select>
              </div>
            </div>
            
            <div class="grid margin">
              <div class="s-12 m-6 l-4">
                <?php echo form_label("<span class='margin-bottom-10'>Date of Birth</span>"); ?>
                <input id="datepicker" name="dob" class="required" placeholder="Date of Birth" title="Pupil's Date of Birth" type="text" required />
                <script>
                  $('#datepicker').datepicker({ dateFormat: 'dd-mm-yy' });
                </script>
              </div>
              <div class="s-12 m-6 l-4">
                <?php echo form_label("<span class='margin-bottom-10'>Nationality</span>"); ?>
                <select name="nationality" class="required" required>
                  <?php echo get_nationality_options(); ?>
                </select>
              </div>
              <div class="s-12 m-8 l-4">
                <?php echo form_label("<span class='margin-bottom-10'>Grade Applied For</span>"); ?>
                <select name="grade" class="required border-radius" required>
                  <option value="">Select Grade</option>
                  <option value="Grade 1">Grade 1</option>
                  <option value="Grade 2">Grade 2</option>
                  <option value="Grade 3">Grade 3</option>
                  <option value="Grade 4">Grade 4</option>
                  <option value="Grade 5">Grade 5</option>
                  <option value="Grade 6">Grade 6</option>
                  <option value="Grade 7">Grade 7</option>
                </select>
              </div>
            </div>
            
            <div class="grid margin">
              <div class="s-12 m-12 l-12">
                <?php echo form_label("<span class='margin-bottom-10'>Previous School</span>"); ?>
                <input name="previous_school" class="required" placeholder="Previous School" title="Previous School" type="text" required />
              </div>
            </div>
            
            <div class="grid margin">
              <div class="s-12 m-12 l-12">
                <?php echo form_label("<span class='margin-bottom-10'>Does the child have any handicap or special needs?</span>"); ?>
                <textarea name="handicap" class="" placeholder="If yes, please specify. If no, write 'None'" rows="3"></textarea>
              </div>
            </div>
            
            <!-- Part 2: Family Information - Father's Details -->
            <h3 class="application-form text-uppercase margin-bottom-10 margin-top">Part 2: Family Information</h3>
            <hr />
            <h4 class="text-left margin-top-bottom-10">--- Father or Guardian's Details ---</h4>
            
            <div class="grid margin">
              <div class="s-12 m-6 l-3">
                <?php echo form_label("<span class='margin-bottom-10'>Last Name</span>"); ?>
                <input name="lname_f" class="required" placeholder="Father's Last Name" title="Father's Last Name" type="text" required />
              </div>
              <div class="s-12 m-6 l-6">
                <?php echo form_label("<span class='margin-bottom-10'>First Name</span>"); ?>
                <input name="fname_f" class="required" placeholder="Father's First Name" title="Father's First Name" type="text" required />
              </div>
              <div class="s-12 m-8 l-3">
                <?php echo form_label("<span class='margin-bottom-10'>Nationality</span>"); ?>
                <select name="nationality_f" class="required border-radius" required>
                  <?php 
                  // Use the nationalities file (already included above)
                  echo get_nationality_options();
                  ?>
                </select>
              </div>
            </div>
            
            <div class="grid margin">
              <div class="s-12 m-4 l-4">
                <?php echo form_label("<span class='margin-bottom-10'>ID/Passport Number</span>"); ?>
                <input name="identity_f" class="required" placeholder="ID/Passport Number" title="Father's ID/Passport Number" type="text" required />
              </div>
              <div class="s-12 m-4 l-4">
                <?php echo form_label("<span class='margin-bottom-10'>Occupation</span>"); ?>
                <input name="occupation_f" class="required" placeholder="Occupation" title="Father's Occupation" type="text" required />
              </div>
              <div class="s-12 m-4 l-4">
                <?php echo form_label("<span class='margin-bottom-10'>Employer</span>"); ?>
                <input name="employer_f" class="required" placeholder="Employer" title="Father's Employer" type="text" required />
              </div>
            </div>
            
            <div class="grid margin">
              <div class="s-12 m-12 l-12">
                <?php echo form_label("<span class='margin-bottom-10'>Physical Address</span>"); ?>
                <input name="address_f" class="required" placeholder="Physical Address" title="Father's Physical Address" type="text" required />
              </div>
            </div>
            
            <div class="grid margin">
              <div class="s-12 m-7 l-8">
                <?php echo form_label("<span class='margin-bottom-10'>Postal Address</span>"); ?>
                <input name="address_postal_f" class="" placeholder="Postal Address" title="Father's Postal Address" type="text" />
              </div>
              <div class="s-12 m-5 l-4">
                <?php echo form_label("<span class='margin-bottom-10'>Email Address</span>"); ?>
                <input name="email_f" class="required email" placeholder="Email Address" title="Father's Email Address" type="email" required />
              </div>
            </div>
            
            <div class="grid margin">
              <div class="s-12 m-4 l-4">
                <?php echo form_label("<span class='margin-bottom-10'>Phone Number</span>"); ?>
                <input name="phone_f" class="required" placeholder="Phone Number" title="Father's Phone Number" type="text" required />
              </div>
              <div class="s-12 m-4 l-4">
                <?php echo form_label("<span class='margin-bottom-10'>Work Phone</span>"); ?>
                <input name="phone_work_f" class="" placeholder="Work Phone" title="Father's Work Phone" type="text" />
              </div>
              <div class="s-12 m-4 l-4">
                <?php echo form_label("<span class='margin-bottom-10'>Residence Status</span>"); ?>
                <select name="res_status_f" class="required border-radius" required>
                  <option value="">Select Status</option>
                  <option value="Permanent">Permanent</option>
                  <option value="Temporary">Temporary</option>
                </select>
              </div>
            </div>
            
            <!-- Part 2: Family Information - Mother's Details -->
            <h4 class="margin-top-bottom-10 text-left margin-bottom-10">--- Mother's Details ---</h4>
            
            <div class="grid margin">
              <div class="s-12 m-6 l-3">
                <?php echo form_label("<span class='margin-bottom-10'>Last Name</span>"); ?>
                <input name="lname_m" class="required" placeholder="Mother's Last Name" title="Mother's Last Name" type="text" required />
              </div>
              <div class="s-12 m-6 l-6">
                <?php echo form_label("<span class='margin-bottom-10'>First Name</span>"); ?>
                <input name="fname_m" class="required" placeholder="Mother's First Name" title="Mother's First Name" type="text" required />
              </div>
              <div class="s-12 m-8 l-3">
                <?php echo form_label("<span class='margin-bottom-10'>Nationality</span>"); ?>
                <select name="nationality_m" class="required border-radius" required>
                  <?php 
                  // Use the nationalities file (already included above)
                  echo get_nationality_options();
                  ?>
                </select>
              </div>
            </div>
            
            <div class="grid margin">
              <div class="s-12 m-4 l-4">
                <?php echo form_label("<span class='margin-bottom-10'>ID/Passport Number</span>"); ?>
                <input name="identity_m" class="required" placeholder="ID/Passport Number" title="Mother's ID/Passport Number" type="text" required />
              </div>
              <div class="s-12 m-4 l-4">
                <?php echo form_label("<span class='margin-bottom-10'>Occupation</span>"); ?>
                <input name="occupation_m" class="required" placeholder="Occupation" title="Mother's Occupation" type="text" required />
              </div>
              <div class="s-12 m-4 l-4">
                <?php echo form_label("<span class='margin-bottom-10'>Employer</span>"); ?>
                <input name="employer_m" class="required" placeholder="Employer" title="Mother's Employer" type="text" required />
              </div>
            </div>
            
            <div class="grid margin">
              <div class="s-12 m-12 l-12">
                <?php echo form_label("<span class='margin-bottom-10'>Physical Address</span>"); ?>
                <input name="address_m" class="required" placeholder="Physical Address" title="Mother's Physical Address" type="text" required />
              </div>
            </div>
            
            <div class="grid margin">
              <div class="s-12 m-7 l-8">
                <?php echo form_label("<span class='margin-bottom-10'>Postal Address</span>"); ?>
                <input name="address_postal_m" class="" placeholder="Postal Address" title="Mother's Postal Address" type="text" />
              </div>
              <div class="s-12 m-5 l-4">
                <?php echo form_label("<span class='margin-bottom-10'>Email Address</span>"); ?>
                <input name="email_m" class="required email" placeholder="Email Address" title="Mother's Email Address" type="email" required />
              </div>
            </div>
            
            <div class="grid margin">
              <div class="s-12 m-4 l-4">
                <?php echo form_label("<span class='margin-bottom-10'>Phone Number</span>"); ?>
                <input name="phone_m" class="required" placeholder="Phone Number" title="Mother's Phone Number" type="text" required />
              </div>
              <div class="s-12 m-4 l-4">
                <?php echo form_label("<span class='margin-bottom-10'>Work Phone</span>"); ?>
                <input name="phone_work_m" class="" placeholder="Work Phone" title="Mother's Work Phone" type="text" />
              </div>
              <div class="s-12 m-4 l-4">
                <?php echo form_label("<span class='margin-bottom-10'>Residence Status</span>"); ?>
                <select name="res_status_m" class="required border-radius" required>
                  <option value="">Select Status</option>
                  <option value="Permanent">Permanent</option>
                  <option value="Temporary">Temporary</option>
                </select>
              </div>
            </div>
            
            <!-- Part 3: Additional Information -->
            <h3 class="application-form text-uppercase margin-bottom-10 margin-top">Part 3: Additional Information</h3>
            <hr />
            
            <div class="grid margin">
              <div class="s-12 m-12 l-6">
                <?php echo form_label("<span class='margin-bottom-10'>Who will be responsible for paying fees?</span>"); ?>
                <select name="fees_payer" class="required border-radius" required>
                  <option value="">Select Fee Payer</option>
                  <option value="Father">Father</option>
                  <option value="Mother">Mother</option>
                  <option value="Both Parents">Both Parents</option>
                  <option value="Guardian">Guardian</option>
                  <option value="Other">Other</option>
                </select>
              </div>
            </div>
            
            <div class="grid margin">
              <div class="s-12 m-12 l-12">
                <?php echo form_label("<span class='margin-bottom-10'>Does the applicant have any siblings at the school?</span>"); ?>
                <textarea name="siblings" class="" placeholder="If yes, please provide their names and grades. If no, write 'None'" rows="3"></textarea>
              </div>
              <div class="s-12 m-12 l-12">
                <?php echo form_label("<span class='margin-bottom-10'>Would you like to be placed on the waiting list if there is no space available?</span>"); ?>
                <select name="waiting_list" class="required border-radius" required>
                  <option value="">Select Option</option>
                  <option value="Yes">Yes</option>
                  <option value="No">No</option>
                </select>
              </div>
              <div class="s-12 m-12 l-12">
                <?php echo form_label("<span class='margin-bottom-10'>Any additional information you would like to provide?</span>"); ?>
                <textarea name="more_info" class="" placeholder="Additional information (optional)" rows="3"></textarea>
              </div>
            </div>
            
            <!-- Part 4: Terms & Conditions -->
            <h3 class="application-form text-uppercase margin-bottom-10 margin-top">Part 4: Terms & Conditions</h3>
            <hr />
            
            <div class="grid margin">
              <div class="s-12 m-12 l-12">
                <p>By <span class='any-error'>Submitting</span> this electronic Application Form, you agree to the following: If your child is enrolled</p>
                <ul>
                  <li>You are responsible for paying all prescribed fees and deposits.</li>
                  <li>Your child shall be subject to the rules and disciplines of the school.</li>
                  <li>That if any detail of this application be found false, your child will immediately lose his/her place at the school.</li>
                </ul>
              </div>
            </div>
            
            <!-- reCAPTCHA -->
            <div class="grid margin">
              <div class="s-12 m-12 l-12">
                <?php
                if (isset($this->recaptcha)) {
                  echo $this->recaptcha->getWidget();
                  echo $this->recaptcha->getScriptTag();
                }
                ?>
              </div>
            </div>
            
            <!-- Submit Buttons -->
            <div class="grid margin">
              <div class="s-12 m-6 l-3">
                <button class="submit-form button submit-btn border-radius text-white background-blue" type="submit">Apply</button>
              </div>
              <div class="s-12 m-6 l-3">
                <button class="reset-form button cancel-btn border-radius text-white" type="reset">Reset</button>
              </div>
            </div>
            
            <?php echo form_close(); ?>
          </div>
        </div>
      </div>
    </div>
  </article>
</main>