<?php
/**
 * Template Name: Extracurricular Activities
 * Description: A full-width layout template showcasing the school's extracurricular activities.
 * Author: <PERSON>
 */

require_once('includes/header.php');

// Get extracurricular items from WordPress
$args = array(
    'post_type' => 'extracurricular_item',
    'posts_per_page' => -1,
    'orderby' => 'title',
    'order' => 'ASC',
    'meta_query' => array(
        array(
            'key' => 'archived',
            'value' => '1',
            'compare' => '!='
        )
    )
);

$extracurricular_query = new WP_Query($args);
$extracurricular_activities = array();

if ($extracurricular_query->have_posts()) {
    while ($extracurricular_query->have_posts()) {
        $extracurricular_query->the_post();
        
        // Get ACF fields
        $name = get_the_title();
        $excerpt = get_field('excerpt');
        $item_scope = "";
        $card_image = get_field('card_image');
        $page_image = get_field('page_image');
        $extracurricular_type_obj = get_field('extracurricular_type');
        
        // Format the extracurricular type
        $extracurricular_type = '';
        if (!empty($extracurricular_type_obj)) {
            if (is_array($extracurricular_type_obj) && isset($extracurricular_type_obj[0])) {
                // If it's returned as an array of objects
                $extracurricular_type = get_field('type', $extracurricular_type_obj[0]->ID);
            } elseif (is_object($extracurricular_type_obj)) {
                // If it's returned as a single object
                $extracurricular_type = get_field('type', $extracurricular_type_obj->ID);
            }
        }
        
        // Add to activities array
        $extracurricular_activities[] = array(
            'id' => get_the_ID(),
            'name' => $name,
            'card_image' => !empty($card_image) ? $card_image['url'] : '',
            'page_image' => !empty($page_image) ? $page_image['url'] : '',
            'activity_scope' => !empty($item_scope) ? ucfirst($item_scope) : '',
            'excerpt' => $excerpt,
            'extracurricular_type' => $extracurricular_type
        );
    }
    wp_reset_postdata();
}

// Get unique extracurricular types for filtering
$extracurricular_types = array();
foreach ($extracurricular_activities as $activity) {
    if (!empty($activity['extracurricular_type']) && !in_array($activity['extracurricular_type'], $extracurricular_types)) {
        $extracurricular_types[] = $activity['extracurricular_type'];
    }
}

// Get unique activity scopes for filtering
$activity_scopes = array();
foreach ($extracurricular_activities as $activity) {
    if (!empty($activity['activity_scope']) && !in_array($activity['activity_scope'], $activity_scopes)) {
        $activity_scopes[] = $activity['activity_scope'];
    }
}
?>

<!-- MAIN -->
<main role="main">
    <!-- Content -->
    <article>
        <div class="line">
            <header class="rounded-div section background-primary background-transparent text-center"
                data-image-src="<?php echo get_template_directory_uri(); ?>/assets/img/parallax-03.webp">
                <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">Extracurricular
                    Activities</h1>
            </header>
        </div>

        <!-- Breadcrumb Navigation -->
        <?php get_template_part('includes/breadcrumb'); ?>

        <div class="section background-white">
            <div class="line">
                <div class="s-12 m-12 l-12 margin-bottom-20">
                    <?php 
                        while (have_posts()) :
                            the_post();
                            the_content();
                        endwhile;
                        ?>
                </div>

                <!-- Combined Filter and Activities Section in a single rounded div -->
                <div class="s-12 m-12 l-12 margin-bottom-30">
                    <div class="background-primary-hightlight padding rounded-div box-shadow">
                        <?php if (!empty($extracurricular_types)): ?>
                        <!-- Filter Section -->
                        <div class="filter-section margin-bottom-30">
                            <h3 class="text-center margin-bottom-20">Filter Activities</h3>

                            <!-- Category Filter -->
                            <div class="s-12 m-12 l-12">
                                <p class="category-filter">By Extracurricular Category:</p>
                                <div class="blog-category-nav text-center">
                                    <a href="#" class="category-link active" data-filter="all">All Categories</a>
                                    <?php foreach ($extracurricular_types as $type): ?>
                                    <a href="#" class="category-link"
                                        data-filter="<?php echo htmlspecialchars($type); ?>"><?php echo htmlspecialchars($type); ?></a>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Extracurricular Activities Cards -->
                        <d3iv class="grid margin activities-container">
                            <?php if (empty($extracurricular_activities)): ?>
                            <div class="s-12 m-12 l-12 margin-m-bottom text-center">
                                <p>No extracurricular activities found. Please check back later.</p>
                            </div>
                            <?php else: ?>
                            <!-- No results message (hidden by default) -->
                            <div class="s-12 m-12 l-12 margin-m-bottom text-center no-results-message" style="display: none;">
                                <p>No activities match the selected filter. Please try another category.</p>
                            </div>
                            <?php foreach ($extracurricular_activities as $index => $activity): ?>
                            <div class="s-12 m-4 l-4 margin-m-bottom blog-post activity-category"
                                data-category="<?php echo htmlspecialchars($activity['extracurricular_type']); ?>"
                                data-scope="<?php echo htmlspecialchars($activity['activity_scope']); ?>">
                                <?php if (!empty($activity['activity_scope'])): ?>
                                <span
                                    class="gallery-label extracurricular-label"><?php echo htmlspecialchars($activity['activity_scope']); ?></span>
                                <?php endif; ?>
                        <a href="<?php echo esc_url(get_permalink($activity['id'])); ?>">
                            <?php if (!empty($activity['card_image'])): ?>
                            <img class="rounded-image-top blog-image"
                                src="<?php echo htmlspecialchars($activity['card_image']); ?>"
                                alt="<?php echo htmlspecialchars($activity['name']); ?>">
                            <?php else: ?>
                            <div class="rounded-image-top blog-image placeholder-image">
                                <span>No Image Available</span>
                            </div>
                            <?php endif; ?>
                        </a>
                        <div class="gallery-content">
                            <h3>
                                <a class="text-orange-hover"
                                    href="<?php echo esc_url(get_permalink($activity['id'])); ?>">
                                    <?php echo htmlspecialchars($activity['name']); ?>
                                </a>
                            </h3>
                            <div class="s-12 m-12 l-12">
                                <?php if (!empty($activity['excerpt'])): ?>
                                <p class="margin-bottom"><?php echo htmlspecialchars($activity['excerpt']); ?>
                                </p>
                                <?php endif; ?>
                                <?php if (!empty($activity['extracurricular_type'])): ?>
                                <p class="text-small"><strong>Category:</strong>
                                    <a href="#" class="category-link"
                                        data-filter="<?php echo htmlspecialchars($activity['extracurricular_type']); ?>">
                                        <?php echo htmlspecialchars($activity['extracurricular_type']); ?>
                                    </a>
                                </p>
                                <?php endif; ?>
                                <a class="text-more-info text-primary-hover margin-bottom-20 learn-more-link"
                                    href="<?php echo esc_url(get_permalink($activity['id'])); ?>">Learn More</a>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    <?php endif; ?>
                        </div>

                        <!-- Pagination - will be shown/hidden by JavaScript based on filtered results -->
                        <div class="line text-center pagination-container" <?php echo (count($extracurricular_activities) <= 9) ? 'style="display:none;"' : ''; ?>>
                            <div class="s-12 margin-bottom">
                                <ul class="pagination">
                                    <!-- Pagination links will be generated by JavaScript -->
                                    <li><a href="#" class="previous-page">Prev</a></li>
                                    <li><a href="#" class="active-page">1</a></li>
                                    <!-- Additional page links will be generated dynamically -->
                                    <li><a href="#" class="next-page">Next</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </article>
</main>

<!-- JavaScript for filtering and pagination of activities -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Category filter functionality
        const categoryLinks = document.querySelectorAll('.category-link');
        const activityItems = document.querySelectorAll('.activity-category');
        const itemsPerPage = 9; // Number of items to show per page
        let currentPage = 1;
        let currentCategory = 'all';

        // Only initialize if we have activities
        if (activityItems.length > 0) {
            // Function to filter activities
            function filterActivities() {
                let visibleCount = 0;
                let visibleItems = [];

                // First determine which items should be visible based on category
                activityItems.forEach(item => {
                    const itemCategory = item.getAttribute('data-category');

                    if (currentCategory === 'all' || itemCategory === currentCategory) {
                        visibleCount++;
                        visibleItems.push(item);
                        // Initially hide all items, we'll show them based on pagination
                        item.style.display = 'none';
                    } else {
                        item.style.display = 'none';
                    }
                });

                // Show "no results" message if no items are visible
                const noResultsMessage = document.querySelector('.no-results-message');
                if (noResultsMessage) {
                    if (visibleCount === 0) {
                        noResultsMessage.style.display = 'block';
                    } else {
                        noResultsMessage.style.display = 'none';
                    }
                }

                // Apply pagination to visible items
                applyPagination(visibleItems);
                
                // Update pagination UI
                updatePaginationUI(visibleItems.length);
            }

            // Function to apply pagination
            function applyPagination(visibleItems) {
                // Calculate start and end indices for current page
                const startIndex = (currentPage - 1) * itemsPerPage;
                const endIndex = Math.min(startIndex + itemsPerPage, visibleItems.length);
                
                // Show only items for current page
                for (let i = 0; i < visibleItems.length; i++) {
                    if (i >= startIndex && i < endIndex) {
                        visibleItems[i].style.display = 'block';
                    } else {
                        visibleItems[i].style.display = 'none';
                    }
                }
            }

            // Function to update pagination UI
            function updatePaginationUI(totalVisibleItems) {
                const paginationContainer = document.querySelector('.pagination-container');
                if (!paginationContainer) return;
                
                // Only show pagination if we have more items than the items per page
                if (totalVisibleItems > itemsPerPage) {
                    paginationContainer.style.display = 'block';
                    
                    // Calculate total pages
                    const totalPages = Math.ceil(totalVisibleItems / itemsPerPage);
                    
                    // Update pagination links
                    const paginationList = paginationContainer.querySelector('ul.pagination');
                    if (paginationList) {
                        // Clear existing page links (keep prev/next)
                        const prevLink = paginationList.querySelector('.previous-page');
                        const nextLink = paginationList.querySelector('.next-page');
                        
                        // Clear the list
                        paginationList.innerHTML = '';
                        
                        // Add prev link
                        const prevItem = document.createElement('li');
                        const prevAnchor = document.createElement('a');
                        prevAnchor.href = '#';
                        prevAnchor.className = 'previous-page';
                        prevAnchor.textContent = 'Prev';
                        prevAnchor.addEventListener('click', function(e) {
                            e.preventDefault();
                            if (currentPage > 1) {
                                currentPage--;
                                filterActivities();
                            }
                        });
                        prevItem.appendChild(prevAnchor);
                        paginationList.appendChild(prevItem);
                        
                        // Add page links
                        for (let i = 1; i <= totalPages; i++) {
                            const pageItem = document.createElement('li');
                            const pageAnchor = document.createElement('a');
                            pageAnchor.href = '#';
                            pageAnchor.textContent = i;
                            if (i === currentPage) {
                                pageAnchor.className = 'active-page';
                            }
                            
                            pageAnchor.addEventListener('click', function(e) {
                                e.preventDefault();
                                currentPage = i;
                                filterActivities();
                            });
                            
                            pageItem.appendChild(pageAnchor);
                            paginationList.appendChild(pageItem);
                        }
                        
                        // Add next link
                        const nextItem = document.createElement('li');
                        const nextAnchor = document.createElement('a');
                        nextAnchor.href = '#';
                        nextAnchor.className = 'next-page';
                        nextAnchor.textContent = 'Next';
                        nextAnchor.addEventListener('click', function(e) {
                            e.preventDefault();
                            if (currentPage < totalPages) {
                                currentPage++;
                                filterActivities();
                            }
                        });
                        nextItem.appendChild(nextAnchor);
                        paginationList.appendChild(nextItem);
                    }
                } else {
                    paginationContainer.style.display = 'none';
                }
            }

            // Category filter event listeners
            categoryLinks.forEach(link => {
                link.addEventListener('click', function (e) {
                    e.preventDefault();

                    // Remove active class from all category links
                    categoryLinks.forEach(l => l.classList.remove('active'));

                    // Add active class to clicked link
                    this.classList.add('active');

                    // Update current category and reset to first page
                    currentCategory = this.getAttribute('data-filter');
                    currentPage = 1;

                    // Filter activities
                    filterActivities();
                });
            });
            
            // Initialize filtering and pagination
            filterActivities();
        }
    });
</script>

<?php get_footer(); ?>