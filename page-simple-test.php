<?php
/**
 * Template Name: Simple Test
 * 
 * A very simple template for testing AJAX functionality.
 */

// Include the simple AJAX handler
require_once('simple-ajax-handler.php');

get_header();
?>

<div class="container">
    <div class="section background-white">
        <h1>Simple AJAX Test</h1>
        
        <div id="simple-test-result" style="margin: 20px 0; padding: 15px; border-radius: 4px;"></div>
        
        <form id="simple-test-form" style="margin-top: 20px;">
            <div class="grid margin">
                <div class="s-12 m-12 l-12">
                    <label for="simple-name">Your Name:</label>
                    <input type="text" id="simple-name" name="name" required>
                </div>
            </div>
            
            <div class="grid margin">
                <div class="s-12 m-12 l-12">
                    <label for="simple-message">Your Message:</label>
                    <textarea id="simple-message" name="message" rows="4" required></textarea>
                </div>
            </div>
            
            <div class="grid margin">
                <div class="s-12 m-12 l-12">
                    <button type="submit" class="button background-blue text-white">Send Test</button>
                </div>
            </div>
        </form>
        
        <div style="margin-top: 30px;">
            <h2>Direct AJAX Test</h2>
            <p>Click the button below to test AJAX directly without a form:</p>
            <button id="direct-test-button" class="button background-green text-white">Direct AJAX Test</button>
            <div id="direct-test-result" style="margin-top: 15px; padding: 15px; border-radius: 4px;"></div>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Direct AJAX test
    $('#direct-test-button').on('click', function() {
        $('#direct-test-result').html('<p>Sending direct test...</p>');
        
        $.ajax({
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            type: 'POST',
            data: {
                action: 'simple_test_action',
                nonce: '<?php echo wp_create_nonce('simple_test_nonce'); ?>',
                direct_test: 'This is a direct test'
            },
            success: function(response) {
                console.log('Direct test success:', response);
                
                if (response.success) {
                    $('#direct-test-result').html('<p style="color: green;">Direct test successful! Server response: ' + response.data.message + '</p>');
                } else {
                    $('#direct-test-result').html('<p style="color: red;">Direct test error: ' + response.data.message + '</p>');
                }
            },
            error: function(xhr, status, error) {
                console.log('Direct test error:', status, error);
                console.log('Response:', xhr.responseText);
                
                $('#direct-test-result').html('<p style="color: red;">Direct test AJAX error: ' + status + ' - ' + error + '</p><pre>' + xhr.responseText + '</pre>');
            }
        });
    });
});
</script>

<?php get_footer(); ?>