 <!-- Sidebar -->
 <div class="s-12 m-4 l-3">
   <aside>

   <?php
   // Get 2 most recently posted notifications (not archived)
   $notifications_args = array(
       'post_type' => 'notification',
       'posts_per_page' => 2,
       'orderby' => 'date',
       'order' => 'DESC',
       'post_status' => 'publish',
       'meta_query' => array(
           'relation' => 'OR',
           array(
               'key' => 'archived',
               'value' => '1',
               'compare' => '!='
           ),
           array(
               'key' => 'archived',
               'compare' => 'NOT EXISTS'
           )
       )
   );
   $notifications_query = new WP_Query($notifications_args);
   
   if ($notifications_query->have_posts()) : ?>
   <h5 class="text-uppercase margin-top-30 margin-bottom-20 text-strong">Latest Notifications</h5>
   <div class="margin">
       <div class="notification-card margin-bottom-20" style="border-radius: 8px; padding: 15px; border: 1px solid #e0e0e0;">
           <?php $post_count = 0; ?>
           <?php while ($notifications_query->have_posts()) : $notifications_query->the_post(); ?>
               <?php if ($post_count > 0) : ?>
                   <hr style="border: none; border-top: 1px solid #ccc; margin: 10px 0; padding: 0;">
               <?php endif; ?>
               <div class="notification-item">
                   <h6 class="margin-bottom-5">
                       <a href="<?php echo get_permalink(); ?>" class="text-primary">
                           <?php echo get_the_title(); ?>
                       </a>
                   </h6>
                   <small class="text-size-12 text-dark">
                       Posted: <?php echo get_the_date('M j, Y'); ?>
                   </small>
               </div>
               <?php $post_count++; ?>
           <?php endwhile; ?>
           <hr style="border: none; border-top: 1px solid #ccc; margin: 10px 0; padding: 0;">
           <div class="text-center">
               <a href="<?php echo get_permalink(426); ?>" class="next-step button rounded-full-btn text-white background-blue">
                   View All Notifications
               </a>
           </div>
       </div>
   </div>
   <?php endif; wp_reset_postdata(); ?>

   <?php
   // Get 2 rapidly approaching upcoming events (closest to due date)
   $today = date('d-m-y');
   
   $events_args = array(
       'post_type' => 'upcoming_event',
       'posts_per_page' => 2,
       'meta_key' => 'due_date',
       'orderby' => 'meta_value',
       'order' => 'ASC',
       'post_status' => 'publish',
       'meta_query' => array(
           'relation' => 'AND',
           array(
               'key' => 'due_date',
               'value' => $today,
               'compare' => '>='
           ),
           array(
               'relation' => 'OR',
               array(
                   'key' => 'archived',
                   'value' => '1',
                   'compare' => '!='
               ),
               array(
                   'key' => 'archived',
                   'compare' => 'NOT EXISTS'
               )
           )
       )
   );
   $events_query = new WP_Query($events_args);
   
   if ($events_query->have_posts()) : ?>
   <h5 class="text-uppercase margin-top-30 margin-bottom-20 text-strong">Upcoming Events</h5>
   <div class="margin">
       <div class="event-card margin-bottom-20" style="border-radius: 8px; padding: 15px; border: 1px solid #e0e0e0;">
           <?php $post_count = 0; ?>
           <?php while ($events_query->have_posts()) : $events_query->the_post(); 
               $event_name = get_field('event_name');
               $due_date = get_field('due_date');
               $event_excerpt = get_field('event_excerpt');
           ?>
               <?php if ($post_count > 0) : ?>
                   <hr style="border: none; border-top: 1px solid #ccc; margin: 10px 0; padding: 0;">
               <?php endif; ?>
               <div class="event-item">
                   <h6 class="margin-bottom-5">
                       <a href="<?php echo get_permalink(); ?>" class="text-primary">
                           <?php echo $event_name ? esc_html($event_name) : get_the_title(); ?>
                       </a>
                   </h6>
                   <?php if ($due_date) : ?>
                   <small class="text-size-12 text-dark">
                       Due Date: <?php echo esc_html($due_date); ?>
                   </small>
                   <?php endif; ?>
               </div>
               <?php $post_count++; ?>
           <?php endwhile; ?>
           <hr style="border: none; border-top: 1px solid #ccc; margin: 10px 0; padding: 0;">
           <div class="text-center">
               <a href="<?php echo get_permalink(434); ?>" class="next-step button rounded-full-btn text-white background-blue">
                   View All Events
               </a>
           </div>
       </div>
   </div>
   <?php endif; wp_reset_postdata(); ?>

   <?php
   // Get 2 newest posted blog posts (not archived)
   $blog_args = array(
       'post_type' => 'post',
       'posts_per_page' => 2,
       'orderby' => 'date',
       'order' => 'DESC',
       'post_status' => 'publish',
       'meta_query' => array(
           'relation' => 'OR',
           array(
               'key' => 'archived',
               'value' => '1',
               'compare' => '!='
           ),
           array(
               'key' => 'archived',
               'compare' => 'NOT EXISTS'
           )
       )
   );
   $blog_query = new WP_Query($blog_args);
   
   if ($blog_query->have_posts()) : ?>
   <h5 class="text-uppercase margin-top-30 margin-bottom-20 text-strong">Latest Blog Posts</h5>
   <div class="margin">
       <div class="blog-card margin-bottom-20" style="border-radius: 8px; padding: 15px; border: 1px solid #e0e0e0;">
           <?php $post_count = 0; ?>
           <?php while ($blog_query->have_posts()) : $blog_query->the_post(); 
               $categories = get_the_category();
           ?>
               <?php if ($post_count > 0) : ?>
                   <hr style="border: none; border-top: 1px solid #ccc; margin: 10px 0; padding: 0;">
               <?php endif; ?>
               <div class="blog-item">
                   <h6 class="margin-bottom-5">
                       <a href="<?php echo get_permalink(); ?>" class="text-primary">
                           <?php echo get_the_title(); ?>
                       </a>
                   </h6>
                   <small class="text-size-12 text-dark">
                       Posted: <?php echo get_the_date('M j, Y'); ?>
                   </small>
                   <?php if (!empty($categories)) : ?>
                   <div class="text-size-12 text-dark margin-top-5">
                       Category: <?php echo esc_html($categories[0]->name); ?>
                   </div>
                   <?php endif; ?>
               </div>
               <?php $post_count++; ?>
           <?php endwhile; ?>
           <hr style="border: none; border-top: 1px solid #ccc; margin: 10px 0; padding: 0;">
           <div class="text-center">
               <a href="<?php echo esc_url(home_url('/blog/')); ?>" class="next-step button rounded-full-btn text-white background-blue">
                   View All Posts
               </a>
           </div>
       </div>
   </div>
   <?php endif; wp_reset_postdata(); ?>

   </aside>
 </div>