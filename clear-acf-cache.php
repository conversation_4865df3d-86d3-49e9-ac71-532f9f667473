<?php
/**
 * Clear ACF Field Cache
 * 
 * This script clears the ACF field cache to ensure new fields are visible in the admin.
 * To use: Visit this file in your browser after adding it to your theme directory.
 */

// Define the WordPress content directory
define('WP_CONTENT_DIR', dirname(dirname(dirname(__FILE__))));

// Load WordPress
require_once(dirname(WP_CONTENT_DIR) . '/wp-load.php');

// Check if user is logged in and is an administrator
if (!is_user_logged_in() || !current_user_can('manage_options')) {
    wp_die('You do not have permission to access this page.');
}

// Clear ACF field cache
if (function_exists('acf_get_field_groups')) {
    // Get all field groups
    $field_groups = acf_get_field_groups();
    
    // Loop through field groups and delete cache
    foreach ($field_groups as $field_group) {
        wp_cache_delete("acf_get_field_group_style_{$field_group['key']}", 'acf');
        wp_cache_delete("acf_get_field_group_fields_{$field_group['key']}", 'acf');
    }
    
    // Clear general ACF caches
    wp_cache_delete('acf_get_field_groups', 'acf');
    wp_cache_delete('acf_get_valid_field_group_cache', 'acf');
    
    echo '<div style="background-color: #dff0d8; color: #3c763d; padding: 15px; border: 1px solid #d6e9c6; border-radius: 4px; margin: 20px 0;">
        <h2>Success!</h2>
        <p>ACF field cache has been cleared successfully. The new archive field should now be visible when editing blog posts.</p>
        <p><a href="' . admin_url('edit.php') . '">Go to Posts</a></p>
    </div>';
} else {
    echo '<div style="background-color: #f2dede; color: #a94442; padding: 15px; border: 1px solid #ebccd1; border-radius: 4px; margin: 20px 0;">
        <h2>Error</h2>
        <p>ACF does not appear to be active on this site.</p>
    </div>';
}