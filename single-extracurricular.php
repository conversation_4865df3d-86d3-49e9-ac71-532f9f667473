<?php
/**
 * The template for displaying single extracurricular posts
 * @package Nkhwazi_Primary_School
 */

get_header();

// Get the current post
global $post;
?>

<!-- MAIN -->
<main role="main">
  <!-- Header Section -->
  <article>
    <div class="line">
      <header class="rounded-div section background-primary background-transparent text-center"
        data-image-src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/parallax-03.webp">
        <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1"><?php the_title(); ?></h1>
      </header>
    </div>

    <?php if (have_posts()) : while (have_posts()) : the_post(); ?>

    <!-- Optional Page Image -->
    <?php 
    $page_image = get_field('page_image');
    if ($page_image) : ?>
    <div class="line margin-top-20">
      <div class="s-12">
        <img src="<?php echo esc_url($page_image['url']); ?>" alt="<?php echo esc_attr(get_the_title()); ?> Page Image"
          class="full-width-img rounded-image extracurricular-page-image">
      </div>
    </div>
    <?php endif; ?>

    <!-- Featured Image (if no page image is set) -->
    <?php if (!$page_image && has_post_thumbnail()) : ?>
    <div class="line margin-top-30 margin-bottom-30">
      <div class="s-12">
        <?php the_post_thumbnail('full', array('class' => 'full-width-img rounded-image', 'alt' => get_the_title())); ?>
      </div>
    </div>
    <?php endif; ?>

    <!-- Main Content Section -->
    <div class="section background-white">
      <div class="line">
        <div class="grid margin">
          <!-- Left Column: Extracurricular Details -->
          <div class="s-12 m-6 l-7">
            <?php 
            // Display additional fields if they exist
            $type = get_field('type');
            $excerpt = get_field('excerpt');
            
            if ($type || $excerpt) :
            ?>
            <div class="extracurricular-summary">
              <?php if ($type) : ?>
              <p><strong>Extracurricular Type:</strong> <?php echo esc_html($type); ?></p>
              <?php endif; ?>

              <?php if ($excerpt) : ?>
              <p><strong>Summary:</strong> <?php echo esc_html($excerpt); ?></p>
              <?php endif; ?>
            </div>
            <?php endif; ?>
            <?php 
            // Display the main content
            the_content(); 
            ?>
          </div>

          <!-- Right Column: Associated Activities -->
          <div class="s-12 m-6 l-5">
            <?php
            // Query for extracurricular_item posts that are related to this extracurricular
            $args = array(
                'post_type' => 'extracurricular_item',
                'posts_per_page' => 4,
                'meta_query' => array(
                    array(
                        'key' => 'extracurricular_type', // Field that links to the parent extracurricular
                        'value' => '"' . get_the_ID() . '"', // ACF relationship fields store IDs as serialized strings
                        'compare' => 'LIKE'
                    )
                )
            );
            $related_query = new WP_Query($args);
            
            // Debug output - can be removed after testing
            if (current_user_can('administrator')) {
                echo '<!-- Debug: Found ' . $related_query->found_posts . ' related activities -->';
            }
            
            // Always show the heading
            ?>
            <h2 class="text-primary margin-bottom-20 text-center text-strong text-uppercase">Associated Activities</h2>

            <?php
            // Check if we have related activities
            if ($related_query->have_posts()) : 
                $related_activities = $related_query->posts;
            ?>
            <!-- Activities List with Links to Single Activity Pages -->
            <div class="activities-list rounded-div background-primary-hightlight box-shadow padding-10">
              <?php 
              $count = 0;
              $total_posts = $related_query->post_count;
              
              while ($related_query->have_posts()) : $related_query->the_post(); 
                $count++;
                // Get the activity data
                $activity_id = get_the_ID();
                $activity_title = get_the_title();
                $activity_name = get_field('name', $activity_id);
                $activity_excerpt = get_field('excerpt', $activity_id);
                $activity_date = get_field('date', $activity_id);
                $card_image = get_field('card_image', $activity_id);
              ?>
              <!-- Activity Card (Clickable) -->
              <a href="<?php echo esc_url(get_permalink($activity_id)); ?>" class="activity-link">
                <div class="grid margin">
                  <div class="s-5 m-5 l-5">
                    <?php if ($card_image) : ?>
                    <img src="<?php echo esc_url($card_image['url']); ?>"
                      alt="<?php echo esc_attr($activity_name ? $activity_name : $activity_title); ?>"
                      class="activity-thumbnail">
                    <?php else : ?>
                    <img src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/placeholder.jpg"
                      alt="<?php echo esc_attr($activity_name ? $activity_name : $activity_title); ?>"
                      class="activity-thumbnail">
                    <?php endif; ?>
                  </div>
                  <div class="s-7 m-7 l-7">
                    <h4 class="text-strong"><?php echo esc_html($activity_name ? $activity_name : $activity_title); ?>
                    </h4>
                    <?php if ($activity_date) : ?>
                    <p class="text-size-12 text-grey margin-bottom-5"><?php echo esc_html($activity_date); ?></p>
                    <?php endif; ?>
                    <?php if ($activity_excerpt) : ?>
                    <p class="text-size-12 text-grey margin-bottom-5">
                      <?php echo wp_trim_words($activity_excerpt, 10); ?></p>
                    <?php endif; ?>
                    <span class="text-size-12 m-12 l-12 text-primary">Click to view details</span>
                  </div>
                </div>
              </a>

              <?php if ($count < $total_posts) : ?>
              <hr class="activity-separator">
              <?php endif; ?>

              <?php endwhile; 
              wp_reset_postdata(); // Reset the post data to the main query
              ?>

              <?php 
              // If there are more activities than we're showing, display a "View All" link
              if ($related_query->found_posts > 4) : 
                // Try to find a page with the extracurricular-activities template
                $pages = get_pages(array(
                  'meta_key' => '_wp_page_template',
                  'meta_value' => 'page-extracurricular-activities.php'
                ));
                
                $activities_url = '';
                if (!empty($pages)) {
                  $activities_url = get_permalink($pages[0]->ID);
                } else {
                  // Fallback to the home page
                  $activities_url = home_url();
                }
              ?>
              <div class="text-center margin-top-30">
                <a href="<?php echo esc_url($activities_url); ?>"
                  class="button background-primary text-white text-strong">View All Activities</a>
              </div>
              <?php endif; ?>
            </div>
            <?php else: ?>
            <!-- No Activities Message -->
            <div class="no-activities-message">

              <div class="background-grey-light padding rounded-div text-center">
                <i class="icon-info-circled text-size-30 text-primary margin-bottom-10 display-block"></i>
                <p class="text-strong">No activities have been added to this extracurricular category yet.</p>
                <p class="margin-bottom-15">Check back later for updates or explore other extracurricular categories.
                </p>

                <?php
                // Try to find a page with the extracurricular-activities template
                $pages = get_pages(array(
                  'meta_key' => '_wp_page_template',
                  'meta_value' => 'page-extracurricular-activities.php'
                ));
                
                $activities_url = '';
                if (!empty($pages)) {
                  $activities_url = get_permalink($pages[0]->ID);
                } else {
                  // Fallback to the extracurricular page ID
                  $activities_url = get_permalink(335);
                }
                ?>

                <div class="line text-center margin-bottom-10">
                  <?php
        // Get the URL of the extracurricular category page using its ID
        $extracurricular_page_id = 335; // Specific ID for the extracurricular page
        $back_url = get_permalink($extracurricular_page_id);
        ?>
                  <a href="<?php echo esc_url($back_url); ?>" class="button background-primary text-white text-strong">
                    Explore Other Extracurricular Categories
                  </a>
                </div>
              </div>

            </div>
            <?php endif; ?>
          </div>
        </div>
      </div>
    </div>

    <?php endwhile; endif; ?>

    <!-- Back to Extracurricular Categories Button -->
    <div class="section background-white">
      <div class="line text-center margin-bottom-10">
        <?php
        // Get the URL of the extracurricular category page using its ID
        $extracurricular_page_id = 335; // Specific ID for the extracurricular page
        $back_url = get_permalink($extracurricular_page_id);
        ?>
        <a href="<?php echo esc_url($back_url); ?>" class="button background-primary text-white text-strong">
          Back to Extracurricular Categories
        </a>
      </div>
    </div>

  </article>
</main>

<!-- JavaScript no longer needed as we're using direct links -->

<?php get_footer(); ?>