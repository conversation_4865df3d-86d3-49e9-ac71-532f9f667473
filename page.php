<?php
/**
 * The template for displaying all pages
 *
 * This is the template that displays all pages by default.
 * Please note that this is the WordPress construct of pages
 * and that other 'pages' on your WordPress site may use a
 * different template.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package Nkhwazi_Primary_School
 */

get_header();
?>

<!-- MAIN -->
<main role="main">
  <!-- Content -->
  <article>
    <div class="line">
      <header class="rounded-div section background-primary background-transparent text-center"
        data-image-src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/parallax-02.webp">
        <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1"><?php the_title(); ?></h1>
      </header>
    </div>

    <!-- Breadcrumb Navigation -->
    <?php get_template_part('includes/breadcrumb'); ?>

    <div class="section background-white">
      <div class="line">
        <div class="margin">
          <div class="s-12 m-12 l-12">
            <?php
            while (have_posts()) :
                the_post();
                
                the_content();
                
                // If comments are open or we have at least one comment, load up the comment template.
                if (comments_open() || get_comments_number()) :
                    comments_template();
                endif;
                
            endwhile; // End of the loop.
            ?>
          </div>
        </div>
      </div>
    </div>
  </article>
</main>

<?php get_footer(); ?>