/**
 * Lightcase - j<PERSON><PERSON>y Plugin
 * The smart and flexible Lightbox Plugin.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright	Author
 *
 * @version		2.3.4 (29/12/2015)
 */
/**
 * Lightcase settings
 *
 * Note: Override default settings for your project without touching this source code by simply
 * defining those variables within a SASS map called '$lightcase-custom'.
 *
 * // Example usage
 * $lightcase-custom: (
 *   'breakpoint': 768px
 * );
 */
/* line 12, ../scss/components/fonts/_font-lightcase.scss */
[class*='lightcase-icon-']:before {
  font-family: 'mfg', sans-serif;
  font-style: normal;
  font-weight: normal;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  text-align: center;
  /* For safety - reset parent styles, that can break glyph codes*/
  font-variant: normal;
  text-transform: none;
  /* fix buttons height, for twitter bootstrap */
  line-height: 1em;
  /* Uncomment for 3D effect */
  /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */
}

/* Codes */
/* line 35, ../scss/components/fonts/_font-lightcase.scss */
.lightcase-icon-play:before {
  content: '\f00f';
}

/* line 36, ../scss/components/fonts/_font-lightcase.scss */
.lightcase-icon-pause:before {
  content: '\f00e';
}

/* line 37, ../scss/components/fonts/_font-lightcase.scss */
.lightcase-icon-close:before {
  content: '\274C';
}

/* line 38, ../scss/components/fonts/_font-lightcase.scss */
.lightcase-icon-prev:before {
  content: '\f007';
}

/* line 39, ../scss/components/fonts/_font-lightcase.scss */
.lightcase-icon-next:before {
  content: '\f006';
}

/* line 40, ../scss/components/fonts/_font-lightcase.scss */
.lightcase-icon-spin:before {
  content: '\F528';
}

/**
 * Mixin providing icon defaults to be used on top of font-lightcase.
 *
 * Example usage:
 * @include icon(#e9e9e9)
 */
/**
 * Mixin providing icon defaults including a hover status to be used
 * on top of font-lightcase.
 *
 * Example usage:
 * @include icon-hover(#e9e9e9, #fff)
 */
/**
 * Provides natural content overflow behavior and scrolling support
 * even so for touch devices.
 *
 * Example usage:
 * @include overflow()
 */
/**
 * Neutralizes/resets dimensions including width, height, position as well as margins,
 * paddings and styles. Used to enforce a neutral and unstyled look and behavoir!
 *
 * Example usage:
 * @include clear(true)
 *
 * @param boolean $important
 */
@-webkit-keyframes lightcase-spin {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@-moz-keyframes lightcase-spin {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@-o-keyframes lightcase-spin {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@-ms-keyframes lightcase-spin {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@keyframes lightcase-spin {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
/* line 1, ../scss/components/modules/_case.scss */
#lightcase-case {
  display: none;
  position: fixed;
  z-index: 2002;
  top: 50%;
  left: 50%;
  font-family: arial, sans-serif;
  font-size: 13px;
  line-height: 1.5;
  text-align: left;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}
#lightcase-content .lightcase-contentInner {
    height: auto !important;
}
@media screen and (max-width: 640px) {
  /* line 16, ../scss/components/modules/_case.scss */
  #lightcase-case:not([data-lc-type=image]):not([data-lc-type=video]):not([data-lc-type=flash]):not([data-lc-type=error]) {
    position: relative !important;
    top: auto !important;
    left: auto !important;
    width: auto !important;
    height: auto !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    background: none !important;
  }
}

@media screen and (min-width: 641px) {
  /* line 4, ../scss/components/modules/_content.scss */
  #lightcase-case:not([data-lc-type=error]) #lightcase-content {
    position: relative;
    z-index: 1;
    overflow: hidden;
    text-shadow: none;
    background-color: #fff;
    -webkit-box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
    -moz-box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
    -o-box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
    -webkit-backface-visibility: hidden;
  }
}
@media screen and (min-width: 641px) {
  /* line 24, ../scss/components/modules/_content.scss */
  #lightcase-case[data-lc-type=image] #lightcase-content, #lightcase-case[data-lc-type=video] #lightcase-content {
    background-color: #333;
  }
}
/* line 32, ../scss/components/modules/_content.scss */
#lightcase-case:not([data-lc-type=image]):not([data-lc-type=video]):not([data-lc-type=flash]):not([data-lc-type=error]) #lightcase-content {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  -o-box-shadow: none;
  box-shadow: none;
}
@media screen and (max-width: 640px) {
  /* line 32, ../scss/components/modules/_content.scss */
  #lightcase-case:not([data-lc-type=image]):not([data-lc-type=video]):not([data-lc-type=flash]):not([data-lc-type=error]) #lightcase-content {
    position: relative !important;
    top: auto !important;
    left: auto !important;
    width: auto !important;
    height: auto !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    background: none !important;
  }
}
/* line 42, ../scss/components/modules/_content.scss */
#lightcase-case:not([data-lc-type=image]):not([data-lc-type=video]):not([data-lc-type=flash]):not([data-lc-type=error]) #lightcase-content .lightcase-contentInner {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}
@media screen and (max-width: 640px) {
  /* line 42, ../scss/components/modules/_content.scss */
  #lightcase-case:not([data-lc-type=image]):not([data-lc-type=video]):not([data-lc-type=flash]):not([data-lc-type=error]) #lightcase-content .lightcase-contentInner {
    padding: 15px;
  }
  /* line 51, ../scss/components/modules/_content.scss */
  #lightcase-case:not([data-lc-type=image]):not([data-lc-type=video]):not([data-lc-type=flash]):not([data-lc-type=error]) #lightcase-content .lightcase-contentInner, #lightcase-case:not([data-lc-type=image]):not([data-lc-type=video]):not([data-lc-type=flash]):not([data-lc-type=error]) #lightcase-content .lightcase-contentInner > * {
    width: 100% !important;
    max-width: none !important;
  }
  /* line 58, ../scss/components/modules/_content.scss */
  #lightcase-case:not([data-lc-type=image]):not([data-lc-type=video]):not([data-lc-type=flash]):not([data-lc-type=error]) #lightcase-content .lightcase-contentInner > *:not(iframe) {
    height: auto !important;
    max-height: none !important;
  }
}
@media screen and (max-width: 640px) {
  /* line 69, ../scss/components/modules/_content.scss */
  .lightcase-isMobileDevice #lightcase-case[data-lc-type=iframe] #lightcase-content .lightcase-contentInner {
    overflow: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media screen and (max-width: 640px) and (min-width: 641px) {
  /* line 73, ../scss/components/modules/_content.scss */
  [data-lc-type=image] #lightcase-content .lightcase-contentInner, [data-lc-type=video] #lightcase-content .lightcase-contentInner {
    line-height: 0.75;
  }
}

/* line 85, ../scss/components/modules/_content.scss */
.lightcase-isMobileDevice #lightcase-case[data-lc-type=iframe] #lightcase-content .lightcase-contentInner .lightcase-inlineWrap {
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}
@media screen and (max-width: 640px) {
  /* line 90, ../scss/components/modules/_content.scss */
  #lightcase-case:not([data-lc-type=image]):not([data-lc-type=video]):not([data-lc-type=flash]):not([data-lc-type=error]) #lightcase-content .lightcase-contentInner .lightcase-inlineWrap {
    position: relative !important;
    top: auto !important;
    left: auto !important;
    width: auto !important;
    height: auto !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    background: none !important;
  }
}
@media screen and (min-width: 641px) {
  /* line 97, ../scss/components/modules/_content.scss */
  #lightcase-case:not([data-lc-type=error]) #lightcase-content .lightcase-contentInner .lightcase-inlineWrap {
    padding: 30px;
    overflow: auto;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -o-box-sizing: border-box;
    box-sizing: border-box;
  }
}
@media screen and (max-width: 640px) {
  /* line 114, ../scss/components/modules/_content.scss */
  #lightcase-content h1, #lightcase-content h2, #lightcase-content h3, #lightcase-content h4, #lightcase-content h5, #lightcase-content h6, #lightcase-content p {
    color: #aaa;
  }
}
@media screen and (min-width: 641px) {
  /* line 114, ../scss/components/modules/_content.scss */
  #lightcase-content h1, #lightcase-content h2, #lightcase-content h3, #lightcase-content h4, #lightcase-content h5, #lightcase-content h6, #lightcase-content p {
    color: #333;
  }
}

/* line 3, ../scss/components/modules/_error.scss */
#lightcase-case p.lightcase-error {
  margin: 0;
  font-size: 17px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #aaa;
}
@media screen and (max-width: 640px) {
  /* line 3, ../scss/components/modules/_error.scss */
  #lightcase-case p.lightcase-error {
    padding: 30px 0;
  }
}
@media screen and (min-width: 641px) {
  /* line 3, ../scss/components/modules/_error.scss */
  #lightcase-case p.lightcase-error {
    padding: 0;
  }
}

/* line 6, ../scss/components/modules/_global.scss */
.lightcase-isMobileDevice .lightcase-open body {
  max-width: 100%;
  max-height: 100%;
  overflow: hidden;
}
@media screen and (max-width: 640px) {
  /* line 4, ../scss/components/modules/_global.scss */
  .lightcase-open body {
    padding: 55px 0 70px 0;
  }
  /* line 15, ../scss/components/modules/_global.scss */
  .lightcase-open body > *:not([id*='lightcase-']) {
    position: fixed !important;
    top: -9999px !important;
    width: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
  }
}

/* line 1, ../scss/components/modules/_info.scss */
#lightcase-info {
  position: absolute;
  padding-top: 15px;
}
/* line 9, ../scss/components/modules/_info.scss */
#lightcase-info #lightcase-title,
#lightcase-info #lightcase-caption {
  margin: 0;
  padding: 0;
  line-height: 1.5;
  font-weight: normal;
  text-overflow: ellipsis;
}
/* line 19, ../scss/components/modules/_info.scss */
#lightcase-info #lightcase-title {
  font-size: 17px;
  color: #aaa;
}
@media screen and (max-width: 640px) {
  /* line 19, ../scss/components/modules/_info.scss */
  #lightcase-info #lightcase-title {
    position: fixed;
    top: 10px;
    left: 0;
    max-width: 87.5%;
    padding: 5px 15px;
    background: #333;
  }
}
/* line 33, ../scss/components/modules/_info.scss */
#lightcase-info #lightcase-caption {
  clear: both;
  font-size: 13px;
  color: #aaa;
}
/* line 39, ../scss/components/modules/_info.scss */
#lightcase-info #lightcase-sequenceInfo {
  font-size: 11px;
  color: #aaa;
}
@media screen and (max-width: 640px) {
  /* line 45, ../scss/components/modules/_info.scss */
  .lightcase-fullScreenMode #lightcase-info {
    padding-left: 15px;
    padding-right: 15px;
  }
  /* line 51, ../scss/components/modules/_info.scss */
  #lightcase-case:not([data-lc-type=image]):not([data-lc-type=video]):not([data-lc-type=flash]):not([data-lc-type=error]) #lightcase-info {
    position: static;
  }
}

/* line 1, ../scss/components/modules/_loading.scss */
#lightcase-loading {
  position: fixed;
  z-index: 9999;
  width: 1.123em;
  height: auto;
  font-size: 38px;
  line-height: 1;
  text-align: center;
  text-shadow: none;
  position: fixed;
  z-index: 2001;
  top: 50%;
  left: 50%;
  margin-top: -0.5em;
  margin-left: -0.5em;
  opacity: 1;
  font-size: 32px;
  text-shadow: 0 0 15px #fff;
  -moz-transform-origin: 50% 53%;
  -webkit-animation: lightcase-spin 0.5s infinite linear;
  -moz-animation: lightcase-spin 0.5s infinite linear;
  -o-animation: lightcase-spin 0.5s infinite linear;
  animation: lightcase-spin 0.5s infinite linear;
}
/* line 20, ../scss/components/mixins/_presets.scss */
#lightcase-loading, #lightcase-loading:focus {
  text-decoration: none;
  color: #fff;
  -webkit-tap-highlight-color: transparent;
  -webkit-transition: color, opacity, ease-in-out 0.25s;
  -moz-transition: color, opacity, ease-in-out 0.25s;
  -o-transition: color, opacity, ease-in-out 0.25s;
  transition: color, opacity, ease-in-out 0.25s;
}
/* line 32, ../scss/components/mixins/_presets.scss */
#lightcase-loading > span {
  display: inline-block;
  text-indent: -9999px;
}

/* line 4, ../scss/components/modules/_navigation.scss */
#lightcase-nav a[class*='lightcase-icon-'] {
  position: fixed;
  z-index: 9999;
  width: 1.123em;
  height: auto;
  font-size: 38px;
  line-height: 1;
  text-align: center;
  text-shadow: none;
  outline: none;
  cursor: pointer;
}
/* line 20, ../scss/components/mixins/_presets.scss */
#lightcase-nav a[class*='lightcase-icon-'], #lightcase-nav a[class*='lightcase-icon-']:focus {
  text-decoration: none;
  color: rgba(255, 255, 255, 0.6);
  -webkit-tap-highlight-color: transparent;
  -webkit-transition: color, opacity, ease-in-out 0.25s;
  -moz-transition: color, opacity, ease-in-out 0.25s;
  -o-transition: color, opacity, ease-in-out 0.25s;
  transition: color, opacity, ease-in-out 0.25s;
}
/* line 32, ../scss/components/mixins/_presets.scss */
#lightcase-nav a[class*='lightcase-icon-'] > span {
  display: inline-block;
  text-indent: -9999px;
}
/* line 49, ../scss/components/mixins/_presets.scss */
#lightcase-nav a[class*='lightcase-icon-']:hover {
  color: white;
  text-shadow: 0 0 15px white;
}
/* line 12, ../scss/components/modules/_navigation.scss */
.lightcase-isMobileDevice #lightcase-nav a[class*='lightcase-icon-']:hover {
  color: #aaa;
  text-shadow: none;
}
/* line 19, ../scss/components/modules/_navigation.scss */
#lightcase-nav a[class*='lightcase-icon-'].lightcase-icon-prev {
  left: 15px;
}
/* line 24, ../scss/components/modules/_navigation.scss */
#lightcase-nav a[class*='lightcase-icon-'].lightcase-icon-next {
  right: 15px;
}
/* line 29, ../scss/components/modules/_navigation.scss */
#lightcase-nav a[class*='lightcase-icon-'].lightcase-icon-pause, #lightcase-nav a[class*='lightcase-icon-'].lightcase-icon-play {
  left: 50%;
  margin-left: -0.5em;
}
@media screen and (min-width: 641px) {
  /* line 29, ../scss/components/modules/_navigation.scss */
  #lightcase-nav a[class*='lightcase-icon-'].lightcase-icon-pause, #lightcase-nav a[class*='lightcase-icon-'].lightcase-icon-play {
    opacity: 0;
  }
}
/* line 40, ../scss/components/modules/_navigation.scss */
#lightcase-nav a[class*='lightcase-icon-'].lightcase-icon-close {
  top: 15px;
  right: 15px;
  bottom: auto;
  margin: 0;
  opacity: 0;
  outline: none;
}
@media screen and (max-width: 640px) {
  /* line 40, ../scss/components/modules/_navigation.scss */
  #lightcase-nav a[class*='lightcase-icon-'].lightcase-icon-close {
    position: absolute;
  }
}
@media screen and (min-width: 641px) {
  /* line 40, ../scss/components/modules/_navigation.scss */
  #lightcase-nav a[class*='lightcase-icon-'].lightcase-icon-close {
    position: fixed;
  }
}
@media screen and (max-width: 640px) {
  /* line 4, ../scss/components/modules/_navigation.scss */
  #lightcase-nav a[class*='lightcase-icon-'] {
    bottom: 15px;
    font-size: 24px;
  }
}
@media screen and (min-width: 641px) {
  /* line 4, ../scss/components/modules/_navigation.scss */
  #lightcase-nav a[class*='lightcase-icon-'] {
    bottom: 50%;
    margin-bottom: -0.5em;
  }
  /* line 66, ../scss/components/modules/_navigation.scss */
  #lightcase-nav a[class*='lightcase-icon-']:hover, #lightcase-case:hover ~ #lightcase-nav a[class*='lightcase-icon-'] {
    opacity: 1;
  }
}

/* line 1, ../scss/components/modules/_overlay.scss */
#lightcase-overlay {
  display: none;
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 2000;
  top: 0;
  left: 0;
  background: #333;
}
@media screen and (max-width: 640px) {
  /* line 1, ../scss/components/modules/_overlay.scss */
  #lightcase-overlay {
    opacity: 1 !important;
  }
}
