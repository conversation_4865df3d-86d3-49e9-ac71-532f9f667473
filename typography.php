<!DOCTYPE html>
<html lang="en-US">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Prospera - New Amazing HTML5 Template</title>
    <link rel="stylesheet" href="assets/css/components.css">  
    <link rel="stylesheet" href="assets/css/icons.css">  
    <link rel="stylesheet" href="assets/css/responsee.css">
    <link rel="stylesheet" href="assets/owl-carousel/owl.carousel.css">
    <link rel="stylesheet" href="assets/owl-carousel/owl.theme.css">
    <link rel="stylesheet" href="assets/css/lightcase.css">
    <!-- CUSTOM STYLE -->
    <link rel="stylesheet" href="assets/css/template-style.css">
    <link href='https://fonts.googleapis.com/css?family=Open+Sans:400,300,700,800&subset=latin,latin-ext' rel='stylesheet' type='text/css'>
    <script type="text/javascript" src="assets/js/jquery-1.8.3.min.js"></script>
    <script type="text/javascript" src="assets/js/jquery-ui.min.js"></script>    
    <script type="text/javascript" src="assets/js/validation.js"></script> 
  </head>
  
  <!--
  You can change the color scheme of the page. Just change the class of the <body> tag. 
  You can use this class: "primary-color-white", "primary-color-yellow", "primary-color-red", "primary-color-orange", "primary-color-pink", "primary-color-purple", "primary-color-blue", "primary-color-light-blue", "primary-color-aqua", "primary-color-green", "primary-color-dark" 
  -->
  
  <!--
  Each element is able to have its own background or text color. Just change the class of the element.  
  You can use this class: 
  "background-white", "background-yellow", "background-red", "background-orange", "background-pink", "background-purple", "background-blue", "background-light-blue", "background-aqua", "background-green", "background-grey", "background-sand",  "background-primary" 
  "text-white", "text-yellow", "text-red", "text-orange", "text-pink", "text-purple", "text-blue", "text-light-blue", "text-aqua", "text-green", "text-primary"
  -->
  
  <!-- 
  If you want animated elements after scroll, add class "animated-element" or "animated-element slow" (for slower animation) to the elements.
  -->
  <body class="size-1140 primary-color-aqua">
  
    <!-- HEADER -->
    <header role="banner">    
      <!-- Top Bar -->
      <div class="top-bar hide-s hide-m background-white">
        <div class="line">
          <div class="s-12 m-6 l-6">
            <div class="top-bar-contact">
              <p class="text-size-12">Contact Us: 0800 200 200 | <a class="text-orange-hover" href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>
          </div>
          <div class="s-12 m-6 l-6">
            <div class="right">
              <ul class="top-bar-social right">
                <li><a href="/"><i class="icon-facebook_circle text-orange-hover"></i></a></li>
                <li><a href="/"><i class="icon-twitter_circle text-orange-hover"></i></a> </li>
                <li><a href="/"><i class="icon-google_plus_circle text-orange-hover"></i></a></li>
                <li><a href="/"><i class="icon-instagram_circle text-orange-hover"></i></a></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Top Navigation -->
      <nav class="background-white background-primary-hightlight sticky">
        <div class="line">
          <div class="s-12 l-2">
            <a href="index.php" class="logo"><img src="assets/img/logo.png" alt=""></a>
          </div>
          <div class="top-nav s-12 l-10">
            
            <ul class="right chevron">
              <li>
                <a>Home</a>
                <ul>
                  <li><a href="index.php">Business Home Page</a></li>
                  <li><a href="design.php">Design Home Page</a></li>
                  <li><a href="animated-elements.php">Animated Elements</a></li>
                  <li><a href="boxed-carousel.php">Boxed Carousel</a></li>
                  <li><a href="video-carousel.php">Video Carousel</a></li>
                </ul>
              </li>
              <li>
                <a>Magazine</a>
                <ul>
                  <li><a href="magazine.php">Magazine Home Page</a></li>
                  <li><a href="sample-post.php">Sample Post</a></li>
                </ul>
              </li>
              <li>
                <a>Pages</a>
                <ul>
                  <li><a href="sample-page.php">Sample Page</a></li>
                  <li><a href="sample-page-with-sidebar.php">Sample Page with Sidebar</a></li>
                </ul>
              </li>
              <li><a href="features.php">Features</a></li>
              <li><a href="elements.php">Elements</a></li>
              <li><a href="typography.php">Typography</a></li>
              <li><a href="gallery.php">Gallery</a></li>
              <li><a href="contact.php">Contact</a></li>
            </ul>
          </div>
        </div>
      </nav>
    </header>
    
    <!-- MAIN -->
    <main role="main">
      <!-- Article -->
      <article>
        <header class="section background-primary background-transparent text-center" data-image-src="<?php echo get_template_directory_uri(); ?>/assets/img/parallax-02.webp">
            <h1 class="text-white margin-bottom-0 text-size-50 text-thin text-line-height-1">Typography</h1>
        </header>
        <div class="section background-white"> 
          <div class="line"> 
            <div class="margin">
              <!-- Sidebar -->
              <div class="s-12 m-4 l-2">
                <aside class="aside-left">                  
                  <div class="aside-nav background-primary-hightlight">                    
                    <ul class="chevron">                      
                      <li><a href="#headings">Headings</a></li> 
                      <li><a href="#paragraph">Paragraph</a></li> 
                      <li><a href="#blockquote">Blockquote</a></li> 
                      <li><a href="#breaks">Breaks</a></li> 
                      <li><a href="#lists">Lists</a></li>
                      <li><a href="#tables">Tables</a></li>                                        
                    </ul>                  
                  </div>
                </aside>  
              </div>
              <!-- Content -->
              <div class="s-12 m-8 l-10">
                <div id="headings"></div>
                <h1>H1 Title</h1>
                <p class="h1">Paragraph with h1 class</p>
                <hr class="break">
                <h2>H2 Title</h2>
                <p class="h2">Paragraph with h2 class</p>
                <hr class="break">
                <h3>H3 Title</h3>
                <p class="h3">Paragraph with h3 class</p>
                <hr class="break">
                <h4>H4 Title</h4>
                <p class="h4">Paragraph with h4 class</p>
                <hr class="break">
                <h5>H5 Title</h5>
                <p class="h5">Paragraph with h5 class</p>
                <hr class="break">
                <h6>H6 Title</h6>
                <p class="h6">Paragraph with h6 class</p>
                <hr class="break">            
                <h1 class="text-size-60 text-thin">H1 Title - text size 60px, thin</h1>
                <h1 class="text-size-40 text-extra-strong text-red">H2 Title - text size 40px, extra strong, red</h1>
                <div id="paragraph"></div>    
                <hr class="break">
                
                <h2>Paragraph</h2>
                <p>
                Lorem ipsum dolor <span class="text-red text-uppercase text-strong">text red, uppercase, strong</span>, sed diam nonummy nibh euismod <span class="text-pink text-orange-hover text-size-20">text pink, orange hover, size 20px</span> ut laoreet dolore magna aliquam erat volutpat. 
                Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. 
                Duis autem vel eum iriure dolor in <span class="text-green text-uppercase text-extra-strong">text green, uppercase, extra strong</span> in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros 
                et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore te feugait nulla facilisi.
                </p>
                <br>
                <p class="text-size-12">Paragraph - text size 12px</p>
                <p class="text-size-16">Paragraph - text size 16px</p>
                <p class="text-size-20">Paragraph - text size 20px</p>
                <p class="text-size-25">Paragraph - text size 25px</p>
                <p class="text-size-30">Paragraph - text size 30px</p>
                <p class="text-size-40">Paragraph - text size 40px</p>
                <p class="text-size-50">Paragraph - text size 50px</p>
                <p class="text-size-60">Paragraph - text size 60px</p>
                <p class="text-size-70">Paragraph - text size 70px</p>
                <div id="blockquote"></div> 
                <hr class="break">
                
                <blockquote>
                  <h2>Blockquote</h2>
                  Mirum est notare quam littera gothica, quam nunc putamus parum claram, anteposuerit litterarum formas humanitatis per seacula quarta decima et quinta decima.
                </blockquote>
                <div id="breaks"></div>
                <hr class="break">
                
                <h2>Breaks</h2>
                <h5>Full Width Break</h5>
                <hr class="break">
                <h5>Full Width Break - dashed</h5>
                <hr class="break break-dashed">
                <h5>Full Width Break - dotted</h5>
                <hr class="break break-dotted">
                <h5>Full Width Break - double</h5>
                <hr class="break break-double">
                <h5>Full Width Break - dashed, double</h5>
                <hr class="break break-dashed-double">
                <h5>Full Width Break - dotted, double</h5>
                <hr class="break break-dotted-double">
                
                <h5>Full Width Break - small</h5>
                <hr class="break break-small">
                <h5>Full Width Break - small, centered</h5>
                <hr class="break break-small break-center">
                <h5>Full Width Break - small, double</h5>
                <hr class="break break-small break-double">
                <h5>Full Width Break - small, centered, double</h5>
                <hr class="break break-small break-center break-double">
                <h5>Full Width Break - small, double, colored</h5>
                <hr class="break break-small background-red">
                <div id="lists"></div>
                <hr class="break">
                
                <div class="line">
                  <div class="margin">
                    <div class="s-12 m-12 l-6">
                      <h2>Unordered List</h2>
                        <ul class="text-list">
                          <li> Lorem ipsum dolor amet</li>
                          <li> Consectetur adipiscing elit </li>
                          <li>Integer molestie lorem massa </li>
                          <li> Facilisis in pretium nisl aliquet
                            <ul>
                              <li> Nulla volutpat aliquam velit </li>
                              <li> Phasellus iaculis neque </li>
                              <li> Purus sodales ultricies </li>
                              <li> Vestibulum laot porttitor sem </li>
                            </ul>
                          </li>
                          <li> Ac tristique libero volutpat at </li>
                          <li> Faucibus porta lacingilla vel </li>
                          <li> Eget porttitor lorem </li>
                          <li> Integer vel nibh sit amet </li>
                        </ul>
                      </div>
                      <div class="s-12 m-12 l-6">
                      <h2>Ordered List</h2>
                        <ol class="text-list">
                          <li> Lorem ipsum dolor amet</li>
                          <li> Consectetur adipiscing elit </li>
                          <li>Integer molestie lorem massa </li>
                          <li> Facilisis in pretium nisl aliquet
                            <ol>
                              <li> Nulla volutpat aliquam velit </li>
                              <li> Phasellus iaculis neque </li>
                              <li> Purus sodales ultricies </li>
                              <li> Vestibulum laot porttitor sem </li>
                            </ol>
                          </li>
                          <li> Ac tristique libero volutpat at </li>
                          <li> Faucibus porta lacingilla vel </li>
                          <li> Eget porttitor lorem </li>
                          <li> Integer vel nibh sit amet </li>
                        </ol>
                      </div>
                    </div>
                  </div>
                  <div id="tables"></div>  
                  <hr class="break">
    
                  <h2>Tables</h2>
                  <div class="line">
                    <table>
                    <thead>
                    <tr>
                    <th>Main header</th>
                    <th>Main header</th>
                    <th>Main header</th>
                    <th width="400">Main header</th>
                    <th>Main header</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                    <td>Some content</td>
                    <td>Some content</td>
                    <td>Some content</td>
                    <td>Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat.</td>
                    <td>Some content</td>
                    </tr>
                    <tr>
                    <td>Some content</td>
                    <td>Some content</td>
                    <td>Some content</td>
                    <td>Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat.</td>
                    <td>Some content</td>
                    </tr>
                    <tr>
                    <td>Some content</td>
                    <td>Some content</td>
                    <td>Some content</td>
                    <td>Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat.</td>
                    <td>Some content</td>
                    </tr>
                    </tbody>
                    </table>
                    </div> 
              </div> 
            </div>      
          </div> 
        </div>
      </article>
    </main>
    
    <!--FOOTER -->
    <footer>
      <!-- Main Footer -->
      <section class="section background-dark">
        <div class="line">
          <div class="margin">
            <!-- Collumn 1 -->
            <div class="s-12 m-12 l-4 margin-m-bottom-30">
              <h4 class="text-uppercase text-strong">Our Philosophy</h4>
              <p class="text-size-20"><em>"Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt."</em><p>
                            
              <div class="line">
                <h4 class="text-uppercase text-strong margin-top-30">About Our Company</h4>
                <div class="margin">
                  <div class="s-12 m-12 l-4 margin-m-bottom">
                    <a class="image-hover-zoom" href="/"><img src="assets/img/blog-04.jpg" alt=""></a>
                  </div>
                  <div class="s-12 m-12 l-8 margin-m-bottom">
                    <p>Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat.</p>
                    <a class="text-more-info text-primary-hover" href="/">Read more</a>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Collumn 2 -->
            <div class="s-12 m-12 l-4 margin-m-bottom-30">
              <h4 class="text-uppercase text-strong">Contact Us</h4>
              <div class="line">
                <div class="s-1 m-1 l-1 text-center">
                  <i class="icon-placepin text-primary text-size-12"></i>
                </div>
                <div class="s-11 m-11 l-11 margin-bottom-10">
                  <p><b>Adress:</b> Responsive Street 7, London, UK</p>
                </div>
              </div>
              <div class="line">
                <div class="s-1 m-1 l-1 text-center">
                  <i class="icon-mail text-primary text-size-12"></i>
                </div>
                <div class="s-11 m-11 l-11 margin-bottom-10">
                  <p><a href="/" class="text-primary-hover"><b>E-mail:</b> <EMAIL></a></p>
                </div>
              </div>
              <div class="line">
                <div class="s-1 m-1 l-1 text-center">
                  <i class="icon-smartphone text-primary text-size-12"></i>
                </div>
                <div class="s-11 m-11 l-11 margin-bottom-10">
                  <p><b>Phone:</b> 0700 000 987</p>
                </div>
              </div>
              <div class="line">
                <div class="s-1 m-1 l-1 text-center">
                  <i class="icon-twitter text-primary text-size-12"></i>
                </div>
                <div class="s-11 m-11 l-11 margin-bottom-10">
                  <p><a href="/" class="text-primary-hover"><b>Twitter</b></a></p>
                </div>
              </div>
              <div class="line">
                <div class="s-1 m-1 l-1 text-center">
                  <i class="icon-facebook text-primary text-size-12"></i>
                </div>
                <div class="s-11 m-11 l-11">
                  <p><a href="/" class="text-primary-hover"><b>Facebook</b></a></p>
                </div>
              </div>
            </div>
            
            <!-- Collumn 3 -->
            <div class="s-12 m-12 l-4">
              <h4 class="text-uppercase text-strong">Functional Ajax Contact Form</h4>
              <!-- If you want to make a field required, add the "required" class to the input. -->
              <!-- The form e-mail address you can change on file resp-email.php on line 4. -->
              <form name="contactForm" class="customform ajax-form text-white" method="post">
                <div class="line">
                  <div class="margin">
                    <div class="s-12 m-12 l-6">
                      <input name="email" class="required email border-radius" placeholder="Your e-mail" title="Your e-mail" type="text" />
                      <p class="email-error form-error">Please enter your e-mail.</p>
                    </div>
                    <div class="s-12 m-12 l-6">
                      <input name="name" class="name border-radius" placeholder="Your name" title="Your name" type="text" />
                      <p class="name-error form-error">Please enter your name.</p>
                    </div>
                  </div>
                </div>
                <div class="s-12">
                  <input name="subject" class="required subject border-radius" placeholder="Subject" title="Subject" type="text" />
                  <p class="subject-error form-error">Please enter your subject.</p>
                </div>
                <div class="s-12">
                  <textarea name="message" class="required message border-radius" placeholder="Your message" rows="3"></textarea>
                  <p class="message-error form-error">Please enter your message.</p>
                </div>
                <!-- file upload input
                <div class="s-12">
                  <input type="file" name="file[]" />                    
                </div>
                 -->
                <!-- full-name-field is hidden antirobot field -->
                <input name="full-name-field" type="text border-radius" id="full-name-field" class="full-name-field" />
                <div class="s-12"><a class="captcha-button text-white border-radius margin-bottom"><span class="not-a-robot-icon"><i class="icon-check text-green"></i></span> <span class="not-a-robot-text">I'm not a robot</span></a></div> 
                <!-- The submit button text you can edit in the file validation.sj on the line 8. -->
                <div class="s-12 button-parent"></div>
                <div class="line">
                  <p class="mail-success form-success">Your message has been sent successfully.</p>
                  <p class="mail-fail form-error">Sorry, error occured this time sending your message.</p>
                </div>  
              </form>
            </div>
          </div>
        </div>
      </section>
      <hr class="break margin-top-bottom-0" style="border-color: rgba(0, 38, 51, 0.80);">
      
      <!-- Bottom Footer -->
      <section class="padding background-dark">
        <div class="line">
          <div class="s-12 l-6">
            <p class="text-size-12">Copyright 2019, Vision Design - graphic zoo</p>
            <p class="text-size-12">All images have been purchased from Bigstock. Do not use the images in your website.</p>
          </div>
          <div class="s-12 l-6">
            <a class="right text-size-12" href="http://www.myresponsee.com" title="Responsee - lightweight responsive framework">Design and coding<br> by Responsee Team</a>
          </div>
        </div>
      </section>
    </footer> 
    <script type="text/javascript" src="assets/js/responsee.js"></script>
    <script type="text/javascript" src="assets/js/plugins.min.js"></script>
    <script type="text/javascript" src="assets/owl-carousel/owl.carousel.js"></script>
    <script type="text/javascript" src="assets/js/template-scripts.js"></script> 
  </body>
</html>