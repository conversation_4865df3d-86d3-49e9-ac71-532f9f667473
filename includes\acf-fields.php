<?php
/**
 * Register ACF fields for the theme
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Check if ACF is active
if (!function_exists('acf_add_local_field_group')) {
    return;
}

/**
 * Add daily_schedule field to school_categories post type
 */
function nkhwazi_add_daily_schedule_field() {
    acf_add_local_field_group(array(
        'key' => 'group_school_category_schedule',
        'title' => 'School Category Schedule',
        'fields' => array(
            array(
                'key' => 'field_daily_schedule',
                'label' => 'Daily Schedule',
                'name' => 'daily_schedule',
                'type' => 'textarea',
                'instructions' => 'Enter the daily schedule for this school category. (10-300 characters)',
                'required' => 1,
                'wrapper' => array(
                    'width' => '50',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'placeholder' => '',
                'minlength' => 10,
                'maxlength' => 300,
                'rows' => 4,
                'new_lines' => 'wpautop',
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'school_categories',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 0,
    ));
}
add_action('acf/init', 'nkhwazi_add_daily_schedule_field');

/**
 * Add fields to notification post type
 */
function nkhwazi_add_notification_fields() {
    acf_add_local_field_group(array(
        'key' => 'group_notification_fields',
        'title' => 'Notification Details',
        'fields' => array(
            array(
                'key' => 'field_notification_heading',
                'label' => 'Heading',
                'name' => 'heading',
                'type' => 'text',
                'instructions' => 'Enter the notification heading (3-40 characters)',
                'required' => 1,
                'wrapper' => array(
                    'width' => '50',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'placeholder' => '',
                'minlength' => 3,
                'maxlength' => 40,
            ),
            array(
                'key' => 'field_notification_description',
                'label' => 'Description',
                'name' => 'description',
                'type' => 'textarea',
                'instructions' => 'Enter the notification description',
                'required' => 1,
                'wrapper' => array(
                    'width' => '50',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'placeholder' => '',
                'minlength' => 10,
                'maxlength' => 200,
                'rows' => 4,
                'new_lines' => 'wpautop',
            ),
            array(
                'key' => 'field_notification_expiry_date',
                'label' => 'Expiry Date',
                'name' => 'expiry_date',
                'type' => 'date_picker',
                'instructions' => 'Select the expiry date for this notification (used to automatically remove expired notifications)',
                'required' => 1,
                'wrapper' => array(
                    'width' => '50',
                    'class' => '',
                    'id' => '',
                ),
                'display_format' => 'd/m/Y',
                'return_format' => 'd/m/Y',
                'first_day' => 1,
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'notification',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => array(
            0 => 'permalink',
            1 => 'the_content',
            2 => 'excerpt',
            3 => 'discussion',
            4 => 'comments',
            5 => 'revisions',
            6 => 'slug',
            7 => 'author',
            8 => 'format',
            9 => 'page_attributes',
            10 => 'featured_image',
            11 => 'categories',
            12 => 'tags',
            13 => 'send-trackbacks',
        ),
        'active' => true,
        'description' => '',
        'show_in_rest' => 0,
    ));
}
add_action('acf/init', 'nkhwazi_add_notification_fields');