/**
 * Legal Sidebar Block
 */
(function() {
    const { registerBlockType } = wp.blocks;
    const { useBlockProps } = wp.blockEditor;
    const { __ } = wp.i18n;
    
    registerBlockType('nkhwazischool/legal-sidebar', {
        edit: function() {
            const blockProps = useBlockProps({
                className: 'legal-sidebar padding-2x background-light-gray rounded-div',
            });
            
            return (
                <div {...blockProps}>
                    <h3 className="text-strong margin-bottom-20">Legal Documents</h3>
                    <p className="editor-note">Legal navigation will appear here on the front end.</p>
                    <div className="legal-contact margin-top-30">
                        <h4 className="text-strong margin-bottom-10">Questions?</h4>
                        <p>If you have any questions about our legal policies, please <a href="#">contact us</a>.</p>
                    </div>
                </div>
            );
        },
        save: function() {
            return null; // Dynamic block, rendering is handled by <PERSON><PERSON>
        },
    });
})();