/**
 * Application Form JavaScript
 * 
 * Handles form validation, AJAX submission, and UI interactions
 */
(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('Document ready - initializing application form');
        // Initialize form
        initApplicationForm();
    });
    
    // Fallback for jQuery ready event
    $(window).on('load', function() {
        console.log('Window loaded - checking if form is initialized');
        if ($('#application-form').length && !$('#application-form').data('initialized')) {
            console.log('Form not initialized yet - initializing now');
            initApplicationForm();
        }
    });

    /**
     * Initialize the application form
     */
    function initApplicationForm() {
        const $form = $('#application-form');
        
        if (!$form.length) {
            console.log('Form not found in the DOM');
            return;
        }
        
        // Check if already initialized
        if ($form.data('initialized')) {
            console.log('Form already initialized');
            return;
        }
        
        console.log('Initializing application form');
        
        // Mark as initialized
        $form.data('initialized', true);
        
        // Initialize datepicker
        $('#datepicker').datepicker({ 
            dateFormat: 'dd-mm-yy',
            changeMonth: true,
            changeYear: true,
            yearRange: "-20:-3" // Allow selection from 20 years ago up to 3 years before current date
        });
        
        // Initialize form validation
        initFormValidation($form);
        
        // Initialize form steps
        initFormSteps($form);
        
        // Handle form submission
        $form.on('submit', function(e) {
            e.preventDefault();
            console.log('Form submitted');
            
            // Show form message for testing
            showFormMessage($form, 'Processing your application...', 'success');
            
            // Validate all fields before submission
            if (!validateAllFields($form)) {
                showFormMessage($form, 'Please fill in all required fields correctly.', 'error');
                return;
            }
            
            // Validate reCAPTCHA
            if (!validateRecaptcha($form)) {
                showFormMessage($form, 'Please complete the reCAPTCHA verification.', 'error');
                return;
            }
            
            // Submit form via AJAX
            submitFormAjax($form);
        });
    }
    
    /**
     * Initialize form validation
     */
    function initFormValidation($form) {
        // Initialize character counters for fields with min/max length
        initCharacterCounters($form);
        
        // Validate required fields on blur
        $form.find('.required').on('blur', function() {
            validateField($(this));
        });
        
        // Validate optional fields with min/max length on blur
        $form.find('input:not(.required), textarea:not(.required)').filter('[data-min-length], [data-max-length]').on('blur', function() {
            // Only validate if the field has content
            if ($(this).val().trim() !== '') {
                validateField($(this));
            }
        });
        
        // Validate email fields on blur
        $form.find('.email').on('blur', function() {
            validateEmail($(this));
        });
        
        // Validate fields on change for select elements
        $form.find('select.required').on('change', function() {
            validateField($(this));
        });
        
        // Live validation as user types (after first blur)
        $form.find('input.required, textarea.required').on('input', function() {
            if ($(this).hasClass('error') || $(this).hasClass('valid')) {
                validateField($(this));
            }
            updateCharacterCount($(this));
        });
        
        // Live validation for email fields
        $form.find('input.email').on('input', function() {
            if ($(this).hasClass('error') || $(this).hasClass('valid')) {
                validateEmail($(this));
            }
        });
        
        // Live validation for all fields with min/max length
        $form.find('[data-min-length], [data-max-length]').on('input', function() {
            updateCharacterCount($(this));
            // For optional fields, only validate if they have content
            if ($(this).hasClass('required') || $(this).val().trim() !== '') {
                validateField($(this));
            } else {
                // If optional field is empty, remove any validation classes
                $(this).removeClass('error valid');
            }
        });
        
        // Initialize character counters on page load
        $form.find('[data-min-length], [data-max-length]').each(function() {
            updateCharacterCount($(this));
        });
    }
    
    /**
     * Initialize character counters for fields with min/max length
     */
    function initCharacterCounters($form) {
        $form.find('[data-min-length], [data-max-length]').each(function() {
            const $field = $(this);
            
            // Create a container for the field and counter if it doesn't exist
            if (!$field.parent().hasClass('field-with-counter')) {
                // Get the field's current container
                const $fieldContainer = $field.parent();
                
                // Create a wrapper for the field
                $field.wrap('<div class="field-with-counter"></div>');
                
                // Add character counter after the field inside the wrapper
                $field.after('<div class="character-counter"></div>');
                updateCharacterCount($field);
            }
        });
    }
    
    /**
     * Update character count display
     */
    function updateCharacterCount($field) {
        const minLength = $field.data('min-length');
        const maxLength = $field.data('max-length');
        
        if (!minLength && !maxLength) {
            return;
        }
        
        const currentLength = $field.val().length;
        
        // Find the counter within the field's wrapper
        let $counter;
        if ($field.parent().hasClass('field-with-counter')) {
            $counter = $field.parent().find('.character-counter');
        } else {
            // For backward compatibility
            $counter = $field.next('.character-counter');
        }
        
        // Create counter if it doesn't exist
        if (!$counter.length) {
            if ($field.parent().hasClass('field-with-counter')) {
                $field.after('<div class="character-counter"></div>');
                $counter = $field.parent().find('.character-counter');
            } else {
                $field.after('<div class="character-counter"></div>');
                $counter = $field.next('.character-counter');
            }
        }
        
        let counterText = '';
        let counterClass = '';
        
        // Different messages based on validation state
        if (currentLength === 0 && !$field.hasClass('required')) {
            // Optional field that's empty
            counterText = 'Optional';
        } else if (minLength > 0 && currentLength < minLength && (currentLength > 0 || $field.hasClass('required'))) {
            // Below minimum length (only show warning if field has content or is required)
            // Only apply if minLength is greater than 0
            counterText = currentLength + '/' + minLength + ' min';
            counterClass = 'warning';
        } else if (maxLength && currentLength > maxLength) {
            // Exceeds maximum length
            counterText = currentLength + '/' + maxLength + ' (too long)';
            counterClass = 'error';
        } else if (maxLength && currentLength >= maxLength * 0.9) {
            // Approaching maximum length
            const remaining = maxLength - currentLength;
            counterText = remaining + ' character' + (remaining !== 1 ? 's' : '') + ' left';
            counterClass = 'warning';
        } else if (maxLength) {
            // Within limits with maximum
            counterText = currentLength + '/' + maxLength;
        } else if (minLength > 0 && currentLength >= minLength) {
            // Above minimum length with no maximum
            // Only apply if minLength is greater than 0
            counterText = currentLength + ' characters';
        }
        
        $counter.text(counterText).removeClass('warning error').addClass(counterClass);
    }
    
    /**
     * Initialize form steps
     */
    function initFormSteps($form) {
        const $steps = $form.find('.form-step');
        const $progressSteps = $form.find('.form-progress-step');
        const $nextButtons = $form.find('.next-step');
        const $prevButtons = $form.find('.prev-step');
        
        // Show first step
        $steps.first().addClass('active');
        $progressSteps.first().addClass('active');
        
        // Handle next button clicks
        $nextButtons.on('click', function(e) {
            e.preventDefault();
            
            const $currentStep = $form.find('.form-step.active');
            const currentStepIndex = $steps.index($currentStep);
            
            // Validate current step fields
            if (!validateStepFields($currentStep)) {
                showFormMessage($form, 'Please fill in all required fields in this step correctly.', 'error');
                return;
            }
            
            // Hide current step
            $currentStep.removeClass('active');
            
            // Show next step
            $steps.eq(currentStepIndex + 1).addClass('active');
            
            // Update progress indicator
            $progressSteps.eq(currentStepIndex).removeClass('active').addClass('completed');
            $progressSteps.eq(currentStepIndex + 1).addClass('active');
            
            // Scroll to top of form
            $('html, body').animate({
                scrollTop: $form.offset().top - 50
            }, 300);
        });
        
        // Handle previous button clicks
        $prevButtons.on('click', function(e) {
            e.preventDefault();
            
            const $currentStep = $form.find('.form-step.active');
            const currentStepIndex = $steps.index($currentStep);
            
            // Hide current step
            $currentStep.removeClass('active');
            
            // Show previous step
            $steps.eq(currentStepIndex - 1).addClass('active');
            
            // Update progress indicator
            $progressSteps.eq(currentStepIndex).removeClass('active');
            $progressSteps.eq(currentStepIndex - 1).removeClass('completed').addClass('active');
            
            // Scroll to top of form
            $('html, body').animate({
                scrollTop: $form.offset().top - 50
            }, 300);
        });
    }
    
    /**
     * Validate a single field
     */
    function validateField($field) {
        // Get the container (either field-with-counter or parent)
        const $container = $field.parent().hasClass('field-with-counter') ? $field.parent() : $field.parent();
        
        // Remove existing error message
        $container.find('.field-error-message').remove();
        
        // Check if field is empty
        if ($field.val() === '' && $field.hasClass('required')) {
            $field.removeClass('valid').addClass('error');
            
            // Add error message in the appropriate place
            if ($container.hasClass('field-with-counter')) {
                // If using the new structure, add after the counter
                const $counter = $container.find('.character-counter');
                if ($counter.length) {
                    $counter.after('<span class="field-error-message">This field is required</span>');
                } else {
                    $field.after('<span class="field-error-message">This field is required</span>');
                }
            } else {
                // Legacy support
                $field.after('<span class="field-error-message">This field is required</span>');
            }
            return false;
        }
        
        // Check min and max length if specified
        const minLength = $field.data('min-length');
        const maxLength = $field.data('max-length');
        const fieldValue = $field.val().trim();
        
        // Skip validation if field is empty and not required
        if (fieldValue === '' && !$field.hasClass('required')) {
            $field.removeClass('error valid');
            return true;
        }
        
        // For optional fields with content, we still want to validate them
        const isOptionalWithContent = !$field.hasClass('required') && fieldValue !== '';
        
        // Check for alphanumeric content (only for text inputs and textareas)
        // This ensures fields don't pass validation if they only contain punctuation or special characters
        if ($field.is('input[type="text"], textarea') && fieldValue !== '') {
            // Check if the field contains at least one alphanumeric character
            const alphanumericRegex = /[a-zA-Z0-9]/;
            if (!alphanumericRegex.test(fieldValue)) {
                $field.removeClass('valid').addClass('error');
                
                // Add error message
                const errorMsg = 'Please enter at least one letter or number';
                if ($container.hasClass('field-with-counter')) {
                    const $counter = $container.find('.character-counter');
                    if ($counter.length) {
                        $counter.after('<span class="field-error-message">' + errorMsg + '</span>');
                    } else {
                        $field.after('<span class="field-error-message">' + errorMsg + '</span>');
                    }
                } else {
                    $field.after('<span class="field-error-message">' + errorMsg + '</span>');
                }
                return false;
            }
        }
        
        // Special validation for phone fields
        if ($field.attr('name') && $field.attr('name').includes('phone') && fieldValue !== '') {
            // Check if the phone field contains at least one digit
            const phoneRegex = /[0-9]/;
            if (!phoneRegex.test(fieldValue)) {
                $field.removeClass('valid').addClass('error');
                
                // Add error message
                const errorMsg = 'Phone number must contain at least one digit';
                if ($container.hasClass('field-with-counter')) {
                    const $counter = $container.find('.character-counter');
                    if ($counter.length) {
                        $counter.after('<span class="field-error-message">' + errorMsg + '</span>');
                    } else {
                        $field.after('<span class="field-error-message">' + errorMsg + '</span>');
                    }
                } else {
                    $field.after('<span class="field-error-message">' + errorMsg + '</span>');
                }
                return false;
            }
        }
        
        if (minLength > 0 && fieldValue.length < minLength) {
            $field.removeClass('valid').addClass('error');
            
            // Add error message in the appropriate place
            const errorMsg = 'Please enter at least ' + minLength + ' characters';
            if ($container.hasClass('field-with-counter')) {
                const $counter = $container.find('.character-counter');
                if ($counter.length) {
                    $counter.after('<span class="field-error-message">' + errorMsg + '</span>');
                } else {
                    $field.after('<span class="field-error-message">' + errorMsg + '</span>');
                }
            } else {
                $field.after('<span class="field-error-message">' + errorMsg + '</span>');
            }
            return false;
        }
        
        if (maxLength && fieldValue.length > maxLength) {
            $field.removeClass('valid').addClass('error');
            
            // Add error message in the appropriate place
            const errorMsg = 'Please enter no more than ' + maxLength + ' characters (' + fieldValue.length + '/' + maxLength + ')';
            if ($container.hasClass('field-with-counter')) {
                const $counter = $container.find('.character-counter');
                if ($counter.length) {
                    $counter.after('<span class="field-error-message">' + errorMsg + '</span>');
                } else {
                    $field.after('<span class="field-error-message">' + errorMsg + '</span>');
                }
            } else {
                $field.after('<span class="field-error-message">' + errorMsg + '</span>');
            }
            return false;
        }
        
        // If we got here, the field is valid
        $field.removeClass('error').addClass('valid');
        return true;
    }
    
    /**
     * Validate an email field
     */
    function validateEmail($field) {
        // Get the container (either field-with-counter or parent)
        const $container = $field.parent().hasClass('field-with-counter') ? $field.parent() : $field.parent();
        
        // Remove existing error message
        $container.find('.field-error-message').remove();
        
        // Check if field is empty and required
        if ($field.hasClass('required') && $field.val() === '') {
            $field.removeClass('valid').addClass('error');
            
            // Add error message in the appropriate place
            const errorMsg = 'This field is required';
            if ($container.hasClass('field-with-counter')) {
                const $counter = $container.find('.character-counter');
                if ($counter.length) {
                    $counter.after('<span class="field-error-message">' + errorMsg + '</span>');
                } else {
                    $field.after('<span class="field-error-message">' + errorMsg + '</span>');
                }
            } else {
                $field.after('<span class="field-error-message">' + errorMsg + '</span>');
            }
            return false;
        }
        
        // Check email format if not empty
        if ($field.val() !== '') {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test($field.val())) {
                $field.removeClass('valid').addClass('error');
                
                // Add error message in the appropriate place
                const errorMsg = 'Please enter a valid email address';
                if ($container.hasClass('field-with-counter')) {
                    const $counter = $container.find('.character-counter');
                    if ($counter.length) {
                        $counter.after('<span class="field-error-message">' + errorMsg + '</span>');
                    } else {
                        $field.after('<span class="field-error-message">' + errorMsg + '</span>');
                    }
                } else {
                    $field.after('<span class="field-error-message">' + errorMsg + '</span>');
                }
                return false;
            } else {
                $field.removeClass('error').addClass('valid');
                return true;
            }
        }
        
        return true;
    }
    
    /**
     * Validate all fields in a step
     */
    function validateStepFields($step) {
        let isValid = true;
        
        // Validate required fields
        $step.find('.required').each(function() {
            if (!validateField($(this))) {
                isValid = false;
            }
        });
        
        // Validate email fields
        $step.find('.email').each(function() {
            if (!validateEmail($(this))) {
                isValid = false;
            }
        });
        
        // Validate optional fields with content that have min/max length requirements
        $step.find('input:not(.required), textarea:not(.required)').filter('[data-min-length], [data-max-length]').each(function() {
            const $field = $(this);
            const fieldValue = $field.val().trim();
            const maxLength = $field.data('max-length');
            
            // Only validate if the field has content or exceeds max length
            if (fieldValue !== '' && maxLength && fieldValue.length > maxLength) {
                if (!validateField($field)) {
                    isValid = false;
                }
            }
        });
        
        return isValid;
    }
    
    /**
     * Validate all fields in the form
     */
    function validateAllFields($form) {
        let isValid = true;
        
        // Validate required fields
        $form.find('.required').each(function() {
            if (!validateField($(this))) {
                isValid = false;
            }
        });
        
        // Validate email fields
        $form.find('.email').each(function() {
            if (!validateEmail($(this))) {
                isValid = false;
            }
        });
        
        return isValid;
    }
    
    /**
     * Submit form via AJAX
     */
    function submitFormAjax($form) {
        // Show loading indicator
        $form.find('.form-loading').show();
        $form.find('.form-message').hide();
        $form.find('button[type="submit"]').prop('disabled', true);
        
        // Collect form data
        const formData = new FormData($form[0]);
        
        // Add action and nonce
        formData.append('action', 'nkhwazi_submit_application_form');
        formData.append('nonce', nkhwazi_application_form.nonce);
        
        // Convert FormData to object for logging
        const formDataObj = {};
        formData.forEach((value, key) => {
            formDataObj[key] = value;
        });
        
        // Make sure all form fields are included
        const $allFields = $form.find('input, select, textarea');
        $allFields.each(function() {
            const field = $(this);
            const name = field.attr('name');
            
            // Skip fields without a name
            if (!name) return;
            
            // Skip fields that are already in the FormData
            if (formDataObj[name]) return;
            
            // Add the field to the FormData
            if (field.is(':checkbox') || field.is(':radio')) {
                if (field.is(':checked')) {
                    formData.append(name, field.val());
                    formDataObj[name] = field.val();
                }
            } else {
                formData.append(name, field.val());
                formDataObj[name] = field.val();
            }
        });
        
        // Log submission details
        console.log('Submitting form with data:', formDataObj);
        console.log('AJAX URL:', nkhwazi_application_form.ajax_url);
        
        $.ajax({
            url: nkhwazi_application_form.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                console.log('AJAX Success Response:', response);
                
                // Hide loading indicator
                $form.find('.form-loading').hide();
                
                if (response.success) {
                    // Show success message
                    showFormMessage($form, response.data.message, 'success');
                    
                    // Reset form
                    $form[0].reset();
                    $form.find('.valid, .error').removeClass('valid error');
                    $form.find('.field-error-message').remove();
                    
                    // Reset form steps if using steps
                    if ($form.find('.form-step').length) {
                        $form.find('.form-step').removeClass('active');
                        $form.find('.form-step').first().addClass('active');
                        $form.find('.form-progress-step').removeClass('active completed');
                        $form.find('.form-progress-step').first().addClass('active');
                    }
                    
                    // Scroll to top of form
                    $('html, body').animate({
                        scrollTop: $form.offset().top - 50
                    }, 300);
                } else {
                    // Show error message
                    showFormMessage($form, response.data.message, 'error');
                    
                    // Enable submit button
                    $form.find('button[type="submit"]').prop('disabled', false);
                }
            },
            error: function(xhr, status, error) {
                console.log('AJAX Error:', status, error);
                console.log('Response Text:', xhr.responseText);
                
                // Hide loading indicator
                $form.find('.form-loading').hide();
                
                // Show error message
                showFormMessage($form, 'An error occurred. Please try again later.', 'error');
                
                // Enable submit button
                $form.find('button[type="submit"]').prop('disabled', false);
            }
        });
    }
    
    /**
     * Show form message
     */
    function showFormMessage($form, message, type) {
        const $message = $form.find('.form-message');
        
        $message.removeClass('success error').addClass(type);
        $message.html(message).show();
        
        // Scroll to message
        $('html, body').animate({
            scrollTop: $message.offset().top - 100
        }, 300);
    }
    
    /**
     * Validate reCAPTCHA
     */
    function validateRecaptcha($form) {
        const recaptchaResponse = grecaptcha.getResponse();
        const $recaptchaContainer = $form.find('.g-recaptcha-container');
        const $recaptchaError = $form.find('.recaptcha-error-message');
        
        // Clear previous error message
        $recaptchaError.text('');
        
        if (!recaptchaResponse || recaptchaResponse.length === 0) {
            $recaptchaError.text('Please complete the reCAPTCHA verification.');
            $recaptchaContainer.addClass('error');
            return false;
        }
        
        $recaptchaContainer.removeClass('error');
        return true;
    }

})(jQuery);