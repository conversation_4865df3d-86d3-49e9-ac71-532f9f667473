<!-- wp:template-part {"slug":"header","tagName":"header"} /-->

<!-- wp:group {"tagName":"main","style":{"spacing":{"margin":{"top":"0"},"padding":{"top":"0","bottom":"0"}}},"layout":{"type":"default"}} -->
<main class="wp-block-group" style="margin-top:0;padding-top:0;padding-bottom:0">
  <!-- wp:group {"style":{"spacing":{"padding":{"top":"var:preset|spacing|50","bottom":"var:preset|spacing|50"}},"color":{"background":"#3649e2"}},"layout":{"type":"constrained"}} -->
  <div class="wp-block-group has-background rounded-div" style="background-color:#3649e2;padding-top:var(--wp--preset--spacing--50);padding-bottom:var(--wp--preset--spacing--50)">
    <!-- wp:heading {"textAlign":"center","level":1,"textColor":"white","className":"text-uppercase margin-bottom-0 text-thin text-line-height-1"} -->
    <h1 class="wp-block-heading has-text-align-center text-uppercase margin-bottom-0 text-thin text-line-height-1 has-white-color has-text-color"><!-- wp:post-title /--></h1>
    <!-- /wp:heading -->
  </div>
  <!-- /wp:group -->

  <!-- wp:group {"style":{"spacing":{"padding":{"top":"var:preset|spacing|60","bottom":"var:preset|spacing|60"}}},"backgroundColor":"background","layout":{"type":"constrained"}} -->
  <div class="wp-block-group has-background-background-color has-background" style="padding-top:var(--wp--preset--spacing--60);padding-bottom:var(--wp--preset--spacing--60)">
    <!-- wp:columns -->
    <div class="wp-block-columns">
      <!-- wp:column {"width":"66.66%"} -->
      <div class="wp-block-column" style="flex-basis:66.66%">
        <!-- wp:group {"className":"legal-content padding-2x","layout":{"type":"constrained"}} -->
        <div class="wp-block-group legal-content padding-2x">
          <!-- wp:post-content /-->
        </div>
        <!-- /wp:group -->
      </div>
      <!-- /wp:column -->

      <!-- wp:column {"width":"33.33%"} -->
      <div class="wp-block-column" style="flex-basis:33.33%">
        <!-- wp:group {"className":"legal-sidebar padding-2x background-light-gray rounded-div","layout":{"type":"constrained"}} -->
        <div class="wp-block-group legal-sidebar padding-2x background-light-gray rounded-div">
          <!-- wp:heading {"level":3,"className":"text-strong margin-bottom-20"} -->
          <h3 class="wp-block-heading text-strong margin-bottom-20">Legal Documents</h3>
          <!-- /wp:heading -->

          <!-- wp:html -->
          <ul class="legal-nav">
            <?php
            // Get all legal pages
            $legal_pages = array(
                'privacy-policy' => 'Privacy Policy',
                'terms-of-use' => 'Terms of Use',
                'cookies' => 'Cookies',
                'disclaimer' => 'Disclaimer'
            );
            
            $current_page_slug = get_post_field('post_name', get_post());
            
            foreach ($legal_pages as $slug => $title) {
                $page = get_page_by_path($slug);
                if ($page) {
                    $class = ($current_page_slug === $slug) ? 'active-link' : '';
                    echo '<li><a href="' . esc_url(get_permalink($page->ID)) . '" class="' . $class . '">' . esc_html($title) . '</a></li>';
                }
            }
            ?>
          </ul>
          <!-- /wp:html -->

          <!-- wp:group {"className":"legal-contact margin-top-30","layout":{"type":"constrained"}} -->
          <div class="wp-block-group legal-contact margin-top-30">
            <!-- wp:heading {"level":4,"className":"text-strong margin-bottom-10"} -->
            <h4 class="wp-block-heading text-strong margin-bottom-10">Questions?</h4>
            <!-- /wp:heading -->

            <!-- wp:paragraph -->
            <p>If you have any questions about our legal policies, please <a href="<?php echo esc_url(get_permalink(407)); ?>">contact us</a>.</p>
            <!-- /wp:paragraph -->
          </div>
          <!-- /wp:group -->
        </div>
        <!-- /wp:group -->
      </div>
      <!-- /wp:column -->
    </div>
    <!-- /wp:columns -->
  </div>
  <!-- /wp:group -->
</main>
<!-- /wp:group -->

<!-- wp:template-part {"slug":"footer","tagName":"footer"} /-->