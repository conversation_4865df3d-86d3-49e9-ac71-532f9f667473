<?php
/**
 * Template Name: Teaching Staff
 * Description: A template for displaying teaching staff organized by categories (Lower Primary, Middle Primary, Upper Primary).
 *              Includes filtering options and staff cards with modal biographies.
 * Author: <PERSON>
 */
require_once('includes/header.php');
?>
<!-- MAIN -->
<main role="main">
    <!-- third section -->
    <article>
        <div class="line">
            <header class="rounded-div section background-primary background-transparent text-center"
                data-image-src="<?php echo get_template_directory_uri(); ?>/assets/img/parallax-04.webp">
                <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">Teaching Staff</h1>
            </header>
        </div>

        <div class="section background-white">
            <div class="line">
                <div class="margin">
                    <div class="s-12 m-12 l-12">
                        <!-- Gutenberg content from the page -->
                        <div class="line">
                            <?php 
                            while (have_posts()) :
                                the_post();
                                the_content();
                            endwhile;
                            ?>
                        </div>
                        
                        <?php
                        // Get all school categories for filtering
                        $school_categories = get_posts(array(
                            'post_type' => 'school_categories',
                            'posts_per_page' => -1,
                            'orderby' => 'title',
                            'order' => 'ASC'
                        ));
                        
                        // Get current page for pagination
                        $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
                        
                        // Get teachers per page from theme settings or default to 12
                        $teachers_per_page =2;
                        
                        // Count total teachers for pagination
                        $total_teachers = wp_count_posts('teacher')->publish;
                        $total_pages = ceil($total_teachers / $teachers_per_page);
                        ?>
                        
                        <!-- Teacher Filter Categories -->
                        <div class="line margin-bottom-30">
                            <div class="rounded-div background-primary-hightlight padding">
                                <div class="s-12 text-center">
                                    <p class="category-filter margin-bottom-10">Filter Staff Members:</p>
                                    <div class="teacher-filter-buttons">
                                        <a href="#" class="rounded-div button teacher-filter-btn margin-right-10 active">All Teachers</a>
                                        <?php foreach ($school_categories as $category) : 
                                            $category_name = get_the_title($category);
                                            $category_slug = sanitize_title($category_name);
                                        ?>
                                            <a href="#" class="rounded-div button teacher-filter-btn margin-right-10"><?php echo esc_html($category_name); ?></a>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="line">
                            <!-- Team Cards -->
                            <div class="grid margin">
                                <?php
                                // Query teachers
                                $args = array(
                                    'post_type' => 'teacher',
                                    'posts_per_page' => $teachers_per_page,
                                    'paged' => $paged,
                                    'meta_key' => 'priority',
                                    'orderby' => array(
                                        'meta_value_num' => 'ASC',
                                        'title' => 'ASC'
                                    )
                                );
                                
                                $teachers_query = new WP_Query($args);
                                
                                if ($teachers_query->have_posts()) :
                                    $teacher_count = 0;
                                    
                                    while ($teachers_query->have_posts()) : $teachers_query->the_post();
                                        $teacher_count++;
                                        
                                        // Get teacher custom fields
                                        $title = get_field('title');
                                        $first_name = get_field('first_name');
                                        $last_name = get_field('last_name');
                                        $job_title = get_field('job_title');
                                        $bio_excerpt = get_field('bio_excerpt');
                                        $portrait_img = get_field('portrait_img');
                                        
                                        // Get school category
                                        $school_category = get_field('school_category');
                                        $category_name = '';
                                        $category_slug = '';
                                        
                                        if ($school_category && !empty($school_category)) {
                                            $category_obj = $school_category[0]; // Get first category
                                            $category_name = get_the_title($category_obj);
                                            $category_slug = sanitize_title($category_name);
                                        }
                                        
                                        // Generate unique modal ID
                                        $modal_id = 'modal-teacher-' . get_the_ID();
                                        
                                        // Get teacher full name
                                        $teacher_name = $first_name . ' ' . $last_name;
                                        
                                        // Get teacher image
                                        $image_alt = $teacher_name;
                                        
                                        if ($portrait_img && !empty($portrait_img)) {
                                            if (!empty($portrait_img['alt'])) {
                                                $image_alt = $portrait_img['alt'];
                                            }
                                        }
                                ?>
                                        <!-- Teacher Card -->
                                        <div class="card gallery-card s-12 m-6 l-3" data-category="<?php echo esc_attr($category_slug); ?>">
                                            <div class="teacher-category-header"><?php echo esc_html($category_name); ?></div>
                                            <?php if ($portrait_img && !empty($portrait_img)) : ?>
                                                <?php 
                                                // Use the team-photo size
                                                $team_image = wp_get_attachment_image(
                                                    $portrait_img['ID'],
                                                    'team-photo',
                                                    false,
                                                    array('class' => 'team-photo', 'alt' => $image_alt)
                                                );
                                                echo $team_image;
                                                ?>
                                            <?php endif; ?>
                                            <div class="gallery-content">
                                                <h3><?php echo esc_html($teacher_name); ?></h3>
                                                <p class="title"><?php echo esc_html($job_title); ?></p>
                                                <div>
                                                    <a class="margin-top-bottom-10 modal-button read-all submit-btn"
                                                        data-modal="<?php echo esc_attr($modal_id); ?>">View Bio</a>
                                                </div>
                                            </div>

                                            <!-- Modal content-->
                                            <div class="modal" id="<?php echo esc_attr($modal_id); ?>">
                                                <h3>Teacher Biography</h3>
                                                <div class="margin-bottom padding">
                                                    <h2><?php echo esc_html($teacher_name); ?>: <?php echo esc_html($job_title); ?></h2>
                                                    
                                                    <?php 
                                                    // Display the teacher bio content
                                                    the_content(); 
                                                    ?>
                                                </div>
                                                <a class="modal-close-button button cancel-btn">Close</a>
                                            </div>
                                        </div>
                                <?php
                                    endwhile;
                                    wp_reset_postdata();
                                else :
                                ?>
                                    <div class="s-12 margin-bottom-30">
                                        <p>No teachers found.</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <?php if ($total_pages > 1) : ?>
                        <!-- Pagination -->
                        <div class="line text-center pagination-container">
                            <div class="s-12 margin-bottom">
                                <ul class="pagination">
                                    <?php if ($paged > 1) : ?>
                                        <li><a href="<?php echo esc_url(get_pagenum_link($paged - 1)); ?>" class="previous-page">Prev</a></li>
                                    <?php endif; ?>
                                    
                                    <?php
                                    // Calculate pagination range
                                    $range = 2; // Number of pages to show on each side of current page
                                    $showitems = ($range * 2) + 1;
                                    
                                    // Start page
                                    if ($paged > $range + 1) {
                                        echo '<li><a href="' . esc_url(get_pagenum_link(1)) . '">1</a></li>';
                                        if ($paged > $range + 2) {
                                            echo '<li><span>...</span></li>';
                                        }
                                    }
                                    
                                    // Loop through pages
                                    for ($i = max(1, $paged - $range); $i <= min($total_pages, $paged + $range); $i++) {
                                        if ($paged == $i) {
                                            echo '<li><a href="#" class="active-page">' . $i . '</a></li>';
                                        } else {
                                            echo '<li><a href="' . esc_url(get_pagenum_link($i)) . '">' . $i . '</a></li>';
                                        }
                                    }
                                    
                                    // End page
                                    if ($paged < $total_pages - $range) {
                                        if ($paged < $total_pages - $range - 1) {
                                            echo '<li><span>...</span></li>';
                                        }
                                        echo '<li><a href="' . esc_url(get_pagenum_link($total_pages)) . '">' . $total_pages . '</a></li>';
                                    }
                                    ?>
                                    
                                    <?php if ($paged < $total_pages) : ?>
                                        <li><a href="<?php echo esc_url(get_pagenum_link($paged + 1)); ?>" class="next-page">Next</a></li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- put page content above, do not edit below this comment -->
                    </div><!-- Ends content -->

                </div>
            </div>
        </div>
    </article>
</main>

<!-- Add custom JavaScript for filtering functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get all filter buttons
    const filterButtons = document.querySelectorAll('.teacher-filter-buttons .button');
    
    // Get all teacher cards
    const teacherCards = document.querySelectorAll('.card.gallery-card[data-category]');
    
    // Function to filter teachers by category
    function filterTeachers(category) {
        // Remove active class from all buttons
        filterButtons.forEach(btn => btn.classList.remove('active'));
        
        // Add active class to the matching button
        filterButtons.forEach(btn => {
            const btnCategory = btn.textContent.trim().toLowerCase().replace(/\s+/g, '-');
            if (btnCategory === category) {
                btn.classList.add('active');
            }
        });
        
        // Show/hide cards based on category
        teacherCards.forEach(card => {
            if (category === 'all-teachers') {
                card.style.display = 'block';
            } else {
                if (card.getAttribute('data-category') === category) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            }
        });
    }
    
    // Check URL parameters for category
    const urlParams = new URLSearchParams(window.location.search);
    const categoryParam = urlParams.get('category');
    
    if (categoryParam) {
        filterTeachers(categoryParam);
    }
    
    // Add click event to each filter button
    filterButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Get the category from the button text
            const category = this.textContent.trim().toLowerCase().replace(/\s+/g, '-');
            
            // Update URL with the category parameter
            const url = new URL(window.location);
            if (category === 'all-teachers') {
                url.searchParams.delete('category');
            } else {
                url.searchParams.set('category', category);
            }
            window.history.pushState({}, '', url);
            
            // Filter teachers
            filterTeachers(category);
        });
    });
    
    // Modal functionality
    const modalButtons = document.querySelectorAll('.modal-button');
    const modalCloseButtons = document.querySelectorAll('.modal-close-button');
    
    // Open modal
    modalButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const modalId = this.getAttribute('data-modal');
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('active');
                document.body.classList.add('modal-open');
            }
        });
    });
    
    // Close modal
    modalCloseButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const modal = this.closest('.modal');
            if (modal) {
                modal.classList.remove('active');
                document.body.classList.remove('modal-open');
            }
        });
    });
    
    // Close modal when clicking outside
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal') && e.target.classList.contains('active')) {
            e.target.classList.remove('active');
            document.body.classList.remove('modal-open');
        }
    });
});
</script>

<?php get_footer(); ?>