/**
 * Custom gallery scripts for Nkhwazi Primary School
 * Overrides default lightcase settings for galleries
 */
jQuery(document).ready(function($) {
    // Wait for the DOM to be fully loaded and for any other scripts to initialize
    setTimeout(function() {
        // Override lightcase initialization for nkhwazi-gallery items
        if ($('a[data-rel^="lightcase:nkhwazi-gallery"]').length > 0) {
            console.log('Found nkhwazi-gallery items, reinitializing lightcase');
            
            // Remove default lightcase bindings for gallery items
            $('a[data-rel^="lightcase:nkhwazi-gallery"]').removeAttr('data-processed');
            
            // Re-initialize with custom settings
            $('a[data-rel^="lightcase:nkhwazi-gallery"]').lightcase({
                maxWidth: 1280,
                maxHeight: 720,
                transition: 'scrollHorizontal',
                speedIn: 600,
                speedOut: 600,
                iframe: false, // Disable iframe mode
                type: 'image', // Force image type
                shrinkFactor: 1.0,
                overlayOpacity: 0.9,
                video: {
                    width: 1280,
                    height: 720,
                    loop: true
                },
                onStart: function() {
                    console.log('Lightcase started with custom settings');
                }
            });
        }
    }, 500); // Wait 500ms to ensure all other scripts have loaded
});