<?php
/**
 * Template for displaying notification archives
 *
 * @package Nkhwazi_Primary_School
 */

get_header();
?>

<!-- MAIN -->
<main role="main">
    <!-- Content -->
    <article>
        <div class="line">
            <header class="rounded-div section background-primary background-transparent text-center"
                data-image-src="<?php echo get_template_directory_uri(); ?>/assets/img/parallax-06.webp">
                <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">School Notifications</h1>
            </header>
        </div>

        <div class="section background-white">
            <div class="line">
                <div class="margin">
                    <!-- Introduction text -->
                    <div class="s-12 m-12 l-12 margin-bottom-30">
                        <p class="text-center">Important notifications and announcements from Nkhwazi Primary School.</p>
                    </div>
                    
                    <?php if (have_posts()) : ?>
                        <!-- Notifications List -->
                        <div class="s-12 m-12 l-12">
                            <div class="grid margin">
                                <?php while (have_posts()) : the_post(); 
                                    // Get ACF fields
                                    $name = get_field('name');
                                    $description = get_field('description');
                                ?>
                                    <div class="s-12 m-6 l-4 margin-bottom-30">
                                        <div class="notification-card background-grey-hover padding-2x rounded-div box-shadow">
                                            <h3 class="text-strong text-size-20 margin-bottom-10"><?php echo esc_html($name); ?></h3>
                                            <p class="margin-bottom-0"><?php echo esc_html($description); ?></p>
                                        </div>
                                    </div>
                                <?php endwhile; ?>
                            </div>
                        </div>
                        
                        <!-- Pagination -->
                        <?php if (get_the_posts_pagination()) : ?>
                        <div class="s-12 text-center margin-top-30">
                            <?php
                            the_posts_pagination(array(
                                'mid_size' => 2,
                                'prev_text' => __('Prev', 'nkhwazischool'),
                                'next_text' => __('Next', 'nkhwazischool'),
                            ));
                            ?>
                        </div>
                        <?php endif; ?>
                        
                    <?php else : ?>
                        <div class="s-12 m-12 l-12">
                            <p class="text-center">No notifications found.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </article>
</main>

<?php get_footer(); ?>