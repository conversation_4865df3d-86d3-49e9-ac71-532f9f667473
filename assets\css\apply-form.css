/* Application Form Styles */
.application-form {
  color: #3649e2; /* Primary color */
  font-weight: 600;
}

.any-error {
  color: #C81010;
  font-weight: bold;
}

.any-success {
  color: #49BF4C;
  font-weight: bold;
}

/* Style for required fields */
form.customform input.required,
form.customform select.required,
form.customform textarea.required {
  border-left: 4px solid #C81010;
}

/* Improve form field spacing */
form.customform input,
form.customform select,
form.customform textarea {
  margin-bottom: 1rem;
}

/* Style for form section headings */
h3.application-form {
  margin-top: 2rem;
  font-size: 1.4rem;
}

h4.text-left {
  margin-top: 1.5rem;
  color: #3649e2; /* Primary color */
  font-weight: 600;
}

/* Style for submit and reset buttons */
.submit-btn {
  background-color: #3649e2 !important; /* Primary color */
  transition: background-color 0.3s ease;
}

.submit-btn:hover {
  background-color: #2a3ab3 !important; /* Darker primary color */
}

.cancel-btn {
  background-color: #C81010 !important;
  transition: background-color 0.3s ease;
}

.cancel-btn:hover {
  background-color: #a00d0d !important; /* Darker red */
}

/* Datepicker styling */
.ui-datepicker {
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 0 8px rgba(0,0,0,0.1);
  padding: 10px;
}

.ui-datepicker-header {
  background-color: #3649e2; /* Primary color */
  color: white;
  border-radius: 3px;
  padding: 5px;
}

.ui-datepicker-calendar th {
  padding: 5px;
  text-align: center;
}

.ui-datepicker-calendar td {
  padding: 2px;
  text-align: center;
}

.ui-datepicker-calendar a {
  display: block;
  padding: 5px;
  text-decoration: none;
  border-radius: 3px;
}

.ui-datepicker-calendar a:hover {
  background-color: #E2CF36; /* Secondary color */
  color: white;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .s-12 {
    margin-bottom: 10px;
  }
  
  h3.application-form {
    font-size: 1.2rem;
  }
  
  h4.text-left {
    font-size: 1.1rem;
  }
}