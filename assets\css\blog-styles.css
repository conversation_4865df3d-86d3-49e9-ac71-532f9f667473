/* Blog Styles */

/* Blog Post Styling */
.blog-post {
    transition: all 0.3s ease;
    padding: 15px;
    border-radius: 5px;
}

.blog-post:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.blog-post img.rounded-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 5px;
}

.blog-post h3 {
    margin-top: 15px;
    font-size: 1.4rem;
    line-height: 1.3;
}

.blog-post p {
    margin: 10px 0;
    line-height: 1.5;
}

.blog-author a, .blog-category a {
    color: #555;
    text-decoration: none;
    transition: color 0.3s ease;
}

.blog-author a:hover, .blog-category a:hover {
    color: #0074D9;
}

/* Pagination Styling */
.pagination {
    display: inline-block;
    padding: 0;
    margin: 20px 0;
}

.pagination li {
    display: inline;
    margin: 0 2px;
}

.pagination li a {
    color: #333;
    float: left;
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.pagination li a.active-page {
    background-color: #0074D9;
    color: white;
}

.pagination li a:hover:not(.active-page) {
    background-color: #ddd;
}

.pagination li a.previous-page,
.pagination li a.next-page {
    background-color: #f1f1f1;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
    .blog-post img.rounded-image {
        height: 180px;
    }
}

@media screen and (max-width: 480px) {
    .blog-post img.rounded-image {
        height: 160px;
    }
    
    .pagination li a {
        padding: 6px 12px;
    }
}
