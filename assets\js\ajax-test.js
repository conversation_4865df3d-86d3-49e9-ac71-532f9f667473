/**
 * AJAX Test Script
 * 
 * This script tests if AJAX is working correctly
 */
jQuery(document).ready(function($) {
    'use strict';
    
    console.log('AJAX test script loaded');
    
    // Define ajaxurl if not already defined
    if (typeof ajaxurl === 'undefined') {
        var ajaxurl = '/wp-admin/admin-ajax.php';
        console.log('Setting ajaxurl to:', ajaxurl);
    }
    
    // Add a test button to the page
    $('body').append(
        '<div id="ajax-test-container" style="position: fixed; bottom: 20px; right: 20px; z-index: 9999; background: #fff; padding: 15px; border: 1px solid #ddd; border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.1);">' +
        '<h4 style="margin-top: 0;">AJAX Test</h4>' +
        '<button id="test-ajax-button" style="background: #0073aa; color: white; border: none; padding: 8px 15px; border-radius: 3px; cursor: pointer;">Test AJAX</button>' +
        '<div id="ajax-test-result" style="margin-top: 10px;"></div>' +
        '</div>'
    );
    
    // Handle test button click
    $('#test-ajax-button').on('click', function() {
        console.log('Test AJAX button clicked');
        
        $('#ajax-test-result').html('Testing AJAX connection...');
        
        // Make a simple AJAX request
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'nkhwazi_test_ajax_connection'
            },
            success: function(response) {
                console.log('AJAX test success:', response);
                $('#ajax-test-result').html('<div style="color: green; font-weight: bold;">AJAX connection successful!</div>');
            },
            error: function(xhr, status, error) {
                console.error('AJAX test error:', status, error);
                $('#ajax-test-result').html(
                    '<div style="color: red; font-weight: bold;">AJAX connection failed!</div>' +
                    '<div>Error: ' + status + ' - ' + error + '</div>'
                );
            }
        });
    });
});