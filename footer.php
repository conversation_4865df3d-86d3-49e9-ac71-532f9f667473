<?php
/**
 * The template for displaying the footer
 *
 * Contains the closing of the #content div and all content after.
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package Nkhwazi_Primary_School
 */

?>

<!--FOOTER -->
<footer>
  <!-- Main Footer -->
  <div class="line">
    <section class="section background-blue">

      <div class="grid margin">
        <!-- Column 1 -->
        <div class="s-12 m-6 l-3 margin-m-bottom-30">
          <h4 class="text-uppercase text-strong">Contact Us</h4>
          <div class="s-12 m-12 l-12 center footer-links">
            <?php
            // Get school address
            $address = nkhwazi_get_school_address();
            
            if (!empty($address)) {
                // Address section
                echo '<div class="grid text-left">';
                echo '<div class="s-12 m-12 l-1 text-left">';
                echo '<i class="icon-sli-location-pin text-strong text-secondary text-size-20"></i>';
                echo '</div>';
                echo '<div class="s-11 m-11 l-11 margin-bottom-10">';
                echo '<div class="admin-name margin-left-10">';
                
                // Display address lines
                if (!empty($address['line1'])) {
                    echo esc_html($address['line1']);
                    if (!empty($address['line2'])) {
                        echo ', ' . esc_html($address['line2']);
                    }
                }
                
                echo '</div>';
                echo '</div>';
                echo '</div><br>';
                
                // Phone and email section
                echo '<div class="grid text-left">';
                
                // Phone numbers
                if (!empty($address['phone1']) || !empty($address['phone2'])) {
                    echo '<div class="s-12 m-12 l-1 text-left">';
                    echo '<i class="icon-sli-call-in text-strong text-secondary text-size-18 margin-bottom-10"></i>';
                    echo '</div>';
                    
                    if (!empty($address['phone1'])) {
                        echo '<div class="s-11 m-11 l-11 margin-bottom-10">';
                        echo '<div class="margin-left-10"><a href="tel:' . esc_attr(preg_replace('/\s+/', '', $address['phone1'])) . '">' . esc_html($address['phone1']) . '</a></div>';
                        echo '</div>';
                    }
                    
                    if (!empty($address['phone2'])) {
                        echo '<div class="s-11 m-11 l-11 margin-bottom-10">';
                        echo '<div class="margin-left-10"><a href="tel:' . esc_attr(preg_replace('/\s+/', '', $address['phone2'])) . '">' . esc_html($address['phone2']) . '</a></div>';
                        echo '</div>';
                    }
                }
                
                // Email (only if display_email is true)
                if (!empty($address['email']) && !empty($address['display_email'])) {
                    echo '<div class="s-12 m-12 l-1 text-left">';
                    echo '<i class="icon-sli-envelope text-strong text-secondary text-size-18"></i>';
                    echo '</div>';
                    echo '<div class="s-11 m-11 l-11 margin-bottom-10">';
                    echo '<div class="margin-left-10"><a href="mailto:' . esc_attr($address['email']) . '">' . esc_html($address['email']) . '</a></div>';
                    echo '</div>';
                }
                
                echo '</div><br>';
            } else {
                // Fallback if no address is found
                echo '<div class="grid text-left">';
                echo '<div class="s-12 m-12 l-12 margin-bottom-10">';
                echo '<div class="admin-name">Contact information not available</div>';
                echo '</div>';
                echo '</div><br>';
            }
            ?>

            <div class="grid text-left">
              <div class="s-12 m-12 l-1 text-left">
                <i class="icon-facebook text-strong text-secondary text-size-18"></i>
              </div>
              <div class="s-11 m-11 l-11">
                <div class="margin-left-10"><a href="<?php echo esc_url(get_permalink(238)); ?>" class="text-secondary-hover"><b>&nbsp;Facebook</b></a></div>
              </div>
            </div><br>
          </div>

          <div class="grid text-left">
          <div class="s-12 m-12 l-1 text-left margin-top-10">
            <i class="icon-sli-target text-strong text-secondary text-size-18"></i>
            </div>
          <div class="s-12 m-12 l-11 margin-left-10">
            <div class='margin-top-10'><span class="tooltip-container"><span class="tooltip-content tooltip-top">

                  <span class='hide-l hide-xl hide-xxl'><iframe
                      src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3846.2284300191445!2d28.294016364850272!3d-15.41821758928599!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1940f353839a7ea7%3A0x8a82f68499432955!2sNkhwazi+Primary+School%2C+Ituna+Rd%2C+Lusaka%2C+Zambia!5e0!3m2!1sen!2suk!4v1523659030624"
                      width="350" height="300" frameborder="0" style="border:0" allowfullscreen>
                    </iframe></span>
                  <span class='hide-s hide-m'><iframe
                      src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3846.2284300191445!2d28.294016364850272!3d-15.41821758928599!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1940f353839a7ea7%3A0x8a82f68499432955!2sNkhwazi+Primary+School%2C+Ituna+Rd%2C+Lusaka%2C+Zambia!5e0!3m2!1sen!2suk!4v1523659030624"
                      width="800" height="450" frameborder="0" style="border:0" allowfullscreen>
                    </iframe></span>

                </span>Location (Google Map) <i class="fa fa-map-marker fa-lg" aria-hidden="true"
                  style="color: #00815f"></i></span>
            </div>
          </div>
        </div>

        </div>

        <!-- Column 2 -->
        <div class="s-12 m-6 l-3 margin-m-bottom-30">
          <h4 class="text-uppercase text-strong">About Us</h4>
          <div class="s-12 m-12 l-9 center">
            <div class="s-12 m-12 l-12 text-left footer-links">
              <?php
              if (has_nav_menu('about-us')) {
                  wp_nav_menu(array(
                      'theme_location' => 'about-us',
                      'container' => false,
                      'menu_class' => 'footer-menu',
                      'fallback_cb' => false,
                      'depth' => 1,
                      'items_wrap' => '%3$s',
                      'walker' => new Footer_Menu_Walker()
                  ));
              } else {
                  // Fallback if no menu is assigned
                  ?>
                  <p><a href="<?php echo esc_url(get_permalink(238)); ?>">Our History</a></p>
                  <p><a href="<?php echo esc_url(get_permalink(238)); ?>">Mission &amp; Vision</a></p>
                  <p><a href="<?php echo esc_url(get_permalink(238)); ?>">Why Choose Us</a></p>
                  <p><a href="<?php echo esc_url(get_permalink(241)); ?>">Latest News</a></p>
              <?php } ?>
            </div>
          </div>
        </div>

        <!-- Column 3 -->
        <div class="s-12 m-6 l-3 margin-m-bottom-30">
          <h4 class="text-uppercase text-strong">Useful Links</h4>
          <div class="s-12 m-12 l-9 center">
            <div class="s-12 m-12 l-12 text-left footer-links">
              <?php
              if (has_nav_menu('useful-links')) {
                  wp_nav_menu(array(
                      'theme_location' => 'useful-links',
                      'container' => false,
                      'menu_class' => 'footer-menu',
                      'fallback_cb' => false,
                      'depth' => 1,
                      'items_wrap' => '%3$s',
                      'walker' => new Footer_Menu_Walker()
                  ));
              } else {
                  // Fallback if no menu is assigned
                  ?>
                  <p><a href="<?php echo esc_url(get_permalink(380)); ?>">Apply Online</a></p>
                  <p><a href="<?php echo esc_url(get_permalink(398)); ?>">Update Pupil's Details</a></p>
                  <p><a href="<?php echo esc_url(get_permalink(238)); ?>">FAQs</a></p>
                  <p><a href="<?php echo esc_url(get_permalink(238)); ?>">Fees</a></p>
              <?php } ?>
            </div>
          </div>
        </div>

        <!-- Column 4 -->
        <div class="s-12 m-6 l-3 margin-m-bottom-30">
          <h4 class="text-uppercase text-strong">Legal</h4>
          <div class="s-12 m-12 l-7 center">
            <div class="s-12 m-12 l-12 text-left footer-links">
              <?php
              if (has_nav_menu('legal-links')) {
                  wp_nav_menu(array(
                      'theme_location' => 'legal-links',
                      'container' => false,
                      'menu_class' => 'footer-menu',
                      'fallback_cb' => false,
                      'depth' => 1,
                      'items_wrap' => '%3$s',
                      'walker' => new Footer_Menu_Walker()
                  ));
              } else {
                  // Fallback if no menu is assigned
                  ?>
                  <p><a href="<?php echo esc_url(home_url('/privacy-policy/')); ?>">Privacy Policy</a></p>
                  <p><a href="<?php echo esc_url(home_url('/terms-of-use/')); ?>">Terms of Use</a></p>
                  <p><a href="<?php echo esc_url(home_url('/cookies/')); ?>">Cookies</a></p>
                  <p><a href="<?php echo esc_url(home_url('/disclaimer/')); ?>">Disclaimer</a></p>
              <?php } ?>
            </div>
          </div>
        </div>

      </div>

      <div class="admin-contact-info">
        <div class="grid margin">
          <?php
          // Get admin contacts
          $admin_contacts = nkhwazi_get_admin_contacts();
          
          if (!empty($admin_contacts)) {
              $count = 0;
              $total = count($admin_contacts);
              $max_display = 3; // Maximum number of contacts to display
              
              foreach ($admin_contacts as $contact) {
                  if ($count >= $max_display) break;
                  
                  echo '<div class="s-12 m-6 l-4 admin-info-item">';
                  echo '<div class="grid">';
                  
                  // Job Title
                  echo '<div class="s-12 m-12 l-6 admin-name text-center">' . esc_html($contact['job_title']) . '</div>';
                  
                  // Phone Number
                  if (!empty($contact['phone'])) {
                      echo '<div class="s-12 m-12 l-6 admin-phone text-center text-yellow">';
                      // Clean phone number for tel: link (remove spaces, parentheses, dashes, etc.)
                      $clean_phone = preg_replace('/[^0-9+]/', '', $contact['phone']);
                      echo '<a href="tel:' . esc_attr($clean_phone) . '" class="text-yellow call-button">';
                      echo '<i class="icon-sli-phone text-size-14 margin-right-10"></i>' . esc_html($contact['phone']);
                      echo '</a>';
                      echo '</div>';
                  }
                  
                  echo '</div>';
                  echo '</div>';
                  
                  $count++;
              }
              
              // Fill remaining slots with empty divs if needed
              while ($count < $max_display) {
                  echo '<div class="s-12 m-6 l-4 admin-info-item"></div>';
                  $count++;
              }
          } else {
              // Fallback if no admin contacts are found
              echo '<div class="s-12 m-12 l-12 text-center">';
              echo '<p class="text-white">No admin contacts available</p>';
              echo '</div>';
          }
          ?>
        </div>
      </div>
    </section>

    <hr class="break margin-top-bottom-0" style="border-color: rgba(255, 255, 255);">

    <!-- Bottom Footer -->
    <section class="padding background-yellow bottom-footer">
      <div class="grid margin">
        <div class="s-12 m-6 l-6">
          <p class="text-size-16 text-blue">&copy; 2012 - <?php echo date("Y"); ?>, Nkhwazi Primary School</p>
        </div>
        <div class="s-12 m-6 l-6 developer-credit">
          <a class="text-size-12 text-blue" href="http://www.kalcomputaz.com"
            title="KAL Computaz is registered and domiciled in Zambia">Developed By Kal Computaz</a>
        </div>
      </div>
    </section>
  </div>
</footer>

<?php wp_footer(); ?>

<script>
jQuery(document).ready(function($) {
  // Initialize countTo for stats
  if ($('.timer').length > 0) {
    console.log('Timer elements found:', $('.timer').length);
    var hasStarted = false;
    
    // Improved viewport detection - element just needs to be partially visible
    var isInViewport = function(elem) {
      var bounding = elem.getBoundingClientRect();
      var windowHeight = window.innerHeight || document.documentElement.clientHeight;
      var windowWidth = window.innerWidth || document.documentElement.clientWidth;
      
      return (
        bounding.top < windowHeight &&
        bounding.bottom > 0 &&
        bounding.left < windowWidth &&
        bounding.right > 0
      );
    };
    
    // Function to start counting for all timers
    var startAllCounters = function() {
      if (hasStarted) return; // Prevent multiple initializations
      
      var statsSection = $('.nkhwazi-stats');
      if (statsSection.length > 0 && isInViewport(statsSection[0])) {
        console.log('Starting counters...');
        hasStarted = true;
        
        $('.timer').each(function() {
          var $this = $(this);
          var dataTo = parseInt($this.attr('data-to')) || 0;
          var dataSpeed = parseInt($this.attr('data-speed')) || 2000;
          
          // Set initial value to 0
          $this.text('0');
          
          // Start counting
          $this.countTo({
            from: 0,
            to: dataTo,
            speed: dataSpeed,
            refreshInterval: 50,
            onComplete: function() {
              // Ensure final value is displayed correctly
              $this.text(dataTo);
            }
          });
        });
        
        // Remove scroll listener once counting has started
        $(window).off('scroll.statsCounter');
      }
    };
    
    // Use setTimeout to ensure DOM is fully rendered
    setTimeout(function() {
      // Check immediately on page load
      startAllCounters();
      
      // Also check on scroll
      $(window).on('scroll.statsCounter', startAllCounters);
      
      // Also check when window is fully loaded
      $(window).on('load', function() {
        setTimeout(startAllCounters, 500);
      });
      
      // Fallback: start counters after 3 seconds if they haven't started yet
      setTimeout(function() {
        if (!hasStarted) {
          hasStarted = true;
          $('.timer').each(function() {
            var $this = $(this);
            var dataTo = parseInt($this.attr('data-to')) || 0;
            var dataSpeed = parseInt($this.attr('data-speed')) || 2000;
            
            $this.text('0');
            $this.countTo({
              from: 0,
              to: dataTo,
              speed: dataSpeed,
              refreshInterval: 50,
              onComplete: function() {
                $this.text(dataTo);
              }
            });
          });
        }
      }, 3000);
    }, 100);
  }
});
</script>

</body>
</html>