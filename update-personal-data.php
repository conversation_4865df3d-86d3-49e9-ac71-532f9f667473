<div class="line">
           <div class="margin">
            <div class="s-12 m-5 l-5">
                <?php
                   //Last Name
                   require('includes/select_lname.php');
                ?>
         </div>
         <div class="s-12 m-5 l-7">
              <?php
                   //First Name
                   require('includes/select_fname.php');
                ?>
         </div>
      </div>
      </div>
    <!-- second Row -->
        <div class="line">
          <div class="margin">

            <div class="s-12 m-6 l-3">
               <?php
                   //Date of Birth
                   require('includes/select_dob.php');
                ?>
                  <!--You can format the date the datepicker gives in many ways like:   $('#datepicker').datepicker({ dateFormat: 'dd/mm/yy' });
                  any combination using dd-mm-yy because mysql uses '-' separator so '/' may not work well but can still be used as long as you use a function to convert the date to mysql formart -->
                   <script>
                    $('#datepicker').datepicker({ dateFormat: 'dd-mm-yy' });

                  </script>

           </div><!--closes input -->
           <div class="s-12 m-6 l-3">
              <?php echo form_label("<span class='margin-bottom-10'>Grade</span>"); ?>
                <select name="grade" class="required border-radius" required> 
                   <?php
                      //Grade select
                      require('includes/select_grade.php');
                   ?>
               </select>
         </div>
         <div class="s-12 m-12 l-6">
                   <?php
                      //Grade select
                      require('includes/select_religion.php');
                   ?>
         </div>
          </div>
        </div>

       <!-- Third row -->
   <div class="line">
      <div class="margin">
        <div class="s-12 m-12 l-12">
              <?php
                   //handicap
                   require('includes/select_address.php');
              ?>
        </div>
      </div>
   </div>
  <!-- Forth row -->
    <div class="line">
      <div class="margin">
           <div class="s-12 m-12 l-12">
              <?php
                  //Father phone number
                  require('includes/select_siblings.php');
               ?>
           </div>
      </div>
   </div>
    
      <h2 class="application-form s-12 m-12 l-12 margin-bottom-10 text-center">Father or Male Guardian&rsquo;s Details</h2>
      <hr>
      <!-- Fifth Row -->
      <div class="line">
        <div class="margin">
              <div class="s-12 m-6 l-5">
                  <?php
                   //Last Name Father
                   require('includes/select_lname_f.php');
                  ?>
           </div>
           <div class="s-12 m-6 l-7">
                 <?php
                    //Last Name Father
                    require('includes/select_fname_f.php');
                  ?>
           </div>
        </div>
      </div>
        <!-- sixth Row -->
        <div class="line">
          <div class="margin">
            <div class="s-12 m-5 l-3">
                <?php
                   //Occupation Father
                   require('includes/select_occupation_f.php');
                 ?>
            </div>
         <div class="s-12 m-7 l-9">
             <?php
                  //Employer Father
                  require('includes/select_address_business_f.php');
              ?>
        </div>
          </div>
        </div>
  <!-- seventh Row -->
      <div class="line">
         <div class="margin">
          <div class="s-12 m-6 l-3">
              <?php
                  //Address for Father
                  require('includes/select_phone_f.php');
              ?>
        </div>
        <div class="s-12 m-6 l-3">
              <?php
                  //Address for Father
                  require('includes/select_phone_work_f.php');
              ?>
        </div>
        <div class="s-12 m-12 l-6">
              <?php
                  //Address for Father
                  require('includes/select_email_f.php');
              ?>
        </div>
  </div>
  </div>

<h2 class="application-form s-12 m-12 l-12 margin-bottom-10 text-center">Mother or Female Guardian&rsquo;s Details</h2>
      <hr>
      <!-- Fifth Row -->
      <div class="line">
        <div class="margin">
              <div class="s-12 m-6 l-5">
                  <?php
                   //Last Name Father
                   require('includes/select_lname_m.php');
                  ?>
           </div>
           <div class="s-12 m-6 l-7">
                 <?php
                    //Last Name Father
                    require('includes/select_fname_m.php');
                  ?>
           </div>
        </div>
      </div>
        <!-- sixth Row -->
        <div class="line">
          <div class="margin">
            <div class="s-12 m-4 l-3">
                <?php
                   //Occupation Father
                   require('includes/select_occupation_m.php');
                 ?>
            </div>
         <div class="s-12 m-8 l-9">
             <?php
                  //Employer Father
                  require('includes/select_address_business_m.php');
              ?>
        </div>
          </div>
        </div>
  <!-- seventh Row -->
      <div class="line">
         <div class="margin">
          <div class="s-12 m-6 l-3">
              <?php
                  //Address for Father
                  require('includes/select_phone_m.php');
              ?>
        </div>
        <div class="s-12 m-6 l-3">
              <?php
                  //Address for Father
                  require('includes/select_phone_work_m.php');
              ?>
        </div>
        <div class="s-12 m-12 l-6">
              <?php
                  //Address for Father
                  require('includes/select_email_m.php');
              ?>
        </div>
  </div>
  </div>

<h2 class="application-form s-12 m-12 l-12 margin-bottom-10 text-center">Family Doctor or Clinic<span class='tooltip-container'><span class='tooltip-content tooltip-top'>This  can be a named Doctor or a Clinic depending on your Health Scheme</span> <i class='icon-information_black text-green text-red-hover'></i></span></h2>
<hr>

<div class="line">
  <div class="margin">
      <div class="s-12 m-12 l-12">
          <?php
            //Last Name Father
            require('includes/select_family_doctor.php');
          ?>
      </div>
  </div>
</div>
<div class="line">
         <div class="margin">
          <div class="s-12 m-6 l-3">
              <?php
                  //Address for Father
                  require('includes/select_phone_doctor.php');
              ?>
        </div>
        <div class="s-12 m-6 l-3">
              <?php
                  //Address for Father
                  require('includes/select_tel_doctor.php');
              ?>
        </div>
        <div class="s-12 m-12 l-6">
              <?php
                  //Address for Father
                  require('includes/select_email_doctor.php');
              ?>
        </div>
  </div>
</div>

<h2 class="application-form s-12 m-12 l-12 margin-bottom-10 text-center">In Case of Emergency, Contact<span class='tooltip-container'><span class='tooltip-content tooltip-top'>In Case of Emergency, if Parents or Guardians are unreachable, we will contact this person so please provide more than one way to contact them to increase the chance of reaching her/ him</span> <i class='icon-information_black text-green text-red-hover'></i></span></h2>
<hr>

  <div class="line">
    <div class="margin">
          <div class="s-12 m-6 l-5">
              <?php
                //Last Name Father
                require('includes/select_lname_emergency.php');
              ?>
        </div>
        <div class="s-12 m-6 l-7">
              <?php
                //Last Name Father
                require('includes/select_fname_emergency.php');
              ?>
        </div>
    </div>
  </div>

     <div class="line">
         <div class="margin">
          <div class="s-12 m-12 l-3">
              <?php
                  //Address for Father
                  require('includes/select_phone_emergency.php');
              ?>
        </div>
        <div class="s-12 m-6 l-3">
              <?php
                  //Address for Father
                  require('includes/select_tel_emergency.php');
              ?>
        </div>
        <div class="s-12 m-6 l-6">
              <?php
                  //Address for Father
                  require('includes/select_email_emergency.php');
              ?>
        </div>
  </div>
</div>

<h2 class="application-form s-12 m-12 l-12 margin-bottom-10 text-center">Additional Information</h2><hr>
   <div class="line">
      <div class="margin"> 
           <div class="s-12 m-12 l-12">
                  <?php
                     //Siblings at shool
                      require('includes/select_more_info.php');
                  ?>
           </div>
      </div>
    </div>

      <div class='line'>
           <!-- message ends -->
           <div class="margin-left-10 margin">
          <?php
          //get the captcha widget
           echo $this->recaptcha->getWidget();
          //render the script
           echo $this->recaptcha->getScriptTag();
          ?>
        </div>
      </div>
      <br>
      <div class="margin">
             <div class="s-12 m-6 l-3 left"><button class="submit-form submit-btn button border-radius text-white" type="">Submit</button></div>
             <div class="s-12 m-6 l-3 right"><button class="reset-form cancel-btn button border-radius text-white " type="reset">Reset</button></div>
        </div>