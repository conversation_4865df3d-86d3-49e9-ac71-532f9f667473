<?php
/**
 * Template Name: Teaching Staff
 * Description: A template for displaying teaching staff organized by categories (Lower Primary, Middle Primary, Upper Primary).
 *              Includes filtering options and staff cards with modal biographies.
 * Author: <PERSON>
 */
require_once('includes/header.php');
?>
<!-- MAIN -->
<main role="main">
    <!-- third section -->
    <article>
        <div class="line">
            <header class="rounded-div section background-primary background-transparent text-center"
                data-image-src="<?php echo get_template_directory_uri(); ?>/assets/img/parallax-05.webp">
                <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">Teaching Staff</h1>
            </header>
        </div>

        <!-- Breadcrumb Navigation -->
        <?php get_template_part('includes/breadcrumb'); ?>

        <div class="section background-white">
            <div class="line">
                <div class="margin">
                    <div class="s-12 m-12 l-12">
                        <!-- Gutenberg content from the page -->
                        <div class="line margin-bottom-20">
                            <?php 
                            while (have_posts()) :
                                the_post();
                                the_content();
                            endwhile;
                            ?>
                        </div>
                        
                        <?php
                        // Get all school categories for filtering
                        $school_categories = get_posts(array(
                            'post_type' => 'school_categories',
                            'posts_per_page' => -1,
                            'orderby' => 'title',
                            'order' => 'ASC'
                        ));
                        
                        // Get current page for pagination
                        $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
                        
                        // Get teachers per page from theme settings or default to 12
                        $teachers_per_page = 8;
                        
                        // Get the current page URL
                        $current_page_url = get_permalink(376); // Teaching Staff page ID
                        
                        // Check if a category filter is active
                        $active_category = isset($_GET['staff_category']) ? sanitize_text_field($_GET['staff_category']) : '';
                        $category_id = 0;
                        
                        // If we have an active category, get its ID
                        if (!empty($active_category)) {
                            foreach ($school_categories as $cat) {
                                $cat_slug = sanitize_title(get_the_title($cat));
                                if ($cat_slug === $active_category) {
                                    $category_id = $cat->ID;
                                    break;
                                }
                            }
                        }
                        
                        // Count total teachers for pagination (filtered by category if needed)
                        if ($category_id > 0) {
                            // Count teachers in this category
                            $category_query = new WP_Query(array(
                                'post_type' => 'teacher',
                                'posts_per_page' => -1,
                                'fields' => 'ids',
                                'meta_query' => array(
                                    array(
                                        'key' => 'school_category',
                                        'value' => '"' . $category_id . '"',
                                        'compare' => 'LIKE'
                                    )
                                )
                            ));
                            $total_teachers = $category_query->found_posts;
                        } else {
                            // Count all teachers
                            $total_teachers = wp_count_posts('teacher')->publish;
                        }
                        
                        $total_pages = ceil($total_teachers / $teachers_per_page);
                        ?>
                        
                        <!-- Teacher Filter Categories -->
                        <div class="line margin-bottom-30">
                            <div class="rounded-div background-primary-hightlight padding">
                                <div class="s-12 text-center">
                                    <p class="category-filter margin-bottom-10">Filter Staff Members:</p>
                                    <div class="teacher-filter-buttons">
                                        <?php 
                                        // Check if we're on the main view (no category filter)
                                        $is_all_active = empty($active_category);
                                        ?>
                                        <a href="<?php echo esc_url($current_page_url); ?>" 
                                           class="rounded-div button teacher-filter-btn margin-right-10 <?php echo $is_all_active ? 'active' : ''; ?>">
                                            All Teachers
                                        </a>
                                        <?php foreach ($school_categories as $category) : 
                                            $category_name = get_the_title($category);
                                            $category_slug = sanitize_title($category_name);
                                            $is_active = ($active_category === $category_slug);
                                            $category_url = add_query_arg('staff_category', $category_slug, $current_page_url);
                                        ?>
                                            <a href="<?php echo esc_url($category_url); ?>" 
                                               class="rounded-div button teacher-filter-btn margin-right-10 <?php echo $is_active ? 'active' : ''; ?>">
                                                <?php echo esc_html($category_name); ?>
                                            </a>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="line">
                            <!-- Team Cards -->
                            <div class="grid margin">
                                <?php
                                // Query teachers
                                $args = array(
                                    'post_type' => 'teacher',
                                    'posts_per_page' => $teachers_per_page,
                                    'paged' => $paged,
                                    'meta_key' => 'priority',
                                    'orderby' => array(
                                        'meta_value_num' => 'ASC',
                                        'title' => 'ASC'
                                    )
                                );
                                
                                // Add category filter if needed
                                if ($category_id > 0) {
                                    $args['meta_query'] = array(
                                        array(
                                            'key' => 'school_category',
                                            'value' => '"' . $category_id . '"',
                                            'compare' => 'LIKE'
                                        )
                                    );
                                }
                                
                                $teachers_query = new WP_Query($args);
                                
                                if ($teachers_query->have_posts()) :
                                    $teacher_count = 0;
                                    
                                    while ($teachers_query->have_posts()) : $teachers_query->the_post();
                                        $teacher_count++;
                                        
                                        // Get teacher custom fields
                                        $title = get_field('title');
                                        $first_name = get_field('first_name');
                                        $last_name = get_field('last_name');
                                        $job_title = get_field('job_title');
                                        $bio_excerpt = get_field('bio_excerpt');
                                        $portrait_img = get_field('portrait_img');
                                        
                                        // Get school category
                                        $school_category = get_field('school_category');
                                        $category_name = '';
                                        $category_slug = '';
                                        
                                        if ($school_category && !empty($school_category)) {
                                            $category_obj = $school_category[0]; // Get first category
                                            $category_name = get_the_title($category_obj);
                                            $category_slug = sanitize_title($category_name);
                                        }
                                        
                                        // Generate unique modal ID
                                        $modal_id = 'modal-teacher-' . get_the_ID();
                                        
                                        // Get teacher full name
                                        $teacher_name = $first_name . ' ' . $last_name;
                                        
                                        // Get teacher image
                                        $image_alt = $teacher_name;
                                        
                                        if ($portrait_img && !empty($portrait_img)) {
                                            if (!empty($portrait_img['alt'])) {
                                                $image_alt = $portrait_img['alt'];
                                            }
                                        }
                                ?>
                                        <!-- Teacher Card -->
                                        <div class="card gallery-card s-12 m-6 l-3" data-category="<?php echo esc_attr($category_slug); ?>">
                                            <div class="teacher-category-header">
                                                <?php 
                                                // Get the actual permalink for the school category post
                                                if ($school_category && !empty($school_category)) {
                                                    $category_obj = $school_category[0]; // Get first category
                                                    $category_link_url = get_permalink($category_obj->ID);
                                                } else {
                                                    // Fallback to school categories page
                                                    $category_link_url = get_permalink(328);
                                                }
                                                ?>
                                                <a href="<?php echo esc_url($category_link_url); ?>" class="category-link">
                                                    <?php echo esc_html($category_name); ?>
                                                </a>
                                            </div>
                                            <?php if ($portrait_img && !empty($portrait_img)) : ?>
                                                <?php 
                                                // Use the team-photo size
                                                $team_image = wp_get_attachment_image(
                                                    $portrait_img['ID'],
                                                    'team-photo',
                                                    false,
                                                    array('class' => 'team-photo', 'alt' => $image_alt)
                                                );
                                                echo $team_image;
                                                ?>
                                            <?php endif; ?>
                                            <div class="gallery-content">
                                                <h3><?php echo esc_html($teacher_name); ?></h3>
                                                <p class="title"><?php echo esc_html($job_title); ?></p>
                                                <div>
                                                    <a class="margin-top-bottom-10 modal-button read-all submit-btn"
                                                        data-modal="<?php echo esc_attr($modal_id); ?>">View Bio</a>
                                                </div>
                                            </div>

                                            <!-- Modal content-->
                                            <div class="modal" id="<?php echo esc_attr($modal_id); ?>">
                                                <h3>Teacher Biography</h3>
                                                <div class="margin-bottom padding">
                                                    <h2><?php echo esc_html($teacher_name); ?>: <?php echo esc_html($job_title); ?></h2>
                                                    
                                                    <?php 
                                                    // Display the teacher bio content
                                                    the_content(); 
                                                    ?>
                                                </div>
                                                <a class="modal-close-button button cancel-btn">Close</a>
                                            </div>
                                        </div>
                                <?php
                                    endwhile;
                                    wp_reset_postdata();
                                else :
                                ?>
                                    <div class="s-12 margin-bottom-30">
                                        <p>No teachers found.</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <?php if ($total_pages > 1) : ?>
                        <!-- Pagination -->
                        <div class="line text-center pagination-container">
                            <div class="s-12 margin-bottom">
                                <ul class="pagination">
                                    <?php
                                    // Create pagination links that preserve the category parameter
                                    $pagination_args = array(
                                        'base' => str_replace(999999999, '%#%', esc_url(get_pagenum_link(999999999))),
                                        'format' => '?paged=%#%',
                                        'current' => max(1, $paged),
                                        'total' => $total_pages,
                                        'prev_text' => '&laquo; ' . __('Previous', 'nkhwazischool'),
                                        'next_text' => __('Next', 'nkhwazischool') . ' &raquo;',
                                        'mid_size' => 2,
                                        'end_size' => 1,
                                        'type' => 'list'
                                    );
                                    
                                    // Add the staff_category parameter to the pagination links if it exists
                                    if (!empty($active_category)) {
                                        $pagination_args['add_args'] = array(
                                            'staff_category' => sanitize_text_field($active_category)
                                        );
                                    }
                                    
                                    echo paginate_links($pagination_args);
                                    ?>
                                </ul>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- put page content above, do not edit below this comment -->
                    </div><!-- Ends content -->

                </div>
            </div>
        </div>
    </article>
</main>

<!-- Add custom JavaScript for modal functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    
    // Modal functionality
    const modalButtons = document.querySelectorAll('.modal-button');
    const modalCloseButtons = document.querySelectorAll('.modal-close-button');
    
    // Open modal
    modalButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const modalId = this.getAttribute('data-modal');
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('active');
                document.body.classList.add('modal-open');
            }
        });
    });
    
    // Close modal
    modalCloseButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const modal = this.closest('.modal');
            if (modal) {
                modal.classList.remove('active');
                document.body.classList.remove('modal-open');
            }
        });
    });
    
    // Close modal when clicking outside
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal') && e.target.classList.contains('active')) {
            e.target.classList.remove('active');
            document.body.classList.remove('modal-open');
        }
    });
});
</script>

<?php get_footer(); ?>