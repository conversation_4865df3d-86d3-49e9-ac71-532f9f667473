<?php
/**
 * SCF (Secure Custom Field) Fields for Nkhwazi Primary School
 */

// Register SCF fields
function nkhwazi_register_scf_fields() {
    if (!function_exists('acf_add_local_field_group')) {
        return;
    }

    /**
 * Custom validation for minimum and maximum character count
 * works for various post types enforcing max and min length set
 * on a post type field.
 *
 * @param mixed $valid Whether the value is valid
 * @param mixed $value The field value
 * @param array $field The field array
 * @param string $input The field input name
 * @return mixed
 */
function nkhwazischool_validate_min_chars($valid, $value, $field, $input) {
    // If value is already invalid, return the error
    if ($valid !== true) {
        return $valid;
    }
    
    // Skip validation if the field is empty and not required
    if (empty(trim($value)) && !$field['required']) {
        return $valid;
    }

    // Check if min is set and the value is less than min
    if (isset($field['min']) && strlen(trim($value)) < $field['min']) {
        return sprintf('The %s field must be at least %d characters', $field['label'], $field['min']);
    }

    return $valid;
}
// Apply to both textarea and text fields
add_filter('acf/validate_value/type=textarea', 'nkhwazischool_validate_min_chars', 10, 4);
add_filter('acf/validate_value/type=text', 'nkhwazischool_validate_min_chars', 10, 4);
add_filter('acf/validate_value/type=url', 'nkhwazischool_validate_min_chars', 10, 4);

/**
 * Custom validation for maximum character count
 * works for various post types enforcing max length set
 * on a post type field.
 *
 * @param mixed $valid Whether the value is valid
 * @param mixed $value The field value
 * @param array $field The field array
 * @param string $input The field input name
 * @return mixed
 */
function nkhwazischool_validate_max_chars($valid, $value, $field, $input) {
    // If value is already invalid, return the error
    if ($valid !== true) {
        return $valid;
    }
    
    // Skip validation if the field is empty and not required
    if (empty(trim($value)) && !$field['required']) {
        return $valid;
    }

    // Check if max is set and the value is more than max
    if (isset($field['max']) && strlen(trim($value)) > $field['max']) {
        return sprintf('The %s field must not exceed %d characters', $field['label'], $field['max']);
    }

    return $valid;
}
// Apply to both textarea and text fields
add_filter('acf/validate_value/type=textarea', 'nkhwazischool_validate_max_chars', 10, 4);
add_filter('acf/validate_value/type=text', 'nkhwazischool_validate_max_chars', 10, 4);
add_filter('acf/validate_value/type=url', 'nkhwazischool_validate_max_chars', 10, 4);

/**
 * Custom validation for future dates
 * Ensures that dates selected are in the future
 *
 * @param mixed $valid Whether the value is valid
 * @param mixed $value The field value
 * @param array $field The field array
 * @param string $input The field input name
 * @return mixed
 */
function nkhwazischool_validate_future_date($valid, $value, $field, $input) {
    // If value is already invalid, return the error
    if ($valid !== true) {
        return $valid;
    }
    
    // Skip if empty and not required
    if (empty($value) && !$field['required']) {
        return $valid;
    }
    
    // Convert the selected date to a timestamp
    $selected_date = strtotime($value);
    $today = strtotime(date('Y-m-d'));
    
    // Check if the selected date is today or in the past
    if ($selected_date <= $today) {
        return 'Please select a future date (tomorrow or later) for the event.';
    }
    
    return $valid;
}
// Apply to date picker fields
add_filter('acf/validate_value/type=date_picker', 'nkhwazischool_validate_future_date', 10, 4);

    // Job Post Type Fields
    acf_add_local_field_group(array(
        'key' => 'group_job',
        'title' => 'Job Details',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'job',
                ),
            ),
        ),
        'fields' => array(
            array(
                'key' => 'field_job_reference',
                'label' => 'Job Reference',
                'name' => 'job_reference',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Enter a unique reference code for this job (e.g., MT2023-01)',
                'min' => 3,
                'max' => 20,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_job_deadline',
                'label' => 'Application Deadline',
                'name' => 'job_deadline',
                'type' => 'date_picker',
                'required' => 1,
                'instructions' => 'Select the application deadline date',
                'display_format' => 'F j, Y',
                'return_format' => 'F j, Y',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_job_excerpt',
                'label' => 'Job Excerpt',
                'name' => 'job_excerpt',
                'type' => 'textarea',
                'required' => 1,
                'instructions' => 'A brief summary of the job (40-200 characters)',
                'min' => 40,
                'max' => 200,
                'rows' => 3,
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
            array(
                'key' => 'field_job_how_to_apply',
                'label' => 'How to Apply',
                'name' => 'job_how_to_apply',
                'type' => 'textarea',
                'required' => 1,
                'instructions' => 'Instructions for applicants on how to apply for this job',
                'min' => 20,
                'max' => 500,
                'rows' => 4,
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
            array(
                'key' => 'field_job_status',
                'label' => 'Job Status',
                'name' => 'job_status',
                'type' => 'select',
                'required' => 1,
                'choices' => array(
                    'active' => 'Active',
                    'filled' => 'Filled',
                    'expired' => 'Expired',
                    'draft' => 'Draft',
                ),
                'default_value' => 'active',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
        ),
    ));

    // Management Post Type Fields
    acf_add_local_field_group(array(
        'key' => 'group_management',
        'title' => 'Management Details',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'management',
                ),
            ),
        ),
        'fields' => array(
            array(
                'key' => 'field_management_types',
                'label' => 'Management Type',
                'name' => 'management_types',
                'type' => 'post_object',
                'post_type' => array('management_type'),
                'return_format' => 'object',
                'required' => 1,
                'multiple' => 0,
                'ui' => 1,
                'instructions' => 'Select a management type',
                'wrapper' => array(
                    'width' => '50',
                ),
                'message' => 'No management types available. Please create some first.',
                'allow_null' => 1,
                'placeholder' => 'Select an option',
            ),
            array(
                'key' => 'field_title',
                'label' => 'Title',
                'name' => 'title',
                'type' => 'select',
                'choices' => array(
                    'Mr' => 'Mr',
                    'Mrs' => 'Mrs',
                    'Ms' => 'Ms',
                    'Miss' => 'Miss',
                    'Dr' => 'Dr',
                ),
                'required' => 1,
                'default_value' => '',
                'wrapper' => array(
                    'width' => '10',
                ),
            ),
            array(
                'key' => 'field_first_name',
                'label' => 'First Name',
                'name' => 'first_name',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Between 2-30 characters',
                'min' => 2,
                'max' => 30,
                'wrapper' => array(
                    'width' => '40',
                ),
            ),
            array(
                'key' => 'field_last_name',
                'label' => 'Last Name',
                'name' => 'last_name',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Between 2-30 characters',
                'min' => 2,
                'max' => 30,
                'wrapper' => array(
                    'width' => '40',
                ),
            ),
            array(
                'key' => 'field_position',
                'label' => 'Position',
                'name' => 'position',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Between 3-50 characters',
                'min' => 3,
                'max' => 50,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_portrait_img',
                'label' => 'Portrait Image',
                'name' => 'portrait_img',
                'type' => 'image',
                'required' => 1,
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_bio_excerpt',
                'label' => 'Bio Excerpt',
                'name' => 'bio_excerpt',
                'type' => 'textarea',
                'required' => 1,
                'instructions' => 'Between 40-120 characters',
                'min' => 40,
                'max' => 120,
                'rows' => 4,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_priority',
                'label' => 'Priority',
                'name' => 'priority',
                'type' => 'number',
                'required' => 0,
                'instructions' => 'Used when listing items (1-3 characters, optional)',
                'min' => 1,
                'max' => 999,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
        ),
    ));

    // Teacher Post Type Fields
    acf_add_local_field_group(array(
        'key' => 'group_teacher',
        'title' => 'Teacher Details',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'teacher',
                ),
            ),
        ),
        'fields' => array(
            array(
                'key' => 'field_teacher_title',
                'label' => 'Title',
                'name' => 'title',
                'type' => 'select',
                'choices' => array(
                    'Mr' => 'Mr',
                    'Mrs' => 'Mrs',
                    'Ms' => 'Ms',
                    'Miss' => 'Miss',
                    'Dr' => 'Dr',
                ),
                'required' => 1,
                'default_value' => '',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_teacher_first_name',
                'label' => 'First Name',
                'name' => 'first_name',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Between 2-30 characters',
                'min' => 2,
                'max' => 30,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_teacher_last_name',
                'label' => 'Last Name',
                'name' => 'last_name',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Between 2-30 characters',
                'min' => 2,
                'max' => 30,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_teacher_job_title',
                'label' => 'Job Title',
                'name' => 'job_title',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Between 2-50 characters',
                'min' => 2,
                'max' => 50,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_teacher_bio_excerpt',
                'label' => 'Bio Excerpt',
                'name' => 'bio_excerpt',
                'type' => 'textarea',
                'required' => 0,
                'instructions' => 'Between 40-120 characters (optional)',
                'min' => 40,
                'max' => 120,
                'rows' => 4,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_teacher_school_category',
                'label' => 'School Category',
                'name' => 'school_category',
                'type' => 'relationship',
                'required' => 1,
                'post_type' => array('school_categories'),
                'return_format' => 'object',
                //only search is available to select related item
                'filters' => array(
                    'search' => 1,
                    'post_type' => 0,
                    'taxonomy' => 0,
                ),
                'elements' => array('featured_image'),
                'min' => 1,
                'max' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_teacher_portrait_img',
                'label' => 'Portrait Image',
                'name' => 'portrait_img',
                'type' => 'image',
                'required' => 1,
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_teacher_priority',
                'label' => 'Priority',
                'name' => 'priority',
                'type' => 'number',
                'required' => 0,
                'instructions' => 'Used when listing items (1-3 characters, optional)',
                'min' => 1,
                'max' => 999,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
        ),
    ));

    // School Categories Post Type Fields
    acf_add_local_field_group(array(
        'key' => 'group_school_categories',
        'title' => 'School Category Details',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'school_categories',
                ),
            ),
        ),
        'fields' => array(
            array(
                'key' => 'field_school_category',
                'label' => 'School Category',
                'name' => 'school_category',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Between 6-30 characters',
                'min' => 6,
                'max' => 30,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_category_excerpt',
                'label' => 'Category Excerpt',
                'name' => 'category_excerpt',
                'type' => 'textarea',
                'required' => 1,
                'instructions' => 'Between 60-120 characters',
                'min' => 60,
                'max' => 120,
                'rows' => 4,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_grade_range',
                'label' => 'Grade Range',
                'name' => 'grade_range',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Between 5-20 characters',
                'min' => 5,
                'max' => 20,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_age_range',
                'label' => 'Age Range',
                'name' => 'age_range',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Between 5-15 characters',
                'min' => 5,
                'max' => 15,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_card_img',
                'label' => 'Card Image',
                'name' => 'card_img',
                'type' => 'image',
                'required' => 1,
                'instructions' => 'Appears on School Category Card',
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_page_image',
                'label' => 'Page Image',
                'name' => 'page_image',
                'type' => 'image',
                'required' => 0,
                'instructions' => 'Optional page header image (1280 x 500 pixels recommended)',
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
        ),
    ));

    // Frontpage Slideshow Post Type Fields
    acf_add_local_field_group(array(
        'key' => 'group_frontpage_slideshow',
        'title' => 'Slideshow Details',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'frontpage_slideshow',
                ),
            ),
        ),
        'fields' => array(
            array(
                'key' => 'field_slider_image',
                'label' => 'Slider Image',
                'name' => 'slider_image',
                'type' => 'image',
                'required' => 1,
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_slider_heading',
                'label' => 'Slider Heading',
                'name' => 'slider_heading',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Between 4-40 characters',
                'min' => 4,
                'max' => 40,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_slider_description',
                'label' => 'Slider Description',
                'name' => 'slider_description',
                'type' => 'textarea',
                'required' => 1,
                'instructions' => 'Between 20-120 characters',
                'min' => 20,
                'max' => 120,
                'rows' => 4,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_slider_url',
                'label' => 'Slider URL',
                'name' => 'slider_url',
                'type' => 'url',
                'required' => 0,
                'instructions' => 'Optional link for the slide (max 160 characters)',
                'max' => 160,
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
        ),
    ));

    // Upcoming Event Post Type Fields
    acf_add_local_field_group(array(
        'key' => 'group_upcoming_event',
        'title' => 'Event Details',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'upcoming_event',
                ),
            ),
        ),
        'fields' => array(
            array(
                'key' => 'field_due_date',
                'label' => 'Due Date',
                'name' => 'due_date',
                'type' => 'date_picker',
                'required' => 1,
                'instructions' => 'Select a future date (tomorrow or later) for the event (format: DD-MM-YY)',
                'display_format' => 'd-m-y',
                'return_format' => 'd-m-y',
                'first_day' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_event_name',
                'label' => 'Event Name',
                'name' => 'event_name',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Between 4-60 characters',
                'min' => 4,
                'max' => 60,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_event_excerpt',
                'label' => 'Event Excerpt',
                'name' => 'event_excerpt',
                'type' => 'textarea',
                'required' => 1,
                'instructions' => 'Between 80-160 characters',
                'min' => 80,
                'max' => 160,
                'rows' => 4,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_event_archived',
                'label' => 'Archive',
                'name' => 'archived',
                'type' => 'true_false',
                'required' => 0,
                'default_value' => 0,
                'ui' => 1,
                'ui_on_text' => 'Yes',
                'ui_off_text' => 'No',
                'instructions' => 'Toggle to archive this event',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
        ),
    ));

    // Photo Gallery Post Type Fields
    acf_add_local_field_group(array(
        'key' => 'group_photo_gallery',
        'title' => 'Gallery Details',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'photo_gallery',
                ),
            ),
        ),
        'fields' => array(
            array(
                'key' => 'field_gallery_name',
                'label' => 'Gallery Name',
                'name' => 'gallery_name',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Between 4-50 characters',
                'min' => 4,
                'max' => 50,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_gallery_description',
                'label' => 'Gallery Description',
                'name' => 'gallery_description',
                'type' => 'textarea',
                'required' => 1,
                'instructions' => 'Between 60-120 characters',
                'min' => 60,
                'max' => 120,
                'rows' => 3,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_gallery_card',
                'label' => 'Gallery Card Image',
                'name' => 'gallery_card',
                'type' => 'image',
                'required' => 1,
                'instructions' => 'Upload an image for the gallery card (will be cropped to 496 x 279)',
                'return_format' => 'array',
                'preview_size' => 'gallery-card',
                'library' => 'all',
                'min_width' => 496,
                'min_height' => 279,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_gallery',
                'label' => 'Gallery',
                'name' => 'gallery',
                'type' => 'gallery',
                'required' => 1,
                'instructions' => 'Add images to the gallery by clicking the "Add to gallery" button',
                'min' => '',
                'max' => '',
                'insert' => 'append',
                'library' => 'all',
                'min_width' => '',
                'min_height' => '',
                'min_size' => '',
                'max_width' => '',
                'max_height' => '',
                'max_size' => '',
                'mime_types' => '',
            ),
            array(
                'key' => 'field_gallery_archived',
                'label' => 'Archive',
                'name' => 'archived',
                'type' => 'true_false',
                'required' => 0,
                'default_value' => 0,
                'ui' => 1,
                'ui_on_text' => 'Yes',
                'ui_off_text' => 'No',
                'instructions' => 'Toggle to archive this gallery',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
        ),
    ));

    // Address Post Type Fields
    acf_add_local_field_group(array(
        'key' => 'group_address',
        'title' => 'Address Details',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'address',
                ),
            ),
        ),
        'fields' => array(
            array(
                'key' => 'field_address_line1',
                'label' => 'Address Line 1',
                'name' => 'address_line1',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Between 4-60 characters',
                'min' => 4,
                'max' => 60,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_address_line2',
                'label' => 'Address Line 2',
                'name' => 'address_line2',
                'type' => 'text',
                'required' => 0,
                'instructions' => 'Between 2-40 characters (optional)',
                'min' => 2,
                'max' => 40,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_phone1',
                'label' => 'Phone 1',
                'name' => 'phone1',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Between 8-16 characters',
                'min' => 8,
                'max' => 16,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_phone2',
                'label' => 'Phone 2',
                'name' => 'phone2',
                'type' => 'text',
                'required' => 0,
                'instructions' => 'Between 8-16 characters (optional)',
                'min' => 8,
                'max' => 16,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_address_email',
                'label' => 'Email',
                'name' => 'email',
                'type' => 'email',
                'required' => 0,
                'instructions' => 'Valid email address (optional)',
                'wrapper' => array(
                    'width' => '70',
                ),
            ),
            array(
                'key' => 'field_address_display_email',
                'label' => 'Display Email Publicly',
                'name' => 'display_email',
                'type' => 'true_false',
                'required' => 0,
                'instructions' => 'Enable this to display the email address on the website. Disable to protect against spam.',
                'default_value' => 0,
                'ui' => 1,
                'ui_on_text' => 'Yes',
                'ui_off_text' => 'No',
                'wrapper' => array(
                    'width' => '30',
                ),
            ),
        ),
    ));

    // Admin Contact Info Post Type Fields
    acf_add_local_field_group(array(
        'key' => 'group_admin_contact_info',
        'title' => 'Admin Contact Details',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'admin_contact_info',
                ),
            ),
        ),
        'fields' => array(
            array(
                'key' => 'field_admin_priority',
                'label' => 'Priority',
                'name' => 'priority',
                'type' => 'number',
                'required' => 1,
                'instructions' => 'Lower numbers will be displayed first (e.g., 1 is highest priority)',
                'default_value' => 10,
                'min' => 1,
                'max' => 100,
                'step' => 1,
                'wrapper' => array(
                    'width' => '25',
                ),
            ),
            array(
                'key' => 'field_admin_job_title',
                'label' => 'Job Title',
                'name' => 'job_title',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Between 3-30 characters',
                'min' => 3,
                'max' => 30,
                'wrapper' => array(
                    'width' => '75',
                ),
            ),
            array(
                'key' => 'field_admin_phone',
                'label' => 'Phone',
                'name' => 'phone',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Between 8-16 characters',
                'min' => 8,
                'max' => 16,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_admin_email',
                'label' => 'Email',
                'name' => 'email',
                'type' => 'email',
                'required' => 1,
                'instructions' => 'Valid email',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_admin_display',
                'label' => 'Display Email Publicly',
                'name' => 'display',
                'type' => 'true_false',
                'required' => 0,
                'instructions' => 'Enable this to display the email address on the website. Disable to protect against spam.',
                'default_value' => 0,
                'ui' => 1,
                'ui_on_text' => 'Yes',
                'ui_off_text' => 'No',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
        ),
    ));

    // Nkhwazi Stats Post Type Fields
    acf_add_local_field_group(array(
        'key' => 'group_nkhwazi_stats',
        'title' => 'Stat Details',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'nkhwazi_stats',
                ),
            ),
        ),
        'fields' => array(
            array(
                'key' => 'field_stat_body',
                'label' => 'Stat Body',
                'name' => 'stat_body',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Between 4-30 characters',
                'min' => 4,
                'max' => 30,
                'wrapper' => array(
                    'width' => '70',
                ),
            ),
            array(
                'key' => 'field_stat_number',
                'label' => 'Stat Number',
                'name' => 'stat_number',
                'type' => 'number',
                'required' => 1,
                'instructions' => 'Between 1-5 characters',
                'min' => 1,
                'max' => 99999,
                'wrapper' => array(
                    'width' => '30',
                ),
            ),
            array(
                'key' => 'field_stat_is_percentage',
                'label' => 'Is Percentage?',
                'name' => 'stat_is_percentage',
                'type' => 'select',
                'required' => 1,
                'instructions' => 'Select whether this stat should be displayed as a percentage',
                'choices' => array(
                    'no' => 'No',
                    'yes' => 'Yes',
                ),
                'default_value' => 'no',
                'return_format' => 'value',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
        ),
    ));

    // Extracurricular Post Type Fields
    acf_add_local_field_group(array(
        'key' => 'group_extracurricular',
        'title' => 'Extracurricular Details',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'extracurricular',
                ),
            ),
        ),
        'fields' => array(
            array(
                'key' => 'field_extracurricular_type',
                'label' => 'Type',
                'name' => 'type',
                'type' => 'text',
                'required' => 1,
                'min' => 3,
                'max' => 40,
                'instructions' => 'e.g. sports, school clubs, dancing, 3 - 40 characters',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_extracurricular_image',
                'label' => 'Card Image',
                'name' => 'image',
                'type' => 'image',
                'required' => 1,
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
                'instructions' => 'This image will be used for the extracurricular card (480 x 270 pixels)',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_extracurricular_excerpt',
                'label' => 'Excerpt',
                'name' => 'excerpt',
                'type' => 'textarea',
                'required' => 1,
                'instructions' => 'Between 30-140 characters',
                'min' => 30,
                'max' => 140,
                'rows' => 3,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_extracurricular_archived',
                'label' => 'Archived',
                'name' => 'archived',
                'type' => 'true_false',
                'required' => 0,
                'default_value' => 0,
                'ui' => 1,
                'wrapper' => array(
                    'width' => '30',
                ),
            ),
            array(
                'key' => 'field_extracurricular_page_image',
                'label' => 'Page Image',
                'name' => 'page_image',
                'type' => 'image',
                'required' => 0,
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
                'instructions' => 'Optional page header image (1280 x 500 pixels recommended)',
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
        ),
    ));

    // Extracurricular Item Post Type Fields
    acf_add_local_field_group(array(
        'key' => 'group_extracurricular_item',
        'title' => 'Item Details',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'extracurricular_item',
                ),
            ),
        ),
        'fields' => array(
            array(
                'key' => 'field_item_name',
                'label' => 'Name',
                'name' => 'name',
                'type' => 'text',
                'required' => 1,
                'min' => 4,
                'max' => 60,
                'instructions' => 'e.g. football tournament, singing Competition, dance competition, science club event 4 - 60 characters',
                  
                'wrapper' => array(
                    'width' => '60',
                ),
            ),
            array(
                'key' => 'field_item_location',
                'label' => 'Location',
                'name' => 'location',
                'type' => 'text',
                'required' => 1,
                'min' => 4,
                'max' => 30,
                'instructions' => 'where does this take place? 4 - 30 characters',
                'wrapper' => array(
                    'width' => '40',
                ),
            ),
            array(
                'key' => 'field_item_card_image',
                'label' => 'Card Image',
                'name' => 'card_image',
                'type' => 'image',
                'required' => 1,
                'instructions' => 'This appears on the extracurricular card, needs cropping',
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_item_page_image',
                'label' => 'Page Image',
                'name' => 'page_image',
                'type' => 'image',
                'required' => 0,
                'instructions' => 'Optional page header image (1280 x 500 pixels recommended)',
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_item_scope',
                'label' => 'Item Scope',
                'name' => 'item_scope',
                'type' => 'select',
                'choices' => array(
                    'internal' => 'Internal',
                    'external' => 'External',
                ),
                'required' => 1,
                'instructions' => 'Select internal or external',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_item_excerpt',
                'label' => 'Excerpt',
                'name' => 'excerpt',
                'type' => 'textarea',
                'required' => 1,
                'instructions' => 'Between 30-120 characters',
                'min' => 30,
                'max' => 120,
                'rows' => 3,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_item_date',
                'label' => 'Date',
                'name' => 'date',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Between 4-30 characters',
                'min' => 4,
                'max' => 30,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_item_extracurricular_type',
                'label' => 'Extracurricular Type',
                'name' => 'extracurricular_type',
                'type' => 'relationship',
                'required' => 1,
                'post_type' => array('extracurricular'),
                'return_format' => 'object',
                //restrict ways to select related item
                'filters' => array(
                    'search' => 1,
                    'post_type' => 0,
                    'taxonomy' => 0,
                ),
                'elements' => array('featured_image'),
                'min' => 1,
                'max' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_item_gallery',
                'label' => 'Item Gallery',
                'name' => 'item_gallery',
                'type' => 'gallery',
                'required' => 0,
                'instructions' => 'Add images to the gallery by clicking the "Add to gallery" button',
                'min' => '',
                'max' => '',
                'insert' => 'append',
                'library' => 'all',
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
            array(
                'key' => 'field_item_featured',
                'label' => 'Featured',
                'name' => 'featured',
                'type' => 'true_false',
                'required' => 0,
                'default_value' => 0,
                'ui' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_item_archived',
                'label' => 'Archived',
                'name' => 'archived',
                'type' => 'true_false',
                'required' => 0,
                'default_value' => 0,
                'ui' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
        ),
    ));

    // Job Post Type Fields
    acf_add_local_field_group(array(
        'key' => 'group_job',
        'title' => 'Job Details',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'job',
                ),
            ),
        ),
        'fields' => array(
            array(
                'key' => 'field_job_title',
                'label' => 'Job Title',
                'name' => 'job_title',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Between 3-30 characters',
                'min' => 3,
                'max' => 30,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_job_reference',
                'label' => 'Job Reference',
                'name' => 'job_reference',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Between 2-20 characters',
                'min' => 2,
                'max' => 20,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_job_excerpt',
                'label' => 'Job Excerpt',
                'name' => 'job_excerpt',
                'type' => 'textarea',
                'required' => 1,
                'instructions' => 'Between 80-160 characters',
                'min' => 80,
                'max' => 160,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),

            array(
                'key' => 'field_deadline',
                'label' => 'Deadline',
                'name' => 'deadline',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Between 6-20 characters',
                'min' => 6,
                'max' => 20,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_how_to_apply',
                'label' => 'How to Apply',
                'name' => 'how_to_apply',
                'type' => 'textarea',
                'required' => 1,
                'instructions' => 'Between 10-1000 characters',
                'min' => 10,
                'max' => 1000,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
        ),
    ));

    // Page Post Type Fields
    acf_add_local_field_group(array(
        'key' => 'group_page_fields',
        'title' => 'Page Additional Fields',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'page',
                ),
            ),
        ),
        'fields' => array(
            array(
                'key' => 'field_page_image',
                'label' => 'Page Image',
                'name' => 'page_image',
                'type' => 'image',
                'required' => 0,
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
                'instructions' => 'Optional page header image (1280 x 500 pixels recommended)',
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
        ),
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
    ));

    // Blog Post Type Fields
    acf_add_local_field_group(array(
        'key' => 'group_blog',
        'title' => 'Blog Post Details',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'post',
                ),
            ),
        ),
        'fields' => array(
            array(
                'key' => 'field_blog_image',
                'label' => 'Blog Image',
                'name' => 'blog_image',
                'type' => 'image',
                'required' => 1,
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
                'instructions' => 'Select a featured image for this blog post (720 x 405 pixels recommended)',
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
            array(
                'key' => 'field_blog_archived',
                'label' => 'Archive',
                'name' => 'archived',
                'type' => 'true_false',
                'required' => 0,
                'default_value' => 0,
                'ui' => 1,
                'ui_on_text' => 'Yes',
                'ui_off_text' => 'No',
                'instructions' => 'Toggle to archive this blog post',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
        ),
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
    ));
    
    // Management Type Post Type Fields
    acf_add_local_field_group(array(
        'key' => 'group_management_type',
        'title' => 'Management Type Details',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'management_type',
                ),
            ),
        ),
        'fields' => array(
            array(
                'key' => 'field_management_type_name',
                'label' => 'Name',
                'name' => 'management_type_name',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Between 3-40 characters',
                'min' => 3,
                'max' => 40,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_management_type_priority',
                'label' => 'Priority',
                'name' => 'priority',
                'type' => 'number',
                'required' => 0,
                'instructions' => 'Used when listing items (1-3 characters, optional)',
                'min' => 1,
                'max' => 999,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_management_type_description',
                'label' => 'Description',
                'name' => 'description',
                'type' => 'textarea',
                'required' => 1,
                'instructions' => 'A brief description of this management type from 40 to 300 characters',
                'min' => 40,
                'max' => 300,
                'rows' => 3,
            ),
            array(
                'key' => 'field_management_type_governance',
                'label' => 'Governance',
                'name' => 'governance',
                'type' => 'true_false',
                'required' => 0,
                'instructions' => 'Toggle ON if this management type falls under governance (board members, trustees, etc.) rather than direct school operations',
                'default_value' => 0,
                'ui' => 1,
                'ui_on_text' => 'Yes',
                'ui_off_text' => 'No',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
        ),
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
    ));
}
add_action('init', 'nkhwazi_register_scf_fields');