<?php
/**
 * Template Name: Governance
 * Description: Displays only management members that have the governance toggle set to true.
 *              Uses a balanced layout with a main content area (9 columns) and a sidebar (3 columns).
 * Author: <PERSON>
 */

// Add page-specific styles
echo '<style>
main.page-governance .gallery-card .gallery-content { padding: 10px 10px 5px !important; }
main.page-governance .gallery-card .gallery-content .title { margin-bottom: 5px !important; }
main.page-governance .gallery-card .gallery-content .read-all { margin-bottom: 5px !important; margin-top: 5px !important; }
main.page-governance .gallery-card .gallery-content h3 { margin-bottom: 5px !important; }
</style>';

get_header();

// Get the current page ID
$page_id = get_the_ID();

// Check if a specific management type is requested via URL parameter
$requested_type = isset($_GET['type']) ? sanitize_text_field($_GET['type']) : '';

// Get management types to organize members by type, but only those marked as governance
$management_types = get_posts(array(
    'post_type' => 'management_type',
    'posts_per_page' => -1,
    'orderby' => 'meta_value_num',
    'meta_key' => 'priority',
    'order' => 'ASC',
    'meta_query' => array(
        array(
            'key' => 'governance',
            'value' => '1',
            'compare' => '='
        )
    )
));

// If no specific type is requested, find the type with priority 1 or use the first type
$default_type = null;
if (empty($requested_type)) {
    foreach ($management_types as $type) {
        $priority = get_field('priority', $type->ID);
        if ($priority == 1) {
            $default_type = $type;
            break;
        }
    }
    
    // If no type with priority 1 is found, use the first type
    if (!$default_type && !empty($management_types)) {
        $default_type = $management_types[0];
    }
}

?>
<!-- MAIN -->
<main role="main" class="page-governance">
    <!-- third section -->
    <article>
        <div class="line">
            <header class="rounded-div section background-primary background-transparent text-center"
                data-image-src="<?php echo get_template_directory_uri(); ?>/assets/img/parallax-04.webp">
                <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1"><?php the_title(); ?></h1>
            </header>
        </div>

        <!-- Breadcrumb Navigation -->
        <?php get_template_part('includes/breadcrumb'); ?>

        <div class="section background-white">
            <div class="line">
                <div class="margin">
                    <div class="s-12 m-12 l-12">
                        <!-- put page content below, do not edit above this comment -->
                        
                        <!-- Governance Filter Categories -->
                        <div class="line margin-bottom-30">
                            <div class="rounded-div background-primary-hightlight padding">
                                <div class="s-12 text-center">
                                    <p class="category-filter margin-bottom-10">Filter Governance Members:</p>
                                    <div class="teacher-filter-buttons">
                                        <?php 
                                        // Get the current page URL
                                        $current_page_url = get_permalink();
                                        
                                        // Get governance management types
                                        $governance_types = get_posts(array(
                                            'post_type' => 'management_type',
                                            'posts_per_page' => -1,
                                            'orderby' => 'meta_value_num',
                                            'meta_key' => 'priority',
                                            'order' => 'ASC',
                                            'meta_query' => array(
                                                array(
                                                    'key' => 'governance',
                                                    'value' => '1',
                                                    'compare' => '='
                                                )
                                            )
                                        ));
                                        ?>
                                        <?php 
                                        // If no type is requested, set the first one as active
                                        $active_type_slug = !empty($requested_type) ? $requested_type : 
                                            (isset($management_types[0]) ? sanitize_title(get_field('management_type_name', $management_types[0]->ID)) : '');
                                        
                                        foreach ($governance_types as $type) : 
                                            $type_name = get_field('management_type_name', $type->ID);
                                            $type_slug = sanitize_title($type_name);
                                            $is_active = ($active_type_slug === $type_slug);
                                            $type_url = add_query_arg('type', $type_slug, $current_page_url);
                                        ?>
                                            <a href="<?php echo esc_url($type_url); ?>" 
                                               class="rounded-div button teacher-filter-btn margin-right-10 <?php echo $is_active ? 'active' : ''; ?>">
                                                <?php echo esc_html($type_name); ?>
                                            </a>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                    <!-- Governance contents-->
                        <div class="line">
                            
                            <?php
                            // Determine which types to display
                            $types_to_display = array();
                            
                            if (!empty($requested_type)) {
                                // If a specific type is requested, find it by slug
                                foreach ($management_types as $type) {
                                    $type_name = get_field('management_type_name', $type->ID);
                                    $type_slug = sanitize_title($type_name);
                                    
                                    if ($type_slug === $requested_type) {
                                        $types_to_display[] = $type;
                                        break;
                                    }
                                }
                            } elseif (!empty($default_type)) {
                                // If no specific type is requested, use the default type
                                $types_to_display[] = $default_type;
                            }
                            
                            // If no types were found, show the first governance type (fallback)
                            if (empty($types_to_display) && !empty($management_types)) {
                                $types_to_display[] = $management_types[0];
                            }
                            
                            // Loop through each management type to display
                            foreach ($types_to_display as $type) :
                                // Get the management type name
                                $type_name = get_field('management_type_name', $type->ID);
                                $type_slug = sanitize_title($type_name);
                                
                                // Query management members of this type
                                $management_members = get_posts(array(
                                    'post_type' => 'management',
                                    'posts_per_page' => -1,
                                    'meta_query' => array(
                                        array(
                                            'key' => 'management_types',
                                            'value' => $type->ID,
                                            'compare' => '='
                                        )
                                    ),
                                    'orderby' => 'meta_value_num',
                                    'meta_key' => 'priority',
                                    'order' => 'ASC',
                                ));
                                
                                // Only display section if there are members
                                if (!empty($management_members)) :
                            ?>
                            
                            <h2 id="<?php echo esc_attr($type_slug); ?>" class="text-size-30 margin-bottom-10"><?php echo esc_html($type_name); ?></h2>
                            
                            <?php 
                            // Display description if available
                            $description = get_field('description', $type->ID);
                            if (!empty($description)) : 
                            ?>
                            <div class="margin-bottom-20">
                                <?php echo wpautop(esc_html($description)); ?>
                            </div>
                            <?php endif; ?>
                            
                            <!-- Team Cards -->
                            <div class="grid margin">
                                <?php 
                                // Loop through each management member
                                foreach ($management_members as $member) :
                                    // Get member details
                                    $title = get_field('title', $member->ID);
                                    $first_name = get_field('first_name', $member->ID);
                                    $last_name = get_field('last_name', $member->ID);
                                    $position = get_field('position', $member->ID);
                                    $portrait = get_field('portrait_img', $member->ID);
                                    $bio_excerpt = get_field('bio_excerpt', $member->ID);
                                    $full_name = $first_name . ' ' . $last_name;
                                    
                                    // Create a unique modal ID
                                    $modal_id = 'modal-' . $member->ID;
                                ?>
                                
                                <!-- <?php echo esc_html($position); ?> -->
                                <div class="card gallery-card s-12 m-6 l-3">
                                    <a href="#">
                                        <?php if ($portrait) : ?>
                                            <?php 
                                            // Use the team-photo size
                                            $team_image = wp_get_attachment_image(
                                                $portrait['ID'],
                                                'team-photo',
                                                false,
                                                array('class' => 'team-photo rounded-image', 'alt' => $full_name)
                                            );
                                            echo $team_image;
                                            ?>
                                        <?php else : ?>
                                            <img src="<?php echo get_template_directory_uri(); ?>/assets/img/team/placeholder.jpg" 
                                                 alt="<?php echo esc_attr($full_name); ?>" 
                                                 class="team-photo rounded-image">
                                        <?php endif; ?>
                                    </a>
                                    <div class="gallery-content">
                                        <h3><?php echo esc_html($title . ' ' . $first_name . ' ' . $last_name); ?></h3>
                                        <p class="title"><?php echo esc_html($position); ?></p>
                                        <div class="margin-top-20">
                                            <a class="modal-button read-all submit-btn"
                                                data-modal="<?php echo esc_attr($modal_id); ?>">View Bio</a>
                                        </div>
                                    </div>

                                    <!-- Modal content-->
                                    <div class="modal" id="<?php echo esc_attr($modal_id); ?>">
                                        <h3>Biography Container</h3>
                                        <div class="margin-bottom padding">
                                            <h2><?php echo esc_html($title . ' ' . $first_name . ' ' . $last_name); ?></h2>
                                            <h3><?php echo esc_html($position); ?></h3>

                                            <?php echo wpautop($member->post_content); ?>
                                        </div>
                                        <a class="modal-close-button button cancel-btn">Close the Bio</a>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <?php 
                                endif; // End if !empty management_members
                            endforeach; // End foreach management_types 
                            ?>
                        </div>

                        <?php
                        // Check if pagination is needed (future implementation)
                        $total_members = wp_count_posts('management')->publish;
                        if ($total_members > 12) : // Only show pagination if more than 12 members
                        ?>
                        <!-- Pagination -->
                        <div class="line text-center pagination-container">
                            <div class="s-12 margin-bottom">
                                <ul class="pagination">
                                    <li><a href="#" class="previous-page">Prev</a></li>
                                    <li><a href="#" class="active-page">1</a></li>
                                    <li><a href="#">2</a></li>
                                    <li><a href="#" class="next-page">Next</a></li>
                                </ul>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- put page content above, do not edit below this comment -->
                    </div><!-- Ends content -->

                </div>
            </div>
        </div>
    </article>
</main>

<?php
// Add JavaScript for client-side redirect if no type is specified
if (empty($requested_type) && !empty($default_type)) {
    $type_name = get_field('management_type_name', $default_type->ID);
    $type_slug = sanitize_title($type_name);
    $redirect_url = add_query_arg('type', $type_slug, get_permalink());
    ?>
    <script>
    // Use a small timeout to ensure the page loads first
    setTimeout(function() {
        window.location.href = "<?php echo esc_url($redirect_url); ?>";
    }, 100);
    </script>
    <?php
}
?>

<?php get_footer(); ?>