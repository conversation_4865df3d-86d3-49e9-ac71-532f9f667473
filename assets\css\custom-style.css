@charset "UTF-8";
/**
 * SCSS Custom Styles for Nkhwazi Primary School Website
 * custom-style.scss Compiles to custom-style.css
 * Author: <PERSON>
 * Version 1.00
 * Year: 2025
 */
/*
//Ubuntu styles
.ubuntu-light {
  font-family: "Ubuntu", sans-serif;
  font-weight: 300;
  font-style: normal;
}

.ubuntu-regular {
  font-family: "Ubuntu", sans-serif;
  font-weight: 400;
  font-style: normal;
}

.ubuntu-medium {
  font-family: "Ubuntu", sans-serif;
  font-weight: 500;
  font-style: normal;
}

.ubuntu-bold {
  font-family: "Ubuntu", sans-serif;
  font-weight: 700;
  font-style: normal;
}

.ubuntu-light-italic {
  font-family: "Ubuntu", sans-serif;
  font-weight: 300;
  font-style: italic;
}

.ubuntu-regular-italic {
  font-family: "Ubuntu", sans-serif;
  font-weight: 400;
  font-style: italic;
}

.ubuntu-medium-italic {
  font-family: "Ubuntu", sans-serif;
  font-weight: 500;
  font-style: italic;
}

.ubuntu-bold-italic {
  font-family: "Ubuntu", sans-serif;
  font-weight: 700;
  font-style: italic;
}

*/
/* Noto serif styles */
/**
 *
.noto-serif-georgian-<uniquifier> {
  font-family: "Noto Serif Georgian", serif;
  font-optical-sizing: auto;
  font-weight: <weight>;
  font-style: normal;
  font-variation-settings: "wdth" 100;
}
 */
/**
  * Primary Color
  *HEX Code: #3649e2
  *RGB Code: rgb(54,73,226)
  *complementary color
  *Secondary Color: #E2CF36;
  *Secondary color RGB Code: (rgb(226, 207, 54))
 */
.text-yellow-darker {
  color: rgb(171, 153, 34) !important;
}

.text-primary {
  color: rgb(54, 73, 226) !important;
}

/*General Styles*/
body,
p {
  font-family: "ubuntu", sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 1rem;
  text-align: left;
}

/*** Headings */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: rgb(226, 207, 54);
  font-family: "Noto Serif Georgian", serif;
  font-optical-sizing: auto;
  font-weight: 600;
  font-style: normal;
  line-height: 1.3;
  font-variation-settings: "wdth" 100;
  margin: 0.5rem 0;
  text-align: left;
}

h1 {
  font-size: 2.2rem;
  text-align: center;
}

h2 {
  font-size: 1.6rem;
}

h3 {
  font-size: 1.3rem;
}

h4 {
  font-size: 1.1rem;
}

/*** Lists */
/**targeting ONLY content within the main part of the page */
main li {
  font-size: 1rem;
  list-style-type: disc;
}

main ul,
main ol {
  margin-top: 1rem;
  margin-bottom: 1rem;
  margin-left: 1rem;
}

/*
 * Custom styles for Nkhwazi Primary School website
 */
/* Form progress steps */
.form-progress {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
  position: relative;
  /* Remove any secondary color line */
}
.form-progress::after {
  display: none !important;
}

/* Additional rule to target any potential secondary color line */
.form-progress::after {
  display: none !important;
}

.form-progress-step {
  position: relative;
  z-index: 2;
  text-align: center;
  transition: all 0.3s ease;
}
.form-progress-step.clickable {
  cursor: pointer;
}
.form-progress-step.clickable:hover .form-progress-step-number {
  background-color: #3649e2;
  color: white;
  transform: scale(1.1);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}
.form-progress-step.clickable:hover .form-progress-step-label {
  color: #3649e2;
  font-weight: bold;
}
.form-progress-step.active .form-progress-step-number {
  background-color: #3649e2;
  color: white;
  transform: scale(1.1);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}
.form-progress-step.active .form-progress-step-label {
  color: #3649e2;
  font-weight: bold;
}
.form-progress-step.completed .form-progress-step-number {
  background-color: #2ecc71;
  color: white;
}

/* The form-progress-step-number styles are defined in application-form.css */
.form-progress-step-label {
  font-size: 14px;
  color: #666;
}

/* Form steps */
.form-step {
  display: none !important;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.form-step.active {
  display: block !important;
  opacity: 1;
}

/**Header styles */
.website-name {
  color: rgb(54, 73, 226) !important;
}

.rounded-div {
  border-radius: 10px;
  overflow: hidden;
  /* Add word breaking properties to prevent text overflow */
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  -webkit-hyphens: auto;
          hyphens: auto;
}

/* Management sidebar specific styles */
.management-sidebar a {
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  -webkit-hyphens: auto;
          hyphens: auto;
  display: block;
  width: 100%;
}

/* Logo and school name container */
.nav-text {
  background: none repeat scroll 0 0 rgb(54, 73, 226);
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

.logo-container .logo {
  margin: 2px;
}

.logo-container h1 {
  margin: 0;
  color: rgb(54, 73, 226);
  font-size: 2.2rem;
  text-align: left;
  font-weight: 600;
}

/* Centered menu styles */
.centered-menu {
  display: flex;
  justify-content: center; /* Changed from left to center */
  flex-wrap: wrap;
  float: none !important;
}

.centered-menu > li {
  float: none !important;
  display: inline-block;
}

/* WordPress Menu Styles */
.top-nav ul.menu {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  float: none !important;
  margin: 0;
  padding: 0;
}

.top-nav ul.menu > li {
  float: none !important;
  display: inline-block;
  position: relative;
}

.top-nav ul.menu > li > a {
  display: block;
  padding: 0.7rem 1.25rem;
  text-decoration: none;
  color: #444;
  font-size: 1rem;
  transition: color 0.2s linear 0s;
}

.top-nav ul.menu > li > a:hover {
  color: #3649e2;
}

.top-nav ul.menu > li.menu-item-has-children > a:after,
.top-nav ul.menu > li.submenu > a:after {
  content: "▼";
  font-size: 0.6rem;
  margin-left: 0.5rem;
  position: relative;
  top: -0.2rem;
}

.top-nav ul.menu > li > ul.sub-menu {
  background: white none repeat scroll 0 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: absolute;
  display: none;
  top: 100%;
  width: 13rem;
  z-index: 10;
  margin: 0;
  padding: 0;
}

.top-nav ul.menu > li:hover > ul.sub-menu,
.top-nav ul.menu > li > ul.sub-menu.show-ul {
  display: block;
}

.top-nav ul.menu > li > ul.sub-menu > li {
  float: left;
  list-style: none;
  width: 100%;
}

.top-nav ul.menu > li > ul.sub-menu > li > a {
  color: #444;
  display: block;
  font-size: 0.9rem;
  padding: 0.6rem 1.25rem;
  text-decoration: none;
  transition: all 0.15s linear 0s;
}

.top-nav ul.menu > li > ul.sub-menu > li > a:hover {
  background: #3649e2 none repeat scroll 0 0;
  color: white;
}

/* Second level dropdown menu styles */
.top-nav ul.menu > li > ul.sub-menu > li.menu-item-has-children > a:after {
  content: "▶"; /* Right-pointing triangle */
  font-size: 0.6rem;
  margin-left: 0.5rem;
  position: relative;
  top: -0.1rem;
}

.top-nav ul.menu > li > ul.sub-menu > li > ul.sub-menu {
  background: white none repeat scroll 0 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: absolute;
  display: none;
  left: 100%; /* Position to the right of the first level dropdown */
  top: 0;
  width: 13rem;
  z-index: 11; /* Higher than first dropdown */
  margin: 0;
  padding: 0;
}

.top-nav ul.menu > li > ul.sub-menu > li:hover > ul.sub-menu,
.top-nav ul.menu > li > ul.sub-menu > li > ul.sub-menu.show-ul {
  display: block;
}

.top-nav ul.menu > li > ul.sub-menu > li > ul.sub-menu > li {
  float: left;
  list-style: none;
  width: 100%;
}

.top-nav ul.menu > li > ul.sub-menu > li > ul.sub-menu > li > a {
  color: #444;
  display: block;
  font-size: 0.9rem;
  padding: 0.6rem 1.25rem;
  text-decoration: none;
  transition: all 0.15s linear 0s;
}

.top-nav ul.menu > li > ul.sub-menu > li > ul.sub-menu > li > a:hover {
  background: #3649e2 none repeat scroll 0 0;
  color: white;
}

/* Footer Menu Styles */
.footer-links .menu-item {
  margin-bottom: 10px;
}

.footer-links .menu-item a {
  color: #fff;
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-links .menu-item a:hover {
  color: #E2CF36;
  text-decoration: underline;
}

.social-bar-footer {
  border-bottom: 2px solid rgba(54, 73, 226, 0.7);
}

/** Event styles */
.event-date {
  margin-top: 10px;
  background-color: rgb(226, 207, 54);
  color: #ffffff;
  padding: 6px 10px;
}

.event-card {
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  padding: 20px;
  transition: all 0.3s ease;
  height: 100%;
  border: 1px solid rgba(0, 0, 0, 0.08);
}
.event-card:hover {
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
  transform: translateY(-5px);
}

.category-padding {
  padding: 15px 10px !important;
}

/* School Category Range Alignment Styles */
.category-ranges {
  margin: 10px 0;
  background-color: #eeeeee;
  border-radius: 8px;
  border-left: 4px solid rgb(54, 73, 226);
  padding: 12px 15px;
  /* Remove bottom margin from last range item */
  /* Responsive adjustments for smaller screens */
}
.category-ranges .grade-range,
.category-ranges .age-range {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  line-height: 1.4;
}
.category-ranges .grade-range .range-label,
.category-ranges .age-range .range-label {
  display: inline-block;
  width: 100px; /* Fixed width for consistent alignment */
  font-weight: 500;
  color: rgb(171, 153, 34);
  flex-shrink: 0; /* Prevent shrinking */
  font-size: 0.95rem;
}
.category-ranges .grade-range .range-value,
.category-ranges .age-range .range-value {
  font-weight: 600;
  color: #222;
  margin-left: 5px;
  font-size: 0.95rem;
}
.category-ranges .age-range {
  margin-bottom: 0;
}
@media (max-width: 768px) {
  .category-ranges .grade-range .range-label,
  .category-ranges .age-range .range-label {
    width: 90px; /* Slightly smaller on mobile */
    font-size: 0.9rem;
  }
  .category-ranges .grade-range .range-value,
  .category-ranges .age-range .range-value {
    font-size: 0.9rem;
  }
}
@media (max-width: 480px) {
  .category-ranges .grade-range .range-label,
  .category-ranges .age-range .range-label {
    width: 80px; /* Even smaller on very small screens */
  }
}

/* Blog Card Metadata Styling */
.blog-metadata {
  background-color: #eeeeee;
  border-radius: 8px;
  padding: 10px 12px 5px 10px !important;
  margin: 10px 0 15px 0;
  border-left: 3px solid rgb(171, 153, 34);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.blog-metadata .s-4 {
  font-size: 0.85rem;
  line-height: 1.3;
  color: #222;
}
.blog-metadata .s-4 a {
  color: #222;
  text-decoration: none;
  transition: color 0.3s ease;
  font-weight: 500;
}
.blog-metadata .s-4 a:hover {
  color: rgb(54, 73, 226);
  text-decoration: underline;
}

/* Active link in sidebar */
.active-link {
  border: 2px solid rgb(226, 207, 54) !important;
  background-color: white !important;
  color: #000000 !important; /* Make text black */
  font-weight: bold !important;
  position: relative;
  padding: 10px 30px 10px 15px !important; /* Increased padding all around, with extra on the right for the tick */
  line-height: 1.8 !important; /* Further increased line height for better readability */
  margin: 0.2rem 0 !important; /* Add margin for better spacing between items */
}
.active-link:after {
  content: "✓";
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: rgb(226, 207, 54);
}

/* WordPress Gutenberg Block Editor Styles */
/* 
 * Column Card Styles - Consistent with site-wide card design
 * 
 * Usage Instructions:
 * 1. Add these classes to individual columns in the Gutenberg editor
 * 2. In the block editor, select a column and add the class in "Advanced > Additional CSS class(es)"
 * 
 * Available Classes:
 * - column-card: Basic white card with shadow
 * - column-card-primary: White card with blue left border accent
 * - column-card-light: Light grey background card
 * - column-card-highlight: Light blue background card
 * 
 * Modifiers (can be combined):
 * - Add "-compact" for less padding (e.g., column-card-compact)
 * - Add "-spacious" for more padding (e.g., column-card-spacious)
 * - Add "-center" for centered text (e.g., column-card-center)
 * - Add "-static" to disable hover effects (e.g., column-card-static)
 * - Add "-icon-top" for icon/image at top layout (e.g., column-card-icon-top)
 * 
 * For equal height cards, add "equal-height-cards" class to the Columns block
 */
/* Basic column card - matches event-card styling */
.wp-block-columns .wp-block-column.column-card,
.wp-block-column.column-card {
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  padding: 20px;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
}
.wp-block-columns .wp-block-column.column-card:hover,
.wp-block-column.column-card:hover {
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
  transform: translateY(-5px);
}

/* Column card with primary accent - matches extracurricular-summary styling */
.wp-block-columns .wp-block-column.column-card-primary,
.wp-block-column.column-card-primary {
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  padding: 20px;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-left: 4px solid rgb(54, 73, 226);
  margin-bottom: 20px;
}
.wp-block-columns .wp-block-column.column-card-primary:hover,
.wp-block-column.column-card-primary:hover {
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
  transform: translateY(-5px);
}

/* Light background column card - matches blog-metadata styling */
.wp-block-columns .wp-block-column.column-card-light,
.wp-block-column.column-card-light {
  background-color: #eeeeee;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 20px;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}
.wp-block-columns .wp-block-column.column-card-light:hover,
.wp-block-column.column-card-light:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-3px);
}

/* Primary highlight column card - light blue background */
.wp-block-columns .wp-block-column.column-card-highlight,
.wp-block-column.column-card-highlight {
  background-color: rgba(54, 73, 226, 0.1); /* Light blue background using primary color with transparency */
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 20px;
  transition: all 0.3s ease;
  border: 1px solid rgba(54, 73, 226, 0.2);
  margin-bottom: 20px;
}
.wp-block-columns .wp-block-column.column-card-highlight:hover,
.wp-block-column.column-card-highlight:hover {
  background-color: rgba(54, 73, 226, 0.15); /* Slightly darker on hover */
  box-shadow: 0 6px 15px rgba(54, 73, 226, 0.15);
  transform: translateY(-3px);
}

/* Padding modifiers */
.wp-block-column.column-card-compact,
.wp-block-column.column-card-primary-compact,
.wp-block-column.column-card-light-compact,
.wp-block-column.column-card-highlight-compact {
  padding: 15px !important;
}

.wp-block-column.column-card-spacious,
.wp-block-column.column-card-primary-spacious,
.wp-block-column.column-card-light-spacious,
.wp-block-column.column-card-highlight-spacious {
  padding: 30px !important;
}

/* Remove bottom margin for last column cards in a row */
.wp-block-columns .wp-block-column:last-child.column-card,
.wp-block-columns .wp-block-column:last-child.column-card-primary,
.wp-block-columns .wp-block-column:last-child.column-card-light,
.wp-block-columns .wp-block-column:last-child.column-card-highlight {
  margin-bottom: 0;
}

/* Responsive adjustments for column cards */
@media (max-width: 768px) {
  .wp-block-column.column-card,
  .wp-block-column.column-card-primary,
  .wp-block-column.column-card-light,
  .wp-block-column.column-card-highlight {
    margin-bottom: 15px;
    padding: 15px;
  }
  .wp-block-column.column-card.column-card-compact,
  .wp-block-column.column-card-primary.column-card-compact,
  .wp-block-column.column-card-light.column-card-compact,
  .wp-block-column.column-card-highlight.column-card-compact {
    padding: 12px !important;
  }
  .wp-block-column.column-card.column-card-spacious,
  .wp-block-column.column-card-primary.column-card-spacious,
  .wp-block-column.column-card-light.column-card-spacious,
  .wp-block-column.column-card-highlight.column-card-spacious {
    padding: 20px !important;
  }
}
/* Content styling within column cards */
.wp-block-column.column-card,
.wp-block-column.column-card-primary,
.wp-block-column.column-card-light,
.wp-block-column.column-card-highlight {
  /* Style headings within column cards */
  /* Style paragraphs within column cards */
  /* Style lists within column cards */
  /* Style images within column cards */
  /* Style buttons within column cards */
}
.wp-block-column.column-card h1, .wp-block-column.column-card h2, .wp-block-column.column-card h3, .wp-block-column.column-card h4, .wp-block-column.column-card h5, .wp-block-column.column-card h6,
.wp-block-column.column-card-primary h1,
.wp-block-column.column-card-primary h2,
.wp-block-column.column-card-primary h3,
.wp-block-column.column-card-primary h4,
.wp-block-column.column-card-primary h5,
.wp-block-column.column-card-primary h6,
.wp-block-column.column-card-light h1,
.wp-block-column.column-card-light h2,
.wp-block-column.column-card-light h3,
.wp-block-column.column-card-light h4,
.wp-block-column.column-card-light h5,
.wp-block-column.column-card-light h6,
.wp-block-column.column-card-highlight h1,
.wp-block-column.column-card-highlight h2,
.wp-block-column.column-card-highlight h3,
.wp-block-column.column-card-highlight h4,
.wp-block-column.column-card-highlight h5,
.wp-block-column.column-card-highlight h6 {
  margin-top: 0;
  margin-bottom: 15px;
}
.wp-block-column.column-card h1:last-child, .wp-block-column.column-card h2:last-child, .wp-block-column.column-card h3:last-child, .wp-block-column.column-card h4:last-child, .wp-block-column.column-card h5:last-child, .wp-block-column.column-card h6:last-child,
.wp-block-column.column-card-primary h1:last-child,
.wp-block-column.column-card-primary h2:last-child,
.wp-block-column.column-card-primary h3:last-child,
.wp-block-column.column-card-primary h4:last-child,
.wp-block-column.column-card-primary h5:last-child,
.wp-block-column.column-card-primary h6:last-child,
.wp-block-column.column-card-light h1:last-child,
.wp-block-column.column-card-light h2:last-child,
.wp-block-column.column-card-light h3:last-child,
.wp-block-column.column-card-light h4:last-child,
.wp-block-column.column-card-light h5:last-child,
.wp-block-column.column-card-light h6:last-child,
.wp-block-column.column-card-highlight h1:last-child,
.wp-block-column.column-card-highlight h2:last-child,
.wp-block-column.column-card-highlight h3:last-child,
.wp-block-column.column-card-highlight h4:last-child,
.wp-block-column.column-card-highlight h5:last-child,
.wp-block-column.column-card-highlight h6:last-child {
  margin-bottom: 0;
}
.wp-block-column.column-card p,
.wp-block-column.column-card-primary p,
.wp-block-column.column-card-light p,
.wp-block-column.column-card-highlight p {
  margin-bottom: 15px;
}
.wp-block-column.column-card p:last-child,
.wp-block-column.column-card-primary p:last-child,
.wp-block-column.column-card-light p:last-child,
.wp-block-column.column-card-highlight p:last-child {
  margin-bottom: 0;
}
.wp-block-column.column-card ul, .wp-block-column.column-card ol,
.wp-block-column.column-card-primary ul,
.wp-block-column.column-card-primary ol,
.wp-block-column.column-card-light ul,
.wp-block-column.column-card-light ol,
.wp-block-column.column-card-highlight ul,
.wp-block-column.column-card-highlight ol {
  margin-bottom: 15px;
}
.wp-block-column.column-card ul:last-child, .wp-block-column.column-card ol:last-child,
.wp-block-column.column-card-primary ul:last-child,
.wp-block-column.column-card-primary ol:last-child,
.wp-block-column.column-card-light ul:last-child,
.wp-block-column.column-card-light ol:last-child,
.wp-block-column.column-card-highlight ul:last-child,
.wp-block-column.column-card-highlight ol:last-child {
  margin-bottom: 0;
}
.wp-block-column.column-card img,
.wp-block-column.column-card-primary img,
.wp-block-column.column-card-light img,
.wp-block-column.column-card-highlight img {
  border-radius: 6px;
  max-width: 100%;
  height: auto;
}
.wp-block-column.column-card .wp-block-button,
.wp-block-column.column-card-primary .wp-block-button,
.wp-block-column.column-card-light .wp-block-button,
.wp-block-column.column-card-highlight .wp-block-button {
  margin-bottom: 10px;
}
.wp-block-column.column-card .wp-block-button:last-child,
.wp-block-column.column-card-primary .wp-block-button:last-child,
.wp-block-column.column-card-light .wp-block-button:last-child,
.wp-block-column.column-card-highlight .wp-block-button:last-child {
  margin-bottom: 0;
}

/* Additional utility classes for column cards */
/* Center content within column cards */
.wp-block-column.column-card-center,
.wp-block-column.column-card-primary-center,
.wp-block-column.column-card-light-center,
.wp-block-column.column-card-highlight-center {
  text-align: center;
}

/* Equal height column cards (when used in a columns block) */
/* Using very specific selectors to override Gutenberg's CSS */
.wp-block-columns.equal-height-cards.is-layout-flex.wp-block-columns-is-layout-flex,
.wp-block-columns.equal-height-cards.is-layout-flex,
.wp-block-columns.equal-height-cards {
  align-items: stretch !important;
  min-height: 100% !important;
}

.wp-block-columns.equal-height-cards .wp-block-column.is-layout-flow.wp-block-column-is-layout-flow.column-card, .wp-block-columns.equal-height-cards .wp-block-column.is-layout-flow.wp-block-column-is-layout-flow.column-card-primary, .wp-block-columns.equal-height-cards .wp-block-column.is-layout-flow.wp-block-column-is-layout-flow.column-card-light, .wp-block-columns.equal-height-cards .wp-block-column.is-layout-flow.wp-block-column-is-layout-flow.column-card-highlight,
.wp-block-columns.equal-height-cards .wp-block-column.is-layout-flow.column-card,
.wp-block-columns.equal-height-cards .wp-block-column.is-layout-flow.column-card-primary,
.wp-block-columns.equal-height-cards .wp-block-column.is-layout-flow.column-card-light,
.wp-block-columns.equal-height-cards .wp-block-column.is-layout-flow.column-card-highlight,
.wp-block-columns.equal-height-cards .wp-block-column.column-card,
.wp-block-columns.equal-height-cards .wp-block-column.column-card-primary,
.wp-block-columns.equal-height-cards .wp-block-column.column-card-light,
.wp-block-columns.equal-height-cards .wp-block-column.column-card-highlight {
  display: flex !important;
  flex-direction: column !important;
  align-self: stretch !important;
  height: auto !important;
  min-height: 100% !important;
  flex: 1 1 auto !important;
  /* Ensure all content within the card is properly spaced */
  /* Remove margin from last element and make it grow */
}
.wp-block-columns.equal-height-cards .wp-block-column.is-layout-flow.wp-block-column-is-layout-flow.column-card > *, .wp-block-columns.equal-height-cards .wp-block-column.is-layout-flow.wp-block-column-is-layout-flow.column-card-primary > *, .wp-block-columns.equal-height-cards .wp-block-column.is-layout-flow.wp-block-column-is-layout-flow.column-card-light > *, .wp-block-columns.equal-height-cards .wp-block-column.is-layout-flow.wp-block-column-is-layout-flow.column-card-highlight > *,
.wp-block-columns.equal-height-cards .wp-block-column.is-layout-flow.column-card > *,
.wp-block-columns.equal-height-cards .wp-block-column.is-layout-flow.column-card-primary > *,
.wp-block-columns.equal-height-cards .wp-block-column.is-layout-flow.column-card-light > *,
.wp-block-columns.equal-height-cards .wp-block-column.is-layout-flow.column-card-highlight > *,
.wp-block-columns.equal-height-cards .wp-block-column.column-card > *,
.wp-block-columns.equal-height-cards .wp-block-column.column-card-primary > *,
.wp-block-columns.equal-height-cards .wp-block-column.column-card-light > *,
.wp-block-columns.equal-height-cards .wp-block-column.column-card-highlight > * {
  margin-bottom: 15px;
  flex-shrink: 0;
}
.wp-block-columns.equal-height-cards .wp-block-column.is-layout-flow.wp-block-column-is-layout-flow.column-card > *:last-child, .wp-block-columns.equal-height-cards .wp-block-column.is-layout-flow.wp-block-column-is-layout-flow.column-card-primary > *:last-child, .wp-block-columns.equal-height-cards .wp-block-column.is-layout-flow.wp-block-column-is-layout-flow.column-card-light > *:last-child, .wp-block-columns.equal-height-cards .wp-block-column.is-layout-flow.wp-block-column-is-layout-flow.column-card-highlight > *:last-child,
.wp-block-columns.equal-height-cards .wp-block-column.is-layout-flow.column-card > *:last-child,
.wp-block-columns.equal-height-cards .wp-block-column.is-layout-flow.column-card-primary > *:last-child,
.wp-block-columns.equal-height-cards .wp-block-column.is-layout-flow.column-card-light > *:last-child,
.wp-block-columns.equal-height-cards .wp-block-column.is-layout-flow.column-card-highlight > *:last-child,
.wp-block-columns.equal-height-cards .wp-block-column.column-card > *:last-child,
.wp-block-columns.equal-height-cards .wp-block-column.column-card-primary > *:last-child,
.wp-block-columns.equal-height-cards .wp-block-column.column-card-light > *:last-child,
.wp-block-columns.equal-height-cards .wp-block-column.column-card-highlight > *:last-child {
  margin-bottom: 0 !important;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

/* Alternative approach using CSS Grid for equal heights */
.wp-block-columns.equal-height-cards-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
  gap: 20px !important;
  align-items: stretch !important;
}
.wp-block-columns.equal-height-cards-grid .wp-block-column.column-card, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-primary, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-light, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-highlight {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
  align-items: stretch !important;
  /* Reset all margins and ensure consistent spacing */
  /* First element should have no top margin */
  /* Last element grows to fill space and has no bottom margin */
  /* Ensure headings have consistent spacing */
  /* Ensure paragraphs have consistent spacing */
  /* Reset any WordPress block margins that might cause differences */
}
.wp-block-columns.equal-height-cards-grid .wp-block-column.column-card > *, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-primary > *, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-light > *, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-highlight > * {
  margin-top: 0 !important;
  margin-bottom: 15px !important;
  flex-shrink: 0;
}
.wp-block-columns.equal-height-cards-grid .wp-block-column.column-card > *:first-child, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-primary > *:first-child, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-light > *:first-child, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-highlight > *:first-child {
  margin-top: 0 !important;
}
.wp-block-columns.equal-height-cards-grid .wp-block-column.column-card > *:last-child, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-primary > *:last-child, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-light > *:last-child, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-highlight > *:last-child {
  margin-bottom: 0 !important;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.wp-block-columns.equal-height-cards-grid .wp-block-column.column-card h1, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card h2, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card h3, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card h4, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card h5, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card h6, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-primary h1, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-primary h2, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-primary h3, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-primary h4, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-primary h5, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-primary h6, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-light h1, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-light h2, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-light h3, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-light h4, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-light h5, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-light h6, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-highlight h1, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-highlight h2, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-highlight h3, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-highlight h4, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-highlight h5, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-highlight h6 {
  margin-top: 0 !important;
  margin-bottom: 15px !important;
  line-height: 1.3 !important;
}
.wp-block-columns.equal-height-cards-grid .wp-block-column.column-card p, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-primary p, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-light p, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-highlight p {
  margin-top: 0 !important;
  margin-bottom: 15px !important;
  line-height: 1.5 !important;
}
.wp-block-columns.equal-height-cards-grid .wp-block-column.column-card .wp-block-heading, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-primary .wp-block-heading, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-light .wp-block-heading, .wp-block-columns.equal-height-cards-grid .wp-block-column.column-card-highlight .wp-block-heading {
  margin-top: 0 !important;
  margin-bottom: 15px !important;
}

/* Improved flexbox approach with better height matching */
.wp-block-columns.equal-height-cards-flex {
  display: flex !important;
  align-items: stretch !important;
  gap: 20px !important;
}
.wp-block-columns.equal-height-cards-flex .wp-block-column {
  flex: 1 !important;
}
.wp-block-columns.equal-height-cards-flex .wp-block-column.column-card, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-primary, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-light, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-highlight {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
  /* Reset all spacing to ensure consistency */
  /* Consistent heading and paragraph spacing */
}
.wp-block-columns.equal-height-cards-flex .wp-block-column.column-card > *, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-primary > *, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-light > *, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-highlight > * {
  margin-top: 0 !important;
  margin-bottom: 15px !important;
}
.wp-block-columns.equal-height-cards-flex .wp-block-column.column-card > *:first-child, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-primary > *:first-child, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-light > *:first-child, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-highlight > *:first-child {
  margin-top: 0 !important;
}
.wp-block-columns.equal-height-cards-flex .wp-block-column.column-card > *:last-child, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-primary > *:last-child, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-light > *:last-child, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-highlight > *:last-child {
  margin-bottom: 0 !important;
  flex-grow: 1;
}
.wp-block-columns.equal-height-cards-flex .wp-block-column.column-card h1, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card h2, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card h3, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card h4, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card h5, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card h6, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-primary h1, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-primary h2, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-primary h3, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-primary h4, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-primary h5, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-primary h6, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-light h1, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-light h2, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-light h3, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-light h4, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-light h5, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-light h6, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-highlight h1, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-highlight h2, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-highlight h3, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-highlight h4, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-highlight h5, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-highlight h6 {
  margin-top: 0 !important;
  margin-bottom: 15px !important;
  line-height: 1.3 !important;
}
.wp-block-columns.equal-height-cards-flex .wp-block-column.column-card p, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-primary p, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-light p, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-highlight p {
  margin-top: 0 !important;
  margin-bottom: 15px !important;
  line-height: 1.5 !important;
}
.wp-block-columns.equal-height-cards-flex .wp-block-column.column-card .wp-block-heading, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-primary .wp-block-heading, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-light .wp-block-heading, .wp-block-columns.equal-height-cards-flex .wp-block-column.column-card-highlight .wp-block-heading {
  margin-top: 0 !important;
  margin-bottom: 15px !important;
}

/* No hover effects variant */
.wp-block-column.column-card-static:hover,
.wp-block-column.column-card-primary-static:hover,
.wp-block-column.column-card-light-static:hover,
.wp-block-column.column-card-highlight-static:hover {
  transform: none !important;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05) !important;
}

/* Column card with icon/image at top */
.wp-block-column.column-card-icon-top,
.wp-block-column.column-card-primary-icon-top,
.wp-block-column.column-card-light-icon-top,
.wp-block-column.column-card-highlight-icon-top {
  text-align: center;
}
.wp-block-column.column-card-icon-top .wp-block-image:first-child,
.wp-block-column.column-card-icon-top img:first-child,
.wp-block-column.column-card-primary-icon-top .wp-block-image:first-child,
.wp-block-column.column-card-primary-icon-top img:first-child,
.wp-block-column.column-card-light-icon-top .wp-block-image:first-child,
.wp-block-column.column-card-light-icon-top img:first-child,
.wp-block-column.column-card-highlight-icon-top .wp-block-image:first-child,
.wp-block-column.column-card-highlight-icon-top img:first-child {
  margin-bottom: 15px;
  max-width: 80px;
  height: auto;
  border-radius: 50%;
}

/* Responsee Framework Integration */
/* Ensure column cards work well with Responsee grid system */
.wp-block-columns .wp-block-column.column-card, .wp-block-columns .wp-block-column.column-card-primary, .wp-block-columns .wp-block-column.column-card-light, .wp-block-columns .wp-block-column.column-card-highlight {
  box-sizing: border-box;
  width: 100%;
}
@media (min-width: 769px) {
  .wp-block-columns .wp-block-column.column-card, .wp-block-columns .wp-block-column.column-card-primary, .wp-block-columns .wp-block-column.column-card-light, .wp-block-columns .wp-block-column.column-card-highlight {
    margin-left: 0;
    margin-right: 0;
  }
}

/* Integration with existing Responsee classes */
.wp-block-column.column-card.background-white, .wp-block-column.column-card-primary.background-white, .wp-block-column.column-card-light.background-white, .wp-block-column.column-card-highlight.background-white {
  background-color: #ffffff !important;
}
.wp-block-column.column-card.background-grey-light, .wp-block-column.column-card-primary.background-grey-light, .wp-block-column.column-card-light.background-grey-light, .wp-block-column.column-card-highlight.background-grey-light {
  background-color: #eeeeee !important;
}

/* Reusable image styles */
.rounded-image-top {
  border-radius: 10px 10px 0 0;
  overflow: hidden;
}

.rounded-image, .carousel-default .item img, .grid.margin article.s-12.m-12.l-6.margin-m-bottom:last-child img {
  border-radius: 10px;
  overflow: hidden;
}
.rounded-image img, .carousel-default .item img img, .grid.margin article.s-12.m-12.l-6.margin-m-bottom:last-child img img {
  border-radius: 10px;
  overflow: hidden;
}

.rounded-image-full {
  border-radius: 10px;
  overflow: hidden;
}
.rounded-image-full img {
  border-radius: 10px;
  overflow: hidden;
  width: 100%;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
  aspect-ratio: 16/9;
}

/* Gallery specific styles */
.image-with-hover-overlay.rounded-image-full {
  margin-bottom: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
.image-with-hover-overlay.rounded-image-full:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.full-img {
  width: 100%;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
}

.full-width-img {
  width: 100%;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
  display: block;
  margin: 0 auto;
  max-width: 1720px;
}

/* Extracurricular page styles */
.extracurricular-summary {
  background-color: #f8f8f8;
  border-left: 4px solid rgb(54, 73, 226);
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 10px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-left: 4px solid rgb(54, 73, 226); /* Maintain the left border */
  transition: all 0.3s ease;
}
.extracurricular-summary:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
}
.extracurricular-summary p {
  margin-bottom: 10px;
  font-size: 1.1rem;
  line-height: 1.5;
}
.extracurricular-summary p:last-child {
  margin-bottom: 0;
}
.extracurricular-summary p strong {
  color: rgb(54, 73, 226);
  font-weight: 600;
}

/* Single Extracurricular Activity Styles */
.background-primary-hightlight {
  background-color: white;
  padding: 15px; /* Reduced padding from 20px to 15px */
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(54, 73, 226, 0.1);
  transition: all 0.3s ease;
  /* Governance filter buttons */
  /* Style for each info row */
  /* Reduce margin in grid */
  /* Special styling for Extracurricular Category section */
}
.background-primary-hightlight .teacher-filter-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;
}
.background-primary-hightlight .teacher-filter-buttons .teacher-filter-btn {
  margin: 5px;
  padding: 8px 15px;
  background-color: white;
  color: rgb(54, 73, 226);
  border: 1px solid rgb(54, 73, 226);
  transition: all 0.3s ease;
}
.background-primary-hightlight .teacher-filter-buttons .teacher-filter-btn:hover {
  background-color: rgb(54, 73, 226);
  color: white;
  transform: translateY(-2px);
}
.background-primary-hightlight .teacher-filter-buttons .teacher-filter-btn.active {
  background-color: rgb(54, 73, 226);
  color: white;
  font-weight: bold;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}
.background-primary-hightlight:hover {
  box-shadow: 0 6px 15px rgba(54, 73, 226, 0.15);
}
.background-primary-hightlight .activity-excerpt {
  font-size: 0.95rem;
  line-height: 1.3;
  padding-bottom: 8px;
}
.background-primary-hightlight .s-12.margin-bottom-10 {
  margin-bottom: 1px; /* Reduced from 15px to 8px */
  padding-bottom: 1px; /* Reduced from 12px to 8px */
}
.background-primary-hightlight .s-12.margin-bottom-10:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}
.background-primary-hightlight .grid.margin {
  margin: 0 -5px; /* Reduced margin */
  /* Reduce spacing between grid items */
}
.background-primary-hightlight .grid.margin > div {
  padding: 0 5px;
}
.background-primary-hightlight .s-12.margin-bottom-10:nth-child(2) {
  background-color: rgba(54, 73, 226, 0.08);
  border-radius: 6px;
  padding: 6px;
  margin: 8px 0;
  border-left: 3px solid rgb(54, 73, 226);
}
.background-primary-hightlight .s-12.margin-bottom-10:nth-child(2) a.category-link {
  display: inline-block;
  position: relative;
  padding: 3px 20px 3px 3px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 3px;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}
.background-primary-hightlight .s-12.margin-bottom-10:nth-child(2) a.category-link i.icon-tag {
  color: rgb(54, 73, 226);
}
.background-primary-hightlight .s-12.margin-bottom-10:nth-child(2) a.category-link:after {
  content: "→";
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.2s ease;
  color: rgb(54, 73, 226);
  font-size: 0.8rem;
}
.background-primary-hightlight .s-12.margin-bottom-10:nth-child(2) a.category-link:hover {
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}
.background-primary-hightlight .s-12.margin-bottom-10:nth-child(2) a.category-link:hover:after {
  right: 5px;
}

/* Activity Summary Table Styles */
.activity-summary-container {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(54, 73, 226, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}
.activity-summary-container:hover {
  box-shadow: 0 6px 15px rgba(54, 73, 226, 0.15);
}

.activity-summary-table {
  width: 100%;
  border-collapse: collapse;
  /* Make table responsive */
  /* Table styles for larger screens */
  /* Alternating row backgrounds */
  /* Excerpt cell styling */
  /* Category link styling */
}
@media screen and (max-width: 600px) {
  .activity-summary-table {
    display: block;
  }
  .activity-summary-table tbody, .activity-summary-table tr, .activity-summary-table th, .activity-summary-table td {
    display: block;
    width: 100%;
  }
  .activity-summary-table th {
    text-align: left;
    background-color: white;
    padding: 10px;
    border-bottom: 1px solid rgba(54, 73, 226, 0.1);
  }
  .activity-summary-table td {
    padding: 10px;
    border-bottom: 1px solid rgba(54, 73, 226, 0.1);
  }
  .activity-summary-table tr:last-child td, .activity-summary-table tr:last-child th {
    border-bottom: none;
  }
}
@media screen and (min-width: 601px) {
  .activity-summary-table th {
    text-align: left;
    padding: 5px 15px;
    width: 40%;
    font-weight: 600;
    color: rgb(54, 73, 226);
  }
  .activity-summary-table td {
    padding: 5px 15px;
  }
}
.activity-summary-table .table-row-odd {
  background-color: white;
}
.activity-summary-table .table-row-even {
  background-color: white;
}
.activity-summary-table .activity-excerpt-cell {
  padding: 10px 15px !important;
}
.activity-summary-table .activity-excerpt {
  font-size: 0.95rem;
  line-height: 1.4;
  color: rgb(171, 153, 34);
  font-weight: 500;
}
.activity-summary-table .category-link {
  display: inline-block;
  position: relative;
  padding: 3px 20px 3px 3px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 3px;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}
.activity-summary-table .category-link:after {
  content: "→";
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.2s ease;
  color: rgb(54, 73, 226);
  font-size: 0.8rem;
}
.activity-summary-table .category-link:hover {
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Extracurricular Item Page Image */
.extracurricular-page-image {
  width: 100%;
  height: auto;
  max-height: 500px;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 10px;
  margin-bottom: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Activity items on extracurricular pages */
.activities-list {
  /* Styles for the simplified related activities (title and date only) */
  /* Separator line between activities */
  /* Thumbnail image in activity cards */
}
.activities-list a.activity-link {
  text-decoration: none;
  display: block;
  color: inherit;
  padding: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  /* Add border-bottom to separate activities in the single rounded div */
}
.activities-list a.activity-link:hover {
  text-decoration: none;
  background-color: rgba(255, 255, 255, 0.7);
  transform: translateY(-2px);
}
.activities-list a.activity-link.border-bottom {
  border-bottom: 1px solid rgba(54, 73, 226, 0.2);
  padding-bottom: 15px;
  margin-bottom: 10px;
}
.activities-list a.activity-link h4 {
  margin-bottom: 5px;
  font-size: 1.1rem;
  line-height: 1.3;
  color: rgb(54, 73, 226);
}
.activities-list a.activity-link p {
  margin: 0;
  color: #666;
}
.activities-list hr.activity-separator {
  border: 0;
  height: 1px;
  background: rgba(54, 73, 226, 0.2);
  margin: 10px 0;
}
.activities-list .activity-thumbnail {
  width: 100%;
  height: auto;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* No activities message styling */
.no-activities-message .background-grey-light {
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}
.no-activities-message .background-grey-light:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
.no-activities-message .icon-info-circled {
  color: rgb(54, 73, 226);
  display: inline-block;
  margin-bottom: 15px;
}
.no-activities-message p {
  color: #555;
  line-height: 1.5;
}
.no-activities-message p.text-strong {
  font-size: 1.1rem;
  color: #333;
  margin-bottom: 8px;
}
.no-activities-message .button {
  margin-top: 10px;
  transition: all 0.3s ease;
}
.no-activities-message .button:hover {
  transform: translateY(-3px);
}

/* Buttons with primary background */
.button.background-primary {
  display: inline-block;
  padding: 12px 24px;
  border-radius: 30px;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  border: 2px solid rgb(54, 73, 226);
}
.button.background-primary:hover {
  background-color: transparent !important;
  color: rgb(54, 73, 226) !important;
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Blog author and category links */
.blog-author,
.blog-category {
  color: #0000ff !important;
}
.blog-author:hover,
.blog-category:hover {
  text-decoration: underline;
}

/* About Nkhwazi Primary School section styles */
.grid.margin article.s-12.m-12.l-6.margin-m-bottom:first-child {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  text-align: left;
}
.grid.margin article.s-12.m-12.l-6.margin-m-bottom:first-child h2,
.grid.margin article.s-12.m-12.l-6.margin-m-bottom:first-child p,
.grid.margin article.s-12.m-12.l-6.margin-m-bottom:first-child a {
  text-align: left;
}

/* Apply rounded corners to the About Nkhwazi Primary School image */
.nkhwazi-stats {
  background-color: #eaeaea;
  padding: 0.8rem;
  border-radius: 10px;
}

.number-stat {
  border: 1px solid #ffffff;
  background-color: #f2f2f2;
  border-radius: 10px;
}

.timer {
  display: inline-block;
  min-width: 1em;
  text-align: center;
  opacity: 1;
  visibility: visible;
}

/** Links */
/* Font colors */
.background-white,
.background-white p,
a.background-white,
.background-white a,
.background-white a:active {
  color: #444;
}

.background-white a:link {
  color: #0000ff;
}

.background-white a:visited {
  color: #800080;
}

.background-white a:hover {
  color: #ff0000;
}

.category-filter {
  color: #222;
  font-size: 1.2rem;
  margin-bottom: 1rem;
  font-weight: bold;
}

/* Teacher filter section */
.teacher-filter-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-bottom: 20px;
}
.teacher-filter-buttons .button {
  margin: 5px;
  transition: all 0.3s ease;
}

.teacher-filter-btn {
  background-color: white !important;
  color: black !important;
  border: 1px solid black !important;
  border-radius: 5px !important;
  padding: 8px 15px !important;
}

.teacher-filter-buttons .button.active {
  background-color: rgb(54, 73, 226) !important;
  color: white !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}
.teacher-filter-buttons .button:hover {
  background-color: rgb(54, 73, 226) !important;
  color: white !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.background-grey {
  background-color: #f5f5f5;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}
.background-grey:hover {
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
}

.box-shadow {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.08);
}
.box-shadow:hover {
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
  transform: translateY(-5px);
}

/* Notification styles */
.notification-card {
  transition: all 0.3s ease;
  background-color: #ffffff !important;
  border: 1px solid #cccccc; /* Grey border around all sides */
  border-left: 4px solid rgb(226, 207, 54); /* Thick left border with secondary color */
  border-radius: 10px !important;
  padding: 0 10px 15px 10px !important;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05) !important;
}
.notification-card h3 {
  padding: 0.5rem 1rem;
  color: #fff;
  border-radius: 10px;
  text-align: center;
  background: rgb(54, 73, 226);
}
.notification-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08) !important;
}
.notification-card small {
  color: #777;
  font-size: 0.85rem;
  display: block;
  margin-bottom: 12px;
}
.notification-card p {
  color: #333;
  line-height: 1.5;
}

/* WordPress Login Page Styles */
body.login {
  background: url("../img/nkhwazi-bg.webp") no-repeat center center fixed !important;
  background-size: cover !important;
  position: relative;
  /* Add overlay for better readability */
}
body.login::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  z-index: -1;
}

/* Login form container */
#login {
  width: 400px !important;
  padding: 8% 0 0;
  margin: auto;
}
#login h1 {
  text-align: center;
  margin-bottom: 30px;
}
#login h1 a {
  background: rgba(255, 255, 255, 0.95) !important;
  text-decoration: none !important;
  padding: 20px !important;
  border-radius: 15px !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.3s ease !important;
  width: 120px !important;
  height: 120px !important;
  margin: 0 auto !important;
  /* Hide the default WordPress logo text */
  text-indent: -9999px !important;
  overflow: hidden !important;
  /* Add the custom logo */
}
#login h1 a::before {
  content: "" !important;
  background: url("../img/nkhwazi-logo.svg") no-repeat center center !important;
  background-size: contain !important;
  width: 80px !important;
  height: 80px !important;
  display: block !important;
  text-indent: 0 !important;
}
#login h1 a:hover {
  background: rgb(255, 255, 255) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3) !important;
}
#login h1 a:hover::before {
  transform: scale(1.05) !important;
}
#login h1 a:focus {
  box-shadow: 0 0 0 3px rgba(54, 73, 226, 0.3) !important;
  outline: none !important;
}

/* Login form styling */
.login form {
  background: rgba(255, 255, 255, 0.95) !important;
  padding: 30px !important;
  border-radius: 15px !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
  border: none !important;
  margin-top: 20px !important;
  -webkit-backdrop-filter: blur(10px) !important;
          backdrop-filter: blur(10px) !important;
}
.login form .forgetmenot {
  margin-bottom: 20px !important;
}
.login form p {
  margin-bottom: 20px !important;
}
.login form p label {
  color: #222 !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  margin-bottom: 8px !important;
  display: block !important;
}
.login form p input[type=text],
.login form p input[type=password],
.login form p input[type=email] {
  width: 100% !important;
  padding: 12px 15px !important;
  border: 2px solid #e1e5e9 !important;
  border-radius: 8px !important;
  font-size: 16px !important;
  transition: all 0.3s ease !important;
  background: white !important;
}
.login form p input[type=text]:focus,
.login form p input[type=password]:focus,
.login form p input[type=email]:focus {
  border-color: rgb(54, 73, 226) !important;
  box-shadow: 0 0 0 3px rgba(54, 73, 226, 0.1) !important;
  outline: none !important;
}
.login form p input[type=checkbox] {
  margin-right: 8px !important;
  transform: scale(1.2) !important;
}
.login form .submit {
  text-align: center !important;
  margin-top: 25px !important;
}
.login form .submit input[type=submit] {
  background: rgb(54, 73, 226) !important;
  color: white !important;
  border: none !important;
  padding: 12px 30px !important;
  border-radius: 8px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  width: 100% !important;
}
.login form .submit input[type=submit]:hover {
  background: #1d30c8 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 15px rgba(54, 73, 226, 0.3) !important;
}
.login form .submit input[type=submit]:active {
  transform: translateY(0) !important;
}

/* Login links styling */
.login #nav,
.login #backtoblog {
  text-align: center !important;
  margin: 20px 0 !important;
}
.login #nav a,
.login #backtoblog a {
  color: white !important;
  text-decoration: none !important;
  background: rgba(0, 0, 0, 0.6) !important;
  padding: 8px 15px !important;
  border-radius: 20px !important;
  font-size: 14px !important;
  transition: all 0.3s ease !important;
  display: inline-block !important;
}
.login #nav a:hover,
.login #backtoblog a:hover {
  background: rgba(0, 0, 0, 0.8) !important;
  color: rgb(226, 207, 54) !important;
  transform: translateY(-1px) !important;
}

/* Error and success messages */
.login .message,
.login #login_error {
  background: rgba(255, 255, 255, 0.95) !important;
  border-left: 4px solid rgb(54, 73, 226) !important;
  border-radius: 8px !important;
  padding: 15px !important;
  margin: 20px 0 !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}
.login .message.error,
.login #login_error.error {
  border-left-color: #dc3545 !important;
}
.login .message.updated,
.login #login_error.updated {
  border-left-color: #28a745 !important;
}

/* Responsive adjustments for login page */
@media (max-width: 480px) {
  #login {
    width: 90% !important;
    padding: 5% 0 0 !important;
  }
  .login form {
    padding: 20px !important;
    margin: 10px !important;
  }
  #login h1 a {
    padding: 15px !important;
    width: 90px !important;
    height: 90px !important;
  }
  #login h1 a::before {
    width: 60px !important;
    height: 60px !important;
  }
}
/* Sidebar Card Styles */
.blog-card,
.event-card {
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 15px;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
}
.blog-card:hover,
.event-card:hover {
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}
.blog-card h6 a,
.event-card h6 a {
  color: rgb(54, 73, 226);
  text-decoration: none;
  font-weight: 600;
}
.blog-card h6 a:hover,
.event-card h6 a:hover {
  color: #1d30c8;
  text-decoration: underline;
}
.blog-card p,
.event-card p {
  color: #666;
  line-height: 1.4;
  margin: 0;
}
.blog-card small,
.event-card small {
  color: #888;
  font-size: 0.85rem;
}

/* Sidebar notification card specific styles */
aside .notification-card {
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 15px;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-left: 4px solid rgb(226, 207, 54);
}
aside .notification-card:hover {
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}
aside .notification-card h6 a {
  color: rgb(54, 73, 226);
  text-decoration: none;
  font-weight: 600;
}
aside .notification-card h6 a:hover {
  color: #1d30c8;
  text-decoration: underline;
}
aside .notification-card p {
  color: #666;
  line-height: 1.4;
  margin: 0;
}
aside .notification-card small {
  color: #888;
  font-size: 0.85rem;
}

/* Sidebar button styles */
aside .button {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  transition: all 0.3s ease;
}
aside .button:hover {
  background-color: #1d30c8 !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(54, 73, 226, 0.3);
}

.notification-detail {
  background-color: #ffffff !important;
  border-radius: 0 10px 10px 0 !important;
  border-left: 4px solid rgb(54, 73, 226);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}
.notification-detail h2 {
  color: rgb(54, 73, 226);
  font-weight: 600;
  margin-bottom: 10px;
}
.notification-detail .margin-bottom-20 {
  line-height: 1.6;
}
.notification-detail .margin-bottom-20 p {
  margin-bottom: 15px;
}
.notification-detail .margin-bottom-20 ul,
.notification-detail .margin-bottom-20 ol {
  margin-left: 20px;
  margin-bottom: 15px;
}
.notification-detail h2 {
  color: rgb(54, 73, 226);
  border-bottom: 1px solid #eeeeee;
  padding-bottom: 10px;
}
.notification-detail small {
  color: #777;
  font-size: 0.85rem;
  display: block;
  margin-bottom: 15px;
}

.display-block {
  display: block;
}

/* Breadcrumbs Styling */
.position-relative {
  position: relative;
}

.breadcrumbs-container {
  position: absolute;
  bottom: 5px; /* Changed from -15px to keep it inside the header */
  left: 10px;
  z-index: 10;
}

.background-white-transparent {
  background-color: rgba(255, 255, 255, 0.85); /* Semi-transparent white */
}

.breadcrumbs {
  font-size: 0.8rem;
  text-align: left;
}
.breadcrumbs .breadcrumb-item {
  display: inline-block;
}
.breadcrumbs .breadcrumb-item a {
  color: #0000ff;
  text-decoration: none;
}
.breadcrumbs .breadcrumb-item a:hover {
  text-decoration: underline;
  color: #ff0000;
}
.breadcrumbs .breadcrumb-separator {
  display: inline-block;
  margin: 0 5px;
  color: #cccccc;
}

.padding-1x {
  padding: 10px 15px;
}

.padding-2x {
  padding: 2rem !important; /* Updated to match template-style.css but with a bit less padding */
}

/* Gallery thumbnails in single-gallery.php */
.image-with-hover-overlay img {
  border-radius: 10px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.image-with-hover-overlay {
  border-radius: 10px;
  overflow: hidden;
}

/* Responsive adjustments for breadcrumbs */
@media screen and (min-width: 992px) {
  /* Adjustments for large screens */
  .breadcrumbs-container {
    bottom: 15px;
  }
}
@media screen and (max-width: 768px) {
  /* Adjustments for small screens */
  .breadcrumbs-container {
    position: relative;
    bottom: auto;
  }
}
/* Lightcase Gallery Fixes */
/* Fix for iframe scrollbars in lightcase galleries */
#lightcase-content {
  overflow: hidden !important;
}

#lightcase-content .lightcase-contentInner {
  overflow: hidden !important;
}

/* Application Form Field with Counter Styles */
.field-with-counter {
  position: relative;
  margin-bottom: 20px; /* Add space between fields */
  width: 100%;
}
.field-with-counter input,
.field-with-counter textarea,
.field-with-counter select {
  width: 100%;
  margin-bottom: 0 !important; /* Remove default bottom margin */
}

/* Application Form Character Counter Styles */
.form-header {
  background-color: #ffffff;
  clear: both;
}

.character-counter {
  font-size: 0.8rem;
  text-align: right;
  color: #666;
  transition: all 0.3s ease;
  padding: 5px;
  border-radius: 3px;
  display: block;
}
.character-counter.warning {
  color: #e2a736;
  font-weight: bold;
  background-color: rgba(226, 167, 54, 0.1);
}
.character-counter.error {
  color: #e74c3c;
  font-weight: bold;
  background-color: rgba(231, 76, 60, 0.1);
}

.field-error-message {
  color: #e74c3c;
  font-size: 0.85rem;
  margin-top: 5px;
  display: block;
  /* When inside a field-with-counter */
}
.field-with-counter .field-error-message {
  margin-bottom: 5px;
}

/* Form field validation styles */
input.error,
textarea.error,
select.error {
  border-color: #e74c3c !important;
  border-left-width: 3px !important;
}

input.valid,
textarea.valid,
select.valid {
  border-color: #2ecc71 !important;
  border-left-width: 3px !important;
}

/* Default border style for form fields */
input.default-border,
textarea.default-border,
select.default-border {
  border-color: #ddd !important;
  border-left-width: 1px !important;
}

/* Character counter styles */
.field-with-counter {
  position: relative;
  margin-bottom: 15px;
}

.character-counter {
  position: absolute;
  right: 10px;
  top: -20px;
  font-size: 12px;
  color: #777;
}

.character-counter.warning {
  color: #f39c12;
}

.character-counter.error {
  color: #e74c3c;
}

/* Field error message */
.field-error-message {
  display: block;
  color: #e74c3c;
  font-size: 12px;
  margin-top: 5px;
}

/* Form message container */
.form-message {
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  display: none;
}

.form-message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.form-message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Any error message */
.any-error {
  color: #e74c3c;
  font-weight: bold;
}

/* Ensure proper sizing for lightcase content */
#lightcase-case {
  max-width: 100vw !important;
  max-height: 100vh !important;
}

@media screen and (max-width: 768px) {
  /* Adjustments for small screens */
  .breadcrumbs-container {
    left: auto;
    margin-top: 10px;
    margin-bottom: -25px;
  }
  .breadcrumbs {
    font-size: 0.7rem;
    width: 100%;
  }
}
.background-white a:active {
  color: #ff00ff;
}

/* Blog Styles */
/* Blog post metadata container */
.blog-meta-container {
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  padding: 15px 10px !important;
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
  /* Responsive adjustments */
}
.blog-meta-container .meta-item {
  font-size: 0.95rem;
}
.blog-meta-container .meta-item.date {
  color: #222;
}
.blog-meta-container .meta-item.author a, .blog-meta-container .meta-item.category a {
  color: #0000ff;
}
.blog-meta-container .meta-item.author a:hover, .blog-meta-container .meta-item.category a:hover {
  color: #ff0000;
  text-decoration: underline;
}
@media screen and (max-width: 768px) {
  .blog-meta-container .meta-item {
    font-size: 0.9rem;
  }
}
@media screen and (max-width: 480px) {
  .blog-meta-container {
    flex-direction: column;
  }
  .blog-meta-container .meta-item {
    width: 100%;
    margin-bottom: 5px;
    text-align: center !important;
  }
  .blog-meta-container .meta-item:last-child {
    margin-bottom: 0;
  }
}

/* Blog Category Navigation - Horizontal */
.blog-categories-horizontal {
  margin-bottom: 30px;
  /* Responsive adjustments */
  /* Medium screens - 2 per line */
  /* Small screens - stacked */
}
.blog-categories-horizontal .blog-categories-menu {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  padding: 0;
  margin: 0;
  width: 100%; /* Ensure full width */
  box-sizing: border-box; /* Include padding in width calculation */
}
.blog-categories-horizontal .blog-categories-menu li {
  margin: 0 10px 10px 0;
  list-style-type: none;
  box-sizing: border-box; /* Include padding in width calculation */
}
.blog-categories-horizontal .blog-categories-menu .blog-category-link {
  display: inline-block;
  padding: 8px 12px;
  background-color: #f5f5f5;
  border-radius: 5px;
  text-align: center;
  transition: all 0.3s ease;
  color: #222;
  font-weight: 500;
  cursor: pointer;
  width: 100%; /* Ensure full width */
  box-sizing: border-box; /* Include padding in width calculation */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis; /* Add ellipsis for long category names */
}
.blog-categories-horizontal .blog-categories-menu .blog-category-link:hover {
  background-color: #e0e0e0;
  color: #0000ff;
  text-decoration: none;
}
.blog-categories-horizontal .blog-categories-menu .blog-category-link.active {
  background-color: rgb(54, 73, 226);
  color: white;
}
.blog-categories-horizontal .blog-categories-menu .blog-category-link.active:hover {
  background-color: #1d30c8;
  text-decoration: none;
}
@media screen and (max-width: 768px) and (min-width: 481px) {
  .blog-categories-horizontal .blog-categories-menu {
    display: flex;
    flex-wrap: wrap;
  }
  .blog-categories-horizontal .blog-categories-menu li {
    width: calc(50% - 10px); /* 2 per line with some margin */
    margin-right: 10px;
    margin-bottom: 10px;
  }
  .blog-categories-horizontal .blog-categories-menu li:nth-child(2n) {
    margin-right: 0; /* Remove right margin for every second item */
  }
  .blog-categories-horizontal .blog-categories-menu .blog-category-link {
    display: block;
    width: 100%;
  }
}
@media screen and (max-width: 480px) {
  .blog-categories-horizontal .blog-categories-menu {
    flex-direction: column;
  }
  .blog-categories-horizontal .blog-categories-menu li {
    margin-right: 0;
    width: 100%;
  }
  .blog-categories-horizontal .blog-categories-menu .blog-category-link {
    display: block;
    width: 100%;
  }
}

.category-filter {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: rgb(54, 73, 226);
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 0.9rem;
}

.no-posts-message {
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 5px;
  text-align: center;
  margin-top: 20px;
}
.no-posts-message p {
  margin: 0;
  color: #222;
}

/* Blog Images with Rounded Corners */
.single-post article img,
.blog article img,
.blog-sidebar img,
.s-12.m-4.l-4 img,
.s-12.m-12.l-12 img,
.wp-block-image img {
  border-radius: 10px !important;
  overflow: hidden !important;
}

/* WordPress Gutenberg Editor Content Spacing Styles */
/* Add breathing room to content blocks */
/* H2 Headings - Add more space above when following paragraphs */
.wp-block-heading h2,
h2.wp-block-heading,
.entry-content h2,
.post-content h2,
article h2 {
  margin-top: 2rem !important;
  margin-bottom: 1rem !important;
}

/* First H2 in content shouldn't have extra top margin */
.entry-content h2:first-child,
.post-content h2:first-child,
article h2:first-child,
.wp-block-heading:first-child h2,
h2.wp-block-heading:first-child {
  margin-top: 1rem !important;
}

/* Table Spacing - Add breathing room above and below tables */
.wp-block-table,
.entry-content table,
.post-content table,
article table,
table {
  margin-top: 1.5rem !important;
  margin-bottom: 1.5rem !important;
}

/* First table in content shouldn't have extra top margin */
.entry-content table:first-child,
.post-content table:first-child,
article table:first-child,
.wp-block-table:first-child {
  margin-top: 0.625rem !important;
}

/* Last table in content shouldn't have extra bottom margin */
.entry-content table:last-child,
.post-content table:last-child,
article table:last-child,
.wp-block-table:last-child {
  margin-bottom: 0.625rem !important;
}

/* Image Spacing - Add breathing room above and below images */
.wp-block-image,
.wp-block-media-text,
.entry-content img,
.post-content img,
article img,
.entry-content figure,
.post-content figure,
article figure {
  margin-top: 1.5rem !important;
  margin-bottom: 1.5rem !important;
}

/* Specific spacing for images when they follow or precede paragraphs */
p + .wp-block-image,
p + figure,
p + img,
.wp-block-paragraph + .wp-block-image,
.wp-block-paragraph + figure {
  margin-top: 1.8rem !important;
}

.wp-block-image + p,
figure + p,
img + p,
.wp-block-image + .wp-block-paragraph,
figure + .wp-block-paragraph {
  margin-top: 1.8rem !important;
}

/* First image in content shouldn't have extra top margin */
.entry-content img:first-child,
.post-content img:first-child,
article img:first-child,
.wp-block-image:first-child,
.entry-content figure:first-child,
.post-content figure:first-child,
article figure:first-child {
  margin-top: 1rem !important;
}

/* Last image in content shouldn't have extra bottom margin */
.entry-content img:last-child,
.post-content img:last-child,
article img:last-child,
.wp-block-image:last-child,
.entry-content figure:last-child,
.post-content figure:last-child,
article figure:last-child {
  margin-bottom: 1rem !important;
}

/* Ensure images don't break out of their containers */
.wp-block-image img,
.entry-content img,
.post-content img,
article img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Blog Home Button */
.rounded-btn {
  border-radius: 30px;
  padding: 10px 20px;
  transition: all 0.3s ease;
  display: inline-block;
  text-decoration: none;
}
.rounded-btn:hover {
  background-color: #1d30c8;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Blog Home Link in content area */
.blog-home-link-container {
  border-top: 1px solid #eeeeee;
  padding-top: 20px;
}
.blog-home-link-container a {
  font-size: 1.1rem;
  font-weight: 500;
  display: inline-block;
  transition: all 0.3s ease;
}
.blog-home-link-container a:hover {
  transform: translateX(-5px);
  text-decoration: underline;
}
.blog-home-link-container a i {
  transition: all 0.3s ease;
}
.blog-home-link-container a:hover i {
  transform: translateX(-3px);
}

/* Blog Category Navigation */
.blog-category-nav {
  margin-bottom: 20px !important;
  /* Add a subtle background to the filter section */
  background-color: rgba(255, 255, 255, 0.5);
  padding: 15px;
  border-radius: 8px;
}
.blog-category-nav .category-link,
.blog-category-nav .blog-category-link {
  color: rgb(54, 73, 226);
  text-decoration: none;
  padding: 8px 15px !important;
  margin: 0 5px;
  transition: all 0.3s ease;
  display: inline-block;
  border-radius: 20px;
  background-color: rgba(255, 255, 255, 0.7);
}
.blog-category-nav .category-link:hover,
.blog-category-nav .blog-category-link:hover {
  color: #1a2ab2;
  background-color: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}
.blog-category-nav .category-link.active,
.blog-category-nav .blog-category-link.active {
  font-weight: bold;
  background-color: rgb(54, 73, 226);
  color: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Filter section in extracurricular activities page */
.filter-section {
  border-bottom: 1px solid rgba(54, 73, 226, 0.2);
  padding-bottom: 20px;
  margin-bottom: 30px;
}
.filter-section h3 {
  color: rgb(54, 73, 226);
  font-weight: 600;
}

/* Activities container in extracurricular activities page */
.activities-container {
  padding-top: 10px;
  /* Add a subtle background to the activities section */
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  padding: 20px;
  /* Improve spacing between activity cards */
  /* No results message styling */
}
.activities-container .blog-post {
  margin-bottom: 30px;
}
.activities-container .no-results-message {
  background-color: rgba(255, 255, 255, 0.8);
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}
.activities-container .no-results-message p {
  color: rgb(54, 73, 226);
  font-weight: 500;
}

/* School Categories Styles */
/* Home page school category card */
.home-category-card {
  transition: all 0.3s ease;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  border-radius: 10px;
  position: relative;
  background-color: #ffffff;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.08);
}
.home-category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
}

.school-category {
  transition: all 0.3s ease;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  padding: 0;
  border-radius: 10px;
  position: relative;
  background-color: #ffffff;
  height: 100%;
  line-height: 1.5rem;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.08);
}
.school-category:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
}
.school-category img {
  width: 100%;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
  margin-bottom: 0 !important;
  border-radius: 10px 10px 0 0;
}
.school-category h3 {
  margin-top: 15px;
  text-align: center;
  padding: 0 15px;
}
.school-category p {
  flex-grow: 1;
  padding: 0 15px;
}
.school-category .text-more-info {
  display: inline-block;
  margin: 15px auto;
  text-align: center;
  width: auto;
  background-color: rgb(54, 73, 226);
  color: white !important;
  padding: 8px 20px;
  border-radius: 5px;
  font-weight: 500;
  transition: all 0.3s ease;
}
.school-category .text-more-info:hover {
  background-color: #1d30c8;
  color: white !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Category Info Box Styles */
.category-info-box {
  border: 1px solid #eaeaea;
  margin-bottom: 10px;
}
.category-info-box h3 {
  color: rgb(54, 73, 226);
  font-size: 1.2rem;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 5px;
}
.category-info-box p {
  margin-bottom: 10px;
}

/* Category Sidebar Styles */
.category-sidebar-item {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
.category-sidebar-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Single Extracurricular Page Styles */
.activity-item {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
.activity-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.activity-link {
  display: block;
  text-decoration: none;
  color: inherit;
}
.activity-link:hover {
  text-decoration: none;
  color: inherit;
}

/* Activity Single Page Styles */
.activity-content {
  line-height: 1.6;
}
.activity-content p {
  margin-bottom: 1rem;
}
.activity-content h3,
.activity-content h4 {
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}
.activity-content ul,
.activity-content ol {
  margin-left: 1.5rem;
  margin-bottom: 1.5rem;
}
.activity-content img {
  max-width: 100%;
}

/* 404 Error Page Styles */
.error-404 {
  text-align: center;
}
.error-404 .error-icon {
  font-size: 120px;
  color: rgb(171, 153, 34);
  margin-bottom: 20px;
}
.error-404 .error-title {
  font-size: 2.5rem;
  margin-bottom: 20px;
  color: rgb(54, 73, 226);
}
.error-404 .error-description {
  font-size: 1.2rem;
  margin-bottom: 30px;
}
.error-404 .error-actions {
  margin-top: 30px;
}
.error-404 .error-actions .text-more-info {
  margin: 0 10px;
}
.error-404 .search-form {
  max-width: 500px;
  margin: 0 auto;
}
.error-404 .search-form input[type=search] {
  border: 1px solid #cccccc;
  border-radius: 5px;
  padding: 10px 15px;
  width: 70%;
}
.error-404 .search-form button {
  background-color: rgb(54, 73, 226);
  color: white;
  border: none;
  border-radius: 5px;
  padding: 10px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}
.error-404 .search-form button:hover {
  background-color: #1d30c8;
}

/* Job Listings and Job Detail Page Styles */
/* Compatible with WordPress custom post types */
.job-card,
.type-job,
.hentry.job {
  transition: all 0.3s ease;
}
.job-card .job-card-inner,
.job-card .entry-content-wrap,
.type-job .job-card-inner,
.type-job .entry-content-wrap,
.hentry.job .job-card-inner,
.hentry.job .entry-content-wrap {
  border-radius: 10px;
  padding: 20px;
  background-color: #ffffff; /* Ensure white background */
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}
.job-card .job-card-inner:hover,
.job-card .entry-content-wrap:hover,
.type-job .job-card-inner:hover,
.type-job .entry-content-wrap:hover,
.hentry.job .job-card-inner:hover,
.hentry.job .entry-content-wrap:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  transform: translateY(-3px);
}
.job-card h3,
.job-card .entry-title,
.type-job h3,
.type-job .entry-title,
.hentry.job h3,
.hentry.job .entry-title {
  margin-top: 0;
}
.job-card h3 a,
.job-card .entry-title a,
.type-job h3 a,
.type-job .entry-title a,
.hentry.job h3 a,
.hentry.job .entry-title a {
  color: inherit;
  text-decoration: none;
}
.job-card h3 a:hover,
.job-card .entry-title a:hover,
.type-job h3 a:hover,
.type-job .entry-title a:hover,
.hentry.job h3 a:hover,
.hentry.job .entry-title a:hover {
  text-decoration: none;
}

.job-meta {
  background-color: rgba(54, 73, 226, 0.05);
  padding: 10px;
  border-radius: 5px;
}

.job-excerpt {
  font-style: italic;
  border-left: 3px solid rgb(54, 73, 226);
  padding-left: 15px;
}

.job-description,
.entry-content {
  /* WordPress image alignment classes */
}
.job-description h3,
.entry-content h3 {
  margin-top: 25px;
  margin-bottom: 10px;
  color: rgb(54, 73, 226);
}
.job-description ul,
.job-description ol,
.entry-content ul,
.entry-content ol {
  margin-left: 20px;
}
.job-description img.alignright,
.entry-content img.alignright {
  float: right;
  margin: 0 0 1em 1em;
}
.job-description img.alignleft,
.entry-content img.alignleft {
  float: left;
  margin: 0 1em 1em 0;
}
.job-description img.aligncenter,
.entry-content img.aligncenter {
  display: block;
  margin-left: auto;
  margin-right: auto;
}
.job-description .alignright,
.entry-content .alignright {
  float: right;
  margin: 0 0 1em 1em;
}
.job-description .alignleft,
.entry-content .alignleft {
  float: left;
  margin: 0 1em 1em 0;
}
.job-description .aligncenter,
.entry-content .aligncenter {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.job-application {
  background-color: #eeeeee;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid rgb(226, 207, 54);
}

.job-actions {
  margin-top: 20px;
}
.job-actions .button {
  margin-right: 10px;
  margin-bottom: 10px;
  transition: all 0.3s ease;
}
.job-actions .button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.activity-header {
  padding: 10px;
  border-radius: 8px 8px 0 0;
  transition: all 0.3s ease;
}
.activity-header:hover {
  background-color: #f0f0f0;
}

.activity-thumbnail {
  border-radius: 5px;
}

.activity-details {
  border-top: 1px solid #e0e0e0;
  border-radius: 0 0 8px 8px;
}

.cursor-pointer {
  cursor: pointer;
}

.background-light-grey {
  background-color: #eeeeee;
  border-radius: 8px;
}

.category-sidebar-image {
  width: 100%;
  height: 100px;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 8px 8px 0 0;
}

.category-sidebar-title {
  padding: 10px;
  text-align: center;
  font-weight: 500;
  color: #222;
  background-color: #f8f8f8;
}

.category-sidebar-link {
  text-decoration: none;
  color: inherit;
}
.category-sidebar-link:hover {
  text-decoration: none;
}

/* Blog Post Styling */
.blog-post {
  transition: all 0.3s ease;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  padding: 10px;
  border-radius: 10px;
  position: relative;
  background-color: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.blog-image {
  margin-top: 8px;
}

.blog-post:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
}

/* Gallery card specific styling */
.gallery-card {
  padding: 0 0 0 0 !important; /* Remove all padding for gallery cards */
  background-color: #ffffff !important; /* Apply white background to entire card */
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05) !important; /* Enhanced shadow */
  overflow: hidden; /* Ensure content doesn't overflow */
  transition: all 0.3s ease;
  margin-bottom: 15px !important; /* Space between the info button and the bottom edge of the card*/
  border-radius: 10px !important; /* Round all corners of the card */
  border: 1px solid rgba(0, 0, 0, 0.08); /* Subtle border for better definition */
}

.gallery-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08) !important; /* Enhanced shadow on hover */
}

/* Team Card Styling */
.card {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
  margin-bottom: 0; /* Changed from 5px to remove extra space */
  padding: 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.08); /* Subtle border for better definition */
  /* Ensure no internal spacing that might create gaps */
  box-sizing: border-box;
  /* Content wrapper for all text and buttons */
  /* Remove spacer that was pushing content apart */
}
.card:hover {
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
  transform: translateY(-5px);
}
.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}
.card .team-photo {
  width: 100%;
  display: block;
  transition: all 0.3s ease;
  margin: 0;
  padding: 0;
}
.card h3 {
  margin-top: 10px;
  font-size: 1.3rem;
}
.card h3,
.card .title,
.card .read-all {
  padding-left: 15px;
  padding-right: 15px;
  margin-top: 15px; /* Add space between image and text */
}
.card .team-link {
  color: rgb(54, 73, 226);
  text-decoration: none;
  transition: color 0.3s ease;
}
.card .team-link:hover {
  color: #1d30c8;
}
.card .title {
  color: #222;
  font-size: 1.1rem !important;
  font-weight: 300;
  margin: 5px 0 5px 0;
  padding: 0 10px;
}
.card:after {
  display: none; /* Hide the spacer completely */
}
.card .read-all {
  display: inline-block;
  margin: 5px 0 0; /* Removed bottom margin completely */
  padding: 8px 20px;
  background-color: rgb(54, 73, 226);
  color: white;
  border-radius: 10px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}
.card .read-all:hover {
  background-color: #1d30c8;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Fix for modal content */
.modal {
  border-radius: 12px;
  max-width: 800px;
  margin: 0 auto;
}
.modal h2,
.modal h3 {
  color: rgb(54, 73, 226);
}
.modal .margin-bottom.padding {
  padding: 20px 30px;
}
.modal .modal-close-button {
  background-color: rgb(54, 73, 226);
  color: white;
  border-radius: 30px;
  padding: 8px 20px;
  display: inline-block;
  margin: 0 auto 20px;
  transition: all 0.3s ease;
}
.modal .modal-close-button:hover {
  background-color: #1d30c8;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Gallery label styling */
.gallery-label {
  position: absolute;
  top: 25px;
  right: 15px;
  background-color: rgb(54, 73, 226);
  color: white;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  z-index: 2;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Extracurricular label styling */
.extracurricular-label {
  background-color: rgb(226, 207, 54);
  color: #333;
  font-weight: 600;
}

/* Teacher category header styling */
.teacher-category-header {
  background-color: rgb(54, 73, 226);
  color: white;
  padding: 10px 0;
  font-size: 0.9rem;
  font-weight: 500;
  text-align: center;
  width: 100%;
  border-radius: 10px 10px 0 0;
  margin: 0;
  /* Category link styling */
}
.teacher-category-header .category-link {
  color: white;
  text-decoration: none;
  display: block;
  width: 100%;
  height: 100%;
  transition: all 0.3s ease;
}
.teacher-category-header .category-link:hover {
  color: white;
  text-decoration: none;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 10px 10px 0 0;
}
.teacher-category-header .category-link:focus {
  color: white;
  text-decoration: none;
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: -2px;
}

/* Teaching staff card specific styling */
.card.gallery-card[data-category] {
  /* Team photo with sharp corners */
}
.card.gallery-card[data-category] .gallery-content {
  padding: 15px;
  padding-bottom: 0; /* Removed bottom padding */
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.card.gallery-card[data-category] .gallery-content h3 {
  margin-top: 10px;
  margin-bottom: 5px;
}
.card.gallery-card[data-category] .gallery-content .title {
  margin-bottom: 8px; /* Reduced bottom margin */
}
.card.gallery-card[data-category] .gallery-content .read-all {
  display: inline-block;
  margin-bottom: 0; /* Removed bottom margin */
  margin-top: auto; /* Push the button to the bottom of the flex container */
}
.card.gallery-card[data-category] .team-photo {
  width: 100%;
  display: block;
  margin: 0;
  padding: 0;
  border-radius: 0 !important; /* Sharp corners */
}

/* Mini teacher cards for school category pages */
.teacher-mini-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.teacher-mini-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}
.teacher-mini-card .teacher-mini-photo {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  -o-object-fit: cover;
     object-fit: cover;
  margin: 0 auto 10px;
  display: block;
}
.teacher-mini-card h4 {
  font-size: 1rem;
  font-weight: 600;
  color: rgb(54, 73, 226);
}
.teacher-mini-card p {
  color: #666;
  margin-bottom: 0;
}

/* Management page specific styles */
.page-management .gallery-card {
  padding-bottom: 0 !important;
}
.page-management .gallery-card .gallery-content {
  padding-bottom: 0 !important;
}
.page-management .gallery-card .gallery-content div {
  margin-bottom: 0 !important;
}
.page-management .gallery-card .gallery-content .read-all {
  margin-bottom: 0 !important;
}

/* Gallery image hover effect */
.gallery-card a img.rounded-image, .gallery-card a .carousel-default .item img, .carousel-default .item .gallery-card a img, .gallery-card a .grid.margin article.s-12.m-12.l-6.margin-m-bottom:last-child img, .grid.margin article.s-12.m-12.l-6.margin-m-bottom:last-child .gallery-card a img,
.gallery-card a .team-photo.rounded-image {
  transition: all 0.3s ease;
  border-radius: 10px 10px 0 0 !important; /* Rounded top corners only for gallery images */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Add shadow to the image */
  width: 100%; /* Ensure image takes full width */
  display: block; /* Remove any extra space */
}

.gallery-card a:hover img.rounded-image, .gallery-card a:hover .carousel-default .item img, .carousel-default .item .gallery-card a:hover img, .gallery-card a:hover .grid.margin article.s-12.m-12.l-6.margin-m-bottom:last-child img, .grid.margin article.s-12.m-12.l-6.margin-m-bottom:last-child .gallery-card a:hover img,
.gallery-card a:hover .team-photo.rounded-image {
  opacity: 0.85;
  transform: scale(1.02);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Gallery card text content */
.gallery-content {
  padding: 15px 15px 0px; /* Removed bottom padding to fix extra space */
  margin-top: 0;
  margin-bottom: 0; /* Added to remove bottom margin */
  /* Fix for the container of the View Bio button */
}
.gallery-content h3 {
  margin-top: 0;
  margin-bottom: 8px; /* Reduced bottom margin */
}
.gallery-content p {
  margin-bottom: 8px; /* Reduced bottom margin */
}
.gallery-content .text-more-info {
  display: inline-block;
  margin-bottom: 0; /* Removed bottom margin */
}
.gallery-content div {
  margin-bottom: 0 !important; /* Remove bottom margin from button container */
}

.blog-post img.rounded-image, .blog-post .carousel-default .item img, .carousel-default .item .blog-post img, .blog-post .grid.margin article.s-12.m-12.l-6.margin-m-bottom:last-child img, .grid.margin article.s-12.m-12.l-6.margin-m-bottom:last-child .blog-post img {
  width: 100%;
  border-radius: 10px 10px 0 0;
}

.blog-post h3 {
  line-height: 1.3;
}

.blog-post p {
  margin: 10px 0;
  line-height: 1.5;
}

.blog-author a,
.blog-category a {
  color: #555;
  text-decoration: none;
  transition: color 0.3s ease;
}

.blog-author a:hover,
.blog-category a:hover {
  color: #0074d9;
}

/* Nationality Dropdown Styling */
select[name=nationality],
select[name=nationality_f],
select[name=nationality_m] {
  max-height: 300px;
  overflow-y: auto !important;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
}
select[name=nationality] option,
select[name=nationality_f] option,
select[name=nationality_m] option {
  padding: 8px 12px;
}
select[name=nationality] option:hover,
select[name=nationality_f] option:hover,
select[name=nationality_m] option:hover {
  background-color: #f5f5f5;
}

/* Nationality Dropdown Styling */
select[name=nationality],
select[name=nationality_f],
select[name=nationality_m] {
  max-height: 300px;
  overflow-y: auto !important;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
}
select[name=nationality] option,
select[name=nationality_f] option,
select[name=nationality_m] option {
  padding: 8px 12px;
}
select[name=nationality] option:hover,
select[name=nationality_f] option:hover,
select[name=nationality_m] option:hover {
  background-color: #f5f5f5;
}

/* Pagination Styling - Removed duplicate styles */
/* Blog Details Link Styling */
.blog-categories {
  background-color: rgb(171, 153, 34);
  padding: 0.8rem;
  margin-bottom: 20px;
  color: #ffffff;
}

.blog-post .text-more-info {
  display: inline-block;
  margin-top: 10px;
  font-weight: 500;
  position: relative;
  padding-right: 20px;
}

.blog-post .text-more-info:hover:after {
  transform: translateX(5px);
}

/* Blog Post Category Tags */
.blog-category a {
  background-color: #f1f1f1;
  padding: 3px 8px;
  border-radius: 3px;
  font-size: 0.85em;
  transition: background-color 0.3s ease;
}

.blog-category a:hover {
  background-color: #e0e0e0;
}

/* Footer styles */
footer .section.background-blue {
  border-radius: 10px 10px 0 0;
  overflow: hidden;
}
footer .bottom-footer {
  margin-bottom: 0.5rem;
}
footer .padding.background-yellow {
  border-radius: 0 0 10px 10px;
  overflow: hidden;
}

.footer-links {
  border-top: 1px solid rgb(226, 207, 54);
  padding-top: 0.625rem;
}
.footer-links p {
  margin-bottom: 15px; /* Adjust this value to increase/decrease the gap */
}

.admin-contact-info {
  padding-top: 1rem;
  margin-top: 2rem;
}

.admin-info-item {
  background: rgba(54, 73, 226, 0.6);
  padding: 0.8rem !important;
  padding-top: 0.8rem !important;
  border-radius: 10px;
  overflow: hidden;
}

.footer-links {
  border-top: 1px solid rgb(226, 207, 54);
  padding-top: 1rem;
}
.footer-links p {
  margin-bottom: 10px; /* Adjust this value to increase/decrease the gap */
}

footer h4 {
  color: rgb(226, 207, 54) !important;
  font-family: "Noto Serif Georgian", serif;
  font-optical-sizing: auto;
  font-weight: 600;
  font-style: normal;
  font-variation-settings: "wdth" 100;
}

.admin-name {
  word-wrap: break-word;
}

.admin-phone {
  /* Prevent aggressive word breaking but allow natural wrapping */
  word-break: keep-all;
  overflow-wrap: break-word;
  -webkit-hyphens: none;
          hyphens: none;
  /* Ensure phone numbers don't overflow their container */
  max-width: 100%;
  box-sizing: border-box;
  /* Allow wrapping at natural break points (spaces, hyphens) */
  white-space: normal;
  /* For very long phone numbers without spaces, allow breaking as last resort */
  word-wrap: break-word;
}

/* Slider styles */
/*** Navigation bar styles */
.top-nav li a,
.background-white .top-nav li a {
  color: #002633;
  font-size: 1rem;
  padding: 0.7em 1.25em;
}

nav {
  border-bottom: 4px solid rgba(0, 0, 0, 0.05);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: 0.5rem 0;
  position: relative;
  z-index: 15;
}

.top-nav ul ul {
  background: rgb(226, 207, 54) none repeat scroll 0 0;
}

.top-nav li ul li {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.top-nav li ul li:last-child {
  border-bottom: 0;
}

.top-nav li ul li a,
.background-white .top-nav li ul li a,
.top-nav .active-item li a {
  background: #eeeeee none repeat scroll 0 0;
  color: rgb(54, 73, 226);
}

@media screen and (min-width: 769px) {
  footer h4 {
    text-align: center !important;
  }
  .developer-credit {
    text-align: right;
  }
}
/* Application Form Styles */
.application-form {
  color: rgb(54, 73, 226);
  font-weight: 600;
}

.any-error {
  color: #c81010;
  font-weight: bold;
}

.any-success {
  color: #06a10b;
  font-weight: bold;
}

/* Style for required fields */
form.customform input.required,
form.customform select.required,
form.customform textarea.required {
  border-left: 4px solid #c81010;
}

/* Improve form field spacing */
form.customform input,
form.customform select,
form.customform textarea {
  margin-bottom: 1rem;
}

/* Style for form section headings */
h3.application-form {
  margin-top: 2rem;
  font-size: 1.4rem;
}

h4.text-left {
  margin-top: 1.5rem;
  color: rgb(54, 73, 226);
  font-weight: 600;
}

/* Style for submit and reset buttons */
.submit-btn {
  background-color: rgb(54, 73, 226) !important;
  transition: background-color 0.3s ease;
}

.submit-btn:hover {
  background-color: #1d30c8 !important;
}

.cancel-btn {
  background-color: #c81010 !important;
  transition: background-color 0.3s ease;
}

.cancel-btn:hover {
  background-color: #990c0c !important;
}

/* Datepicker styling */
.ui-datepicker {
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
  padding: 10px;
}

.ui-datepicker-header {
  background-color: rgb(54, 73, 226);
  color: white;
  border-radius: 3px;
  padding: 5px;
}

.ui-datepicker-calendar th {
  padding: 5px;
  text-align: center;
}

.ui-datepicker-calendar td {
  padding: 2px;
  text-align: center;
}

.ui-datepicker-calendar a {
  display: block;
  padding: 5px;
  text-decoration: none;
  border-radius: 3px;
}

.ui-datepicker-calendar a:hover {
  background-color: rgb(226, 207, 54);
  color: white;
}

/* Mobile styles */
@media screen and (max-width: 768px) {
  /* Ensure the hamburger menu is visible */
  .nav-text {
    display: block !important;
  }
  .top-nav li a,
  .background-white .top-nav li a {
    background: #eeeeee none repeat scroll 0 0;
    color: #000;
    font-size: 1.2em;
    padding: 1em;
    text-align: center;
  }
  .top-nav li ul li ul li a {
    background: none repeat scroll 0 0 #456274;
  }
  .top-nav {
    background: none repeat scroll 0 0 #eeeeee;
  }
  .top-nav li ul li a,
  .background-white .top-nav li ul li a,
  .top-nav .active-item li a {
    background: #cccccc none repeat scroll 0 0;
    color: #000;
  }
  h1 {
    font-size: 1.7rem;
    text-align: center;
  }
  h2 {
    font-size: 1.4rem;
  }
  h3 {
    font-size: 1.1rem;
  }
  h4 {
    font-size: 1rem;
  }
  .blog-post h3 {
    font-size: 1.1rem;
  }
  .homepage-headers,
  .page-headers {
    font-size: 1.4rem;
  }
  .number-stat {
    margin-bottom: 15px;
  }
  /* More info button */
  a.text-more-info {
    margin-bottom: 2rem;
  }
  hr.break-alt {
    margin: 5px 0 !important;
  }
  /* Mobile centered menu styles */
  .centered-menu {
    display: block;
  }
  .centered-menu > li {
    display: block;
  }
  .admin-info-item {
    margin-bottom: 1rem;
    text-align: center;
  }
  footer h4 {
    text-align: left;
  }
}
@media screen and (max-width: 480px) {
  .developer-credit {
    margin-top: 1rem !important;
  }
}
/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .blog-post {
    margin-bottom: 30px;
  }
}
@media screen and (max-width: 480px) {
  .pagination li a {
    padding: 6px 12px;
  }
  .blog-author,
  .blog-category,
  .blog-date {
    font-size: 0.8rem;
  }
}
/* Fix for teacher cards grid to prevent cards from bumping into each other */
.grid.margin {
  grid-row-gap: 20px; /* Add vertical spacing between rows for all grids */
}

/* Specific styling for teacher cards grid */
.teacher-cards-grid {
  grid-row-gap: 50px !important; /* Add more vertical spacing between rows for teacher cards */
  display: grid !important; /* Ensure grid display is enforced */
  margin-bottom: 30px !important; /* Add bottom margin to the entire grid */
}

/* Utility class for smaller bottom margins */
.margin-bottom-5 {
  margin-bottom: 5px !important;
}

/* Specific styling for management page cards */
.page-management .gallery-card .gallery-content {
  padding: 10px 10px 5px; /* Further reduced padding for management page */
}

/* Pagination Styles */
.pagination {
  display: flex;
  justify-content: center;
  list-style: none;
  padding: 0;
  margin: 30px 0;
  flex-wrap: wrap;
}

.pagination li {
  margin: 0 5px;
  display: inline-block;
}

.pagination a {
  display: block;
  padding: 8px 15px;
  text-decoration: none;
  color: #222;
  background-color: #ffffff;
  border: 1px solid #cccccc;
  border-radius: 5px;
  transition: all 0.3s ease;
}
.pagination a:hover {
  background-color: #e8eafc;
  color: rgb(54, 73, 226);
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}
.pagination a.active-page {
  background-color: rgb(54, 73, 226);
  color: #ffffff;
  border-color: rgb(54, 73, 226);
  font-weight: bold;
}
.pagination a.previous-page, .pagination a.next-page {
  background-color: #eeeeee;
}
.pagination a.previous-page:hover, .pagination a.next-page:hover {
  background-color: #cccccc;
  color: #222;
}
.pagination a.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* WordPress Default Pagination Styles */
.pagination-nav {
  margin: 0; /* Remove margin since the container has padding */
  background-color: transparent !important; /* Override Responsee background */
}
.pagination-nav .page-numbers {
  display: flex !important;
  justify-content: center !important;
  flex-wrap: wrap !important;
  list-style: none !important;
  padding: 0 !important;
  margin: 0 !important;
  background-color: transparent !important; /* Ensure no background from Responsee */
  /* Style for previous and next buttons */
}
.pagination-nav .page-numbers li {
  margin: 0 5px 5px 0 !important;
  display: inline-block !important;
  list-style-type: none !important;
  background-color: transparent !important; /* Ensure no background from Responsee */
  padding: 0 !important; /* Reset padding */
}
.pagination-nav .page-numbers a,
.pagination-nav .page-numbers span {
  display: inline-block !important;
  padding: 8px 15px !important;
  background-color: #ffffff !important;
  border: 1px solid #cccccc !important;
  border-radius: 5px !important;
  color: #222 !important;
  text-decoration: none !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}
.pagination-nav .page-numbers a:hover,
.pagination-nav .page-numbers span:hover {
  background-color: #e8eafc !important;
  color: rgb(54, 73, 226) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15) !important;
}
.pagination-nav .page-numbers .current {
  background-color: rgb(54, 73, 226) !important;
  color: #ffffff !important;
  border-color: rgb(54, 73, 226) !important;
  font-weight: bold !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
}
.pagination-nav .page-numbers .dots {
  background: none !important;
  border: none !important;
  padding: 8px 5px !important;
}
.pagination-nav .page-numbers .prev,
.pagination-nav .page-numbers .next {
  background-color: rgb(226, 207, 54) !important;
  color: #ffffff !important;
  border-color: rgb(226, 207, 54) !important;
  font-weight: bold !important;
}
.pagination-nav .page-numbers .prev:hover,
.pagination-nav .page-numbers .next:hover {
  background-color: #c8b51d !important;
  border-color: #c8b51d !important;
  color: #ffffff !important;
}

/* Add specific spacing control for pagination container */
.pagination-container {
  margin-top: 30px; /* Keep space above pagination */
  margin-bottom: 15px; /* Reduce space below pagination by half */
  background-color: rgba(255, 255, 255, 0.7) !important; /* Semi-transparent white background */
  border-radius: 10px !important; /* Rounded corners */
  padding: 15px !important; /* Add some padding */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05) !important; /* Subtle shadow */
  /* Special styling when inside a rounded div */
}
.rounded-div .pagination-container {
  background-color: rgba(255, 255, 255, 0.8) !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
  margin-top: 20px;
  margin-bottom: 0;
  border-top: 1px solid rgba(54, 73, 226, 0.2);
  padding-top: 20px !important;
  /* Make pagination more visible for testing */
}
.rounded-div .pagination-container .pagination a {
  font-weight: 600;
  padding: 10px 18px;
  margin: 0 3px;
}
.rounded-div .pagination-container .pagination a.active-page {
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

/* Additional spacing control for specific sections */
.section .line:last-child .pagination-container {
  margin-bottom: 15px; /* Ensure consistent spacing before footer */
}

/* Ensure proper spacing after content */
.grid.margin + .pagination-container {
  margin-top: 30px; /* Maintain space after content */
}

/* Responsive adjustments for pagination */
@media screen and (max-width: 768px) {
  .pagination-nav .page-numbers li {
    margin-bottom: 10px;
  }
}
/* Contact Form Styles */
.contact-form {
  position: relative;
  /* Admin phone number call button styles */
  /* Legal Pages Template Styles */
}
.contact-form .form-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 100;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
}
.contact-form .spinner {
  display: inline-block;
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid rgb(54, 73, 226);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
.contact-form .form-message {
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  display: none;
  text-align: center;
  font-weight: bold;
}
.contact-form .form-message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}
.contact-form .form-message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}
.contact-form .admin-phone .call-button {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 5px;
  transition: all 0.3s ease;
  text-decoration: none;
  background-color: rgba(226, 207, 54, 0.2);
  /* Make more touch-friendly on mobile */
}
.contact-form .admin-phone .call-button:hover, .contact-form .admin-phone .call-button:focus {
  background-color: rgba(226, 207, 54, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}
@media (max-width: 768px) {
  .contact-form .admin-phone .call-button {
    padding: 8px 15px;
    margin: 5px 0;
    display: block;
    font-size: 1.1rem;
  }
}
.contact-form .legal-content {
  font-size: 1rem;
  line-height: 1.6;
}
.contact-form .legal-content h2 {
  font-size: 1.5rem;
  margin-top: 2rem;
  margin-bottom: 1rem;
  color: rgb(54, 73, 226);
}
.contact-form .legal-content h3 {
  font-size: 1.3rem;
  margin-top: 1.5rem;
  margin-bottom: 0.8rem;
}
.contact-form .legal-content p {
  margin-bottom: 1rem;
}
.contact-form .legal-content ul, .contact-form .legal-content ol {
  margin-left: 1.5rem;
  margin-bottom: 1.5rem;
}
.contact-form .legal-content li {
  margin-bottom: 0.5rem;
}
.contact-form .legal-content table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1.5rem;
}
.contact-form .legal-content table th, .contact-form .legal-content table td {
  padding: 0.75rem;
  border: 1px solid #ddd;
}
.contact-form .legal-content table th {
  background-color: white;
  font-weight: bold;
}
.contact-form .legal-content table tr:nth-child(even) {
  background-color: #f9f9f9;
}
.contact-form .legal-sidebar {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
.contact-form .legal-sidebar.padding-2x {
  padding: 2rem !important; /* Override the padding-2x class for legal-sidebar */
}

/* End of .contact-form */
/* Standalone legal navigation styles that apply everywhere */
.legal-nav {
  list-style: none;
  padding: 0;
  margin: 0 0 1.5rem 0;
}
.legal-nav li {
  margin-bottom: 1rem; /* Further increased spacing between list items */
}
.legal-nav a {
  display: block;
  padding: 0.5rem 1.5rem; /* Increased padding for all links */
  background-color: #f5f5f5;
  border-left: 3px solid transparent;
  transition: all 0.3s ease;
  text-decoration: none;
  color: #222;
  border-radius: 5px;
}
.legal-nav a:hover {
  background-color: #e9e9e9;
  border-left-color: rgb(226, 207, 54);
  transform: translateX(3px);
  padding: 0.5rem 1.5rem !important; /* Ensure there's padding inside the border */
}
.legal-nav a.active-link {
  background-color: white;
  border-left-color: rgb(54, 73, 226);
  color: rgb(54, 73, 226);
  font-weight: bold;
  border: 2px solid rgb(226, 207, 54); /* Add border with secondary color */
  padding: 0.2rem 1.5rem !important; /* Ensure there's padding inside the border */
  margin: 0.2rem 0; /* Add margin for better spacing between items */
  line-height: 1.8; /* Further increased line height for better readability */
}

.legal-contact {
  background-color: #f9f4d2;
  padding: 1.5rem;
  border-radius: 4px;
}
.legal-contact a {
  color: rgb(54, 73, 226);
  font-weight: bold;
  text-decoration: underline;
}
.legal-contact a:hover {
  color: #1a2ab2;
}

.background-light-gray {
  background-color: #f8f8f8;
}

.submit-btn {
  position: relative;
  transition: all 0.3s ease;
}
.submit-btn.submitting {
  background-color: #cccccc !important;
  cursor: not-allowed;
}
.submit-btn:hover {
  background-color: #1d30c8 !important;
}

.spinner-text {
  display: inline-block;
  position: relative;
}
.spinner-text:after {
  content: "...";
  position: absolute;
  right: -12px;
  animation: ellipsis 1.5s infinite;
}

@keyframes ellipsis {
  0% {
    content: ".";
  }
  33% {
    content: "..";
  }
  66% {
    content: "...";
  }
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* Breadcrumb Styles */
.breadcrumb-container {
  background-color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 0;
  margin: 0;
  position: relative;
  z-index: 5;
}
.breadcrumb-container .breadcrumb-wrapper {
  background-color: #ffffff;
  padding: 4px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin: 0.625rem 0;
  border-left: 3px solid rgb(54, 73, 226);
  /* Style the breadcrumb navigation */
  /* Handle different breadcrumb plugin structures */
  /* Responsive adjustments */
}
.breadcrumb-container .breadcrumb-wrapper nav,
.breadcrumb-container .breadcrumb-wrapper .flexy-breadcrumb,
.breadcrumb-container .breadcrumb-wrapper .breadcrumb {
  margin: 0;
  padding: 0;
}
.breadcrumb-container .breadcrumb-wrapper nav ol,
.breadcrumb-container .breadcrumb-wrapper nav ul,
.breadcrumb-container .breadcrumb-wrapper .flexy-breadcrumb ol,
.breadcrumb-container .breadcrumb-wrapper .flexy-breadcrumb ul,
.breadcrumb-container .breadcrumb-wrapper .breadcrumb ol,
.breadcrumb-container .breadcrumb-wrapper .breadcrumb ul {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.breadcrumb-container .breadcrumb-wrapper nav ol li,
.breadcrumb-container .breadcrumb-wrapper nav ul li,
.breadcrumb-container .breadcrumb-wrapper .flexy-breadcrumb ol li,
.breadcrumb-container .breadcrumb-wrapper .flexy-breadcrumb ul li,
.breadcrumb-container .breadcrumb-wrapper .breadcrumb ol li,
.breadcrumb-container .breadcrumb-wrapper .breadcrumb ul li {
  display: inline-flex;
  align-items: center;
  font-size: 0.9rem;
  line-height: 1.4;
  /* Current page (last item) styling */
}
.breadcrumb-container .breadcrumb-wrapper nav ol li:not(:last-child)::after,
.breadcrumb-container .breadcrumb-wrapper nav ul li:not(:last-child)::after,
.breadcrumb-container .breadcrumb-wrapper .flexy-breadcrumb ol li:not(:last-child)::after,
.breadcrumb-container .breadcrumb-wrapper .flexy-breadcrumb ul li:not(:last-child)::after,
.breadcrumb-container .breadcrumb-wrapper .breadcrumb ol li:not(:last-child)::after,
.breadcrumb-container .breadcrumb-wrapper .breadcrumb ul li:not(:last-child)::after {
  content: "›";
  margin: 0 8px;
  color: #cccccc;
  font-weight: bold;
  font-size: 1rem;
}
.breadcrumb-container .breadcrumb-wrapper nav ol li a,
.breadcrumb-container .breadcrumb-wrapper nav ul li a,
.breadcrumb-container .breadcrumb-wrapper .flexy-breadcrumb ol li a,
.breadcrumb-container .breadcrumb-wrapper .flexy-breadcrumb ul li a,
.breadcrumb-container .breadcrumb-wrapper .breadcrumb ol li a,
.breadcrumb-container .breadcrumb-wrapper .breadcrumb ul li a {
  color: rgb(54, 73, 226);
  text-decoration: none;
  transition: color 0.3s ease;
  font-weight: 500;
}
.breadcrumb-container .breadcrumb-wrapper nav ol li a:hover,
.breadcrumb-container .breadcrumb-wrapper nav ul li a:hover,
.breadcrumb-container .breadcrumb-wrapper .flexy-breadcrumb ol li a:hover,
.breadcrumb-container .breadcrumb-wrapper .flexy-breadcrumb ul li a:hover,
.breadcrumb-container .breadcrumb-wrapper .breadcrumb ol li a:hover,
.breadcrumb-container .breadcrumb-wrapper .breadcrumb ul li a:hover {
  color: rgb(171, 153, 34);
  text-decoration: underline;
}
.breadcrumb-container .breadcrumb-wrapper nav ol li:last-child,
.breadcrumb-container .breadcrumb-wrapper nav ul li:last-child,
.breadcrumb-container .breadcrumb-wrapper .flexy-breadcrumb ol li:last-child,
.breadcrumb-container .breadcrumb-wrapper .flexy-breadcrumb ul li:last-child,
.breadcrumb-container .breadcrumb-wrapper .breadcrumb ol li:last-child,
.breadcrumb-container .breadcrumb-wrapper .breadcrumb ul li:last-child {
  color: #222;
  font-weight: 600;
}
.breadcrumb-container .breadcrumb-wrapper .breadcrumb-item,
.breadcrumb-container .breadcrumb-wrapper .breadcrumb-link {
  display: inline-flex;
  align-items: center;
}
.breadcrumb-container .breadcrumb-wrapper .breadcrumb-item:not(:last-child)::after,
.breadcrumb-container .breadcrumb-wrapper .breadcrumb-link:not(:last-child)::after {
  content: "›";
  margin: 0 8px;
  color: #cccccc;
  font-weight: bold;
  font-size: 1rem;
}
@media (max-width: 768px) {
  .breadcrumb-container .breadcrumb-wrapper {
    padding: 10px 15px;
    margin: 8px 0;
  }
  .breadcrumb-container .breadcrumb-wrapper nav ol li,
  .breadcrumb-container .breadcrumb-wrapper nav ul li,
  .breadcrumb-container .breadcrumb-wrapper .flexy-breadcrumb ol li,
  .breadcrumb-container .breadcrumb-wrapper .flexy-breadcrumb ul li,
  .breadcrumb-container .breadcrumb-wrapper .breadcrumb ol li,
  .breadcrumb-container .breadcrumb-wrapper .breadcrumb ul li {
    font-size: 0.85rem;
  }
  .breadcrumb-container .breadcrumb-wrapper nav ol li:not(:last-child)::after,
  .breadcrumb-container .breadcrumb-wrapper nav ul li:not(:last-child)::after,
  .breadcrumb-container .breadcrumb-wrapper .flexy-breadcrumb ol li:not(:last-child)::after,
  .breadcrumb-container .breadcrumb-wrapper .flexy-breadcrumb ul li:not(:last-child)::after,
  .breadcrumb-container .breadcrumb-wrapper .breadcrumb ol li:not(:last-child)::after,
  .breadcrumb-container .breadcrumb-wrapper .breadcrumb ul li:not(:last-child)::after {
    margin: 0 6px;
    font-size: 0.9rem;
  }
}
@media (max-width: 480px) {
  .breadcrumb-container .breadcrumb-wrapper {
    padding: 8px 12px;
  }
  .breadcrumb-container .breadcrumb-wrapper nav ol li,
  .breadcrumb-container .breadcrumb-wrapper nav ul li,
  .breadcrumb-container .breadcrumb-wrapper .flexy-breadcrumb ol li,
  .breadcrumb-container .breadcrumb-wrapper .flexy-breadcrumb ul li,
  .breadcrumb-container .breadcrumb-wrapper .breadcrumb ol li,
  .breadcrumb-container .breadcrumb-wrapper .breadcrumb ul li {
    font-size: 0.8rem;
  }
  .breadcrumb-container .breadcrumb-wrapper nav ol li:not(:last-child)::after,
  .breadcrumb-container .breadcrumb-wrapper nav ul li:not(:last-child)::after,
  .breadcrumb-container .breadcrumb-wrapper .flexy-breadcrumb ol li:not(:last-child)::after,
  .breadcrumb-container .breadcrumb-wrapper .flexy-breadcrumb ul li:not(:last-child)::after,
  .breadcrumb-container .breadcrumb-wrapper .breadcrumb ol li:not(:last-child)::after,
  .breadcrumb-container .breadcrumb-wrapper .breadcrumb ul li:not(:last-child)::after {
    margin: 0 4px;
    font-size: 0.85rem;
  }
}

/* ========================================
 * COMPREHENSIVE GUTENBERG LIST STYLES
 * ========================================
 * 
 * Beautiful and functional list styles for WordPress Gutenberg editor
 * 
 * Usage Instructions:
 * 1. In Gutenberg editor, select a List block
 * 2. In the block settings sidebar, go to "Advanced" > "Additional CSS class(es)"
 * 3. Add one of the classes below
 * 
 * Available List Classes:
 * - list-checkmark: Green checkmarks for completed items
 * - list-star: Golden star bullets for featured items
 * - list-arrow: Modern arrow bullets
 * - list-numbered-circle: Circular numbered items
 * - list-timeline: Timeline-style list with connecting lines
 * - list-feature: Feature list with icons and descriptions
 * - list-steps: Step-by-step process list
 * - list-highlight: Highlighted important items
 * - list-minimal: Clean minimal style
 * - list-card: Card-style list items
 * - list-gradient: Gradient bullet points
 * - list-academic: Academic/educational style
 */
/* Base List Improvements */
.wp-block-list,
.entry-content ul,
.entry-content ol,
.post-content ul,
.post-content ol {
  margin: 1.5rem 0;
  padding-left: 0;
}
.wp-block-list li,
.entry-content ul li,
.entry-content ol li,
.post-content ul li,
.post-content ol li {
  margin-bottom: 0.75rem;
  line-height: 1.6;
  position: relative;
}

/* Remove default list markers for all custom list styles */
.list-checkmark,
.list-star,
.list-arrow,
.list-numbered-circle,
.list-timeline,
.list-feature,
.list-steps,
.list-highlight,
.list-minimal,
.list-card,
.list-gradient,
.list-academic {
  list-style: none !important;
  padding-left: 0 !important;
  margin-left: 0 !important;
  /* Target nested lists too */
}
.list-checkmark li,
.list-star li,
.list-arrow li,
.list-numbered-circle li,
.list-timeline li,
.list-feature li,
.list-steps li,
.list-highlight li,
.list-minimal li,
.list-card li,
.list-gradient li,
.list-academic li {
  list-style: none !important;
  list-style-type: none !important;
}
.list-checkmark li::marker,
.list-star li::marker,
.list-arrow li::marker,
.list-numbered-circle li::marker,
.list-timeline li::marker,
.list-feature li::marker,
.list-steps li::marker,
.list-highlight li::marker,
.list-minimal li::marker,
.list-card li::marker,
.list-gradient li::marker,
.list-academic li::marker {
  display: none !important;
}
.list-checkmark ul, .list-checkmark ol,
.list-star ul,
.list-star ol,
.list-arrow ul,
.list-arrow ol,
.list-numbered-circle ul,
.list-numbered-circle ol,
.list-timeline ul,
.list-timeline ol,
.list-feature ul,
.list-feature ol,
.list-steps ul,
.list-steps ol,
.list-highlight ul,
.list-highlight ol,
.list-minimal ul,
.list-minimal ol,
.list-card ul,
.list-card ol,
.list-gradient ul,
.list-gradient ol,
.list-academic ul,
.list-academic ol {
  list-style: none !important;
  padding-left: 0 !important;
  margin-left: 0 !important;
}
.list-checkmark ul li, .list-checkmark ol li,
.list-star ul li,
.list-star ol li,
.list-arrow ul li,
.list-arrow ol li,
.list-numbered-circle ul li,
.list-numbered-circle ol li,
.list-timeline ul li,
.list-timeline ol li,
.list-feature ul li,
.list-feature ol li,
.list-steps ul li,
.list-steps ol li,
.list-highlight ul li,
.list-highlight ol li,
.list-minimal ul li,
.list-minimal ol li,
.list-card ul li,
.list-card ol li,
.list-gradient ul li,
.list-gradient ol li,
.list-academic ul li,
.list-academic ol li {
  list-style: none !important;
  list-style-type: none !important;
}
.list-checkmark ul li::marker, .list-checkmark ol li::marker,
.list-star ul li::marker,
.list-star ol li::marker,
.list-arrow ul li::marker,
.list-arrow ol li::marker,
.list-numbered-circle ul li::marker,
.list-numbered-circle ol li::marker,
.list-timeline ul li::marker,
.list-timeline ol li::marker,
.list-feature ul li::marker,
.list-feature ol li::marker,
.list-steps ul li::marker,
.list-steps ol li::marker,
.list-highlight ul li::marker,
.list-highlight ol li::marker,
.list-minimal ul li::marker,
.list-minimal ol li::marker,
.list-card ul li::marker,
.list-card ol li::marker,
.list-gradient ul li::marker,
.list-gradient ol li::marker,
.list-academic ul li::marker,
.list-academic ol li::marker {
  display: none !important;
}

/* 1. Checkmark List - Perfect for completed tasks, achievements */
.list-checkmark li {
  position: relative;
  padding-left: 2.5rem;
  margin-bottom: 1rem;
}
.list-checkmark li::before {
  content: "✓";
  position: absolute;
  left: 0;
  top: 0;
  width: 1.8rem;
  height: 1.8rem;
  background: linear-gradient(135deg, #2ecc71, #27ae60);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.9rem;
  box-shadow: 0 2px 4px rgba(46, 204, 113, 0.3);
  transition: all 0.3s ease;
}
.list-checkmark li:hover::before {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(46, 204, 113, 0.4);
}

/* 2. Star List - Perfect for featured items, highlights */
.list-star li {
  position: relative;
  padding-left: 2.5rem;
  margin-bottom: 1rem;
}
.list-star li::before {
  content: "★";
  position: absolute;
  left: 0;
  top: 0;
  width: 1.8rem;
  height: 1.8rem;
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1rem;
  box-shadow: 0 2px 4px rgba(243, 156, 18, 0.3);
  transition: all 0.3s ease;
}
.list-star li:hover::before {
  transform: rotate(72deg) scale(1.1);
  box-shadow: 0 4px 8px rgba(243, 156, 18, 0.4);
}

/* 3. Arrow List - Modern and directional */
.list-arrow li {
  position: relative;
  padding-left: 2.5rem;
  margin-bottom: 1rem;
}
.list-arrow li::before {
  content: "→";
  position: absolute;
  left: 0;
  top: 0;
  width: 1.8rem;
  height: 1.8rem;
  background: linear-gradient(135deg, rgb(54, 73, 226), #2c3e50);
  color: white;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1rem;
  box-shadow: 0 2px 4px rgba(54, 73, 226, 0.3);
  transition: all 0.3s ease;
}
.list-arrow li:hover::before {
  transform: translateX(3px);
  box-shadow: 0 4px 8px rgba(54, 73, 226, 0.4);
}

/* 4. Numbered Circle List - Elegant numbered items */
.list-numbered-circle {
  counter-reset: circle-counter;
}
.list-numbered-circle li {
  position: relative;
  padding-left: 3rem;
  margin-bottom: 1.2rem;
  counter-increment: circle-counter;
}
.list-numbered-circle li::before {
  content: counter(circle-counter);
  position: absolute;
  left: 0;
  top: 0;
  width: 2rem;
  height: 2rem;
  background: linear-gradient(135deg, rgb(226, 207, 54), rgb(171, 153, 34));
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.9rem;
  box-shadow: 0 2px 6px rgba(226, 207, 54, 0.3);
  transition: all 0.3s ease;
}
.list-numbered-circle li:hover::before {
  transform: scale(1.15);
  box-shadow: 0 4px 12px rgba(226, 207, 54, 0.4);
}

/* 5. Timeline List - Perfect for chronological events */
.list-timeline {
  position: relative;
}
.list-timeline::before {
  content: "";
  position: absolute;
  left: 1rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, rgb(54, 73, 226), rgb(226, 207, 54));
  border-radius: 1px;
}
.list-timeline li {
  position: relative;
  padding-left: 3.5rem;
  margin-bottom: 2rem;
}
.list-timeline li::before {
  content: "";
  position: absolute;
  left: 0.5rem;
  top: 0.3rem;
  width: 1rem;
  height: 1rem;
  background: rgb(54, 73, 226);
  border: 3px solid white;
  border-radius: 50%;
  box-shadow: 0 0 0 2px rgb(54, 73, 226), 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
.list-timeline li:hover::before {
  transform: scale(1.3);
  background: rgb(226, 207, 54);
  box-shadow: 0 0 0 2px rgb(226, 207, 54), 0 4px 8px rgba(0, 0, 0, 0.2);
}
.list-timeline li:last-child {
  margin-bottom: 1rem;
}

/* 6. Feature List - Perfect for features, benefits */
.list-feature li {
  background: white;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  padding-left: 4rem;
}
.list-feature li::before {
  content: "◆";
  position: absolute;
  left: 1.5rem;
  top: 1.5rem;
  width: 1.5rem;
  height: 1.5rem;
  background: linear-gradient(135deg, rgb(54, 73, 226), rgb(226, 207, 54));
  color: white;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
}
.list-feature li:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-color: rgb(54, 73, 226);
}

/* 7. Steps List - Perfect for processes, instructions */
.list-steps {
  counter-reset: step-counter;
}
.list-steps li {
  position: relative;
  padding: 1.5rem 1.5rem 1.5rem 4.5rem;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-left: 4px solid rgb(54, 73, 226);
  border-radius: 0 8px 8px 0;
  counter-increment: step-counter;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}
.list-steps li::before {
  content: "Step " counter(step-counter);
  position: absolute;
  left: 1rem;
  top: 1rem;
  background: rgb(54, 73, 226);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
.list-steps li:hover {
  transform: translateX(5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
}

/* 8. Highlight List - Perfect for important points */
.list-highlight li {
  position: relative;
  padding: 1rem 1rem 1rem 3rem;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, rgba(226, 207, 54, 0.1), rgba(226, 207, 54, 0.05));
  border-left: 4px solid rgb(226, 207, 54);
  border-radius: 4px;
  transition: all 0.3s ease;
}
.list-highlight li::before {
  content: "!";
  position: absolute;
  left: 0.8rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.5rem;
  height: 1.5rem;
  background: rgb(226, 207, 54);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.9rem;
}
.list-highlight li:hover {
  background: linear-gradient(135deg, rgba(226, 207, 54, 0.15), rgba(226, 207, 54, 0.08));
  transform: translateX(3px);
}

/* 9. Minimal List - Clean and simple */
.list-minimal li {
  position: relative;
  padding-left: 2rem;
  margin-bottom: 0.8rem;
}
.list-minimal li::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0.7rem;
  width: 6px;
  height: 6px;
  background: rgb(54, 73, 226);
  border-radius: 50%;
  transition: all 0.3s ease;
}
.list-minimal li:hover::before {
  transform: scale(1.5);
  background: rgb(226, 207, 54);
}

/* 10. Card List - Each item as a card */
.list-card li {
  background: white;
  border: 1px solid #e1e8ed;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}
.list-card li:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: rgb(54, 73, 226);
}

/* 11. Gradient List - Colorful gradient bullets */
.list-gradient li {
  position: relative;
  padding-left: 2.5rem;
  margin-bottom: 1rem;
}
.list-gradient li::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0.3rem;
  width: 1.2rem;
  height: 1.2rem;
  background: linear-gradient(135deg, rgb(54, 73, 226), rgb(226, 207, 54));
  border-radius: 50%;
  transition: all 0.3s ease;
}
.list-gradient li:nth-child(2n)::before {
  background: linear-gradient(135deg, rgb(226, 207, 54), rgb(54, 73, 226));
}
.list-gradient li:nth-child(3n)::before {
  background: linear-gradient(135deg, #e74c3c, #f39c12);
}
.list-gradient li:nth-child(4n)::before {
  background: linear-gradient(135deg, #9b59b6, #3498db);
}
.list-gradient li:hover::before {
  transform: scale(1.3) rotate(180deg);
}

/* 12. Academic List - Perfect for educational content */
.list-academic li {
  position: relative;
  padding: 1rem 1rem 1rem 3.5rem;
  margin-bottom: 1rem;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  transition: all 0.3s ease;
}
.list-academic li::before {
  content: "📚";
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
  transition: all 0.3s ease;
}
.list-academic li:nth-child(2n)::before {
  content: "📝";
}
.list-academic li:nth-child(3n)::before {
  content: "🎓";
}
.list-academic li:nth-child(4n)::before {
  content: "📖";
}
.list-academic li:hover {
  background: white;
  border-color: rgb(54, 73, 226);
  transform: translateX(5px);
}
.list-academic li:hover::before {
  transform: translateY(-50%) scale(1.2);
}

/* Responsive Adjustments for All List Styles */
@media (max-width: 768px) {
  .list-checkmark li,
  .list-star li,
  .list-arrow li,
  .list-numbered-circle li,
  .list-timeline li,
  .list-feature li,
  .list-steps li,
  .list-highlight li,
  .list-minimal li,
  .list-card li,
  .list-gradient li,
  .list-academic li {
    font-size: 0.95rem;
    line-height: 1.5;
  }
  .list-feature li,
  .list-card li {
    padding: 1rem;
  }
  .list-steps li {
    padding: 1rem 1rem 1rem 3.5rem;
  }
  .list-timeline::before {
    left: 0.8rem;
  }
  .list-timeline li {
    padding-left: 3rem;
  }
  .list-timeline li::before {
    left: 0.3rem;
  }
}
@media (max-width: 480px) {
  .list-checkmark li,
  .list-star li,
  .list-arrow li,
  .list-numbered-circle li,
  .list-minimal li,
  .list-gradient li {
    padding-left: 2rem;
  }
  .list-checkmark li::before,
  .list-star li::before,
  .list-arrow li::before,
  .list-numbered-circle li::before,
  .list-minimal li::before,
  .list-gradient li::before {
    width: 1.4rem;
    height: 1.4rem;
    font-size: 0.8rem;
  }
  .list-numbered-circle li::before {
    width: 1.6rem;
    height: 1.6rem;
  }
  .list-feature li,
  .list-academic li {
    padding-left: 3rem;
  }
  .list-steps li {
    padding: 1rem 0.8rem 1rem 3rem;
  }
  .list-steps li::before {
    font-size: 0.7rem;
    padding: 0.2rem 0.6rem;
  }
}
/* ========================================
 * SIDEBAR TEXT LEGIBILITY IMPROVEMENTS
 * ========================================
 * Addressing small text issues in main sidebar
 * Improving readability by increasing font sizes
 */
/* Main Sidebar Text Size Improvements */
aside {
  /* Override small text sizes in sidebar for better legibility */
  /* Ensure sidebar headings are appropriately sized */
  /* Improve button text readability in sidebar */
  /* Ensure small text elements maintain minimum readable size */
  /* Card content improvements */
}
aside .text-size-12 {
  font-size: 14px !important; /* Increased from 12px to 14px */
  line-height: 1.5 !important; /* Improved line height for readability */
}
aside h5 {
  font-size: 1.1rem !important; /* Slightly larger than default */
  margin-bottom: 15px !important;
}
aside h6 {
  font-size: 1rem !important; /* Ensure h6 titles are readable */
  line-height: 1.4 !important;
  margin-bottom: 8px !important;
}
aside .button.text-size-12 {
  font-size: 13px !important; /* Slightly larger button text */
  padding: 8px 12px !important; /* Adjust padding for better proportion */
  line-height: 1.3 !important;
  color: white !important; /* Ensure white text on primary color buttons */
}
aside small,
aside .text-dark {
  font-size: 13px !important; /* Minimum readable size for small text */
  line-height: 1.4 !important;
}
aside .notification-card,
aside .event-card,
aside .blog-card {
  /* Ensure adequate spacing between elements */
}
aside .notification-card .text-size-12,
aside .event-card .text-size-12,
aside .blog-card .text-size-12 {
  font-size: 14px !important;
  color: #555 !important; /* Slightly darker for better contrast */
}
aside .notification-card .notification-item,
aside .notification-card .event-item,
aside .notification-card .blog-item,
aside .event-card .notification-item,
aside .event-card .event-item,
aside .event-card .blog-item,
aside .blog-card .notification-item,
aside .blog-card .event-item,
aside .blog-card .blog-item {
  margin-bottom: 12px !important;
}
aside .notification-card .notification-item:last-child,
aside .notification-card .event-item:last-child,
aside .notification-card .blog-item:last-child,
aside .event-card .notification-item:last-child,
aside .event-card .event-item:last-child,
aside .event-card .blog-item:last-child,
aside .blog-card .notification-item:last-child,
aside .blog-card .event-item:last-child,
aside .blog-card .blog-item:last-child {
  margin-bottom: 0 !important;
}

/* Responsive adjustments for sidebar text */
@media (max-width: 768px) {
  aside .text-size-12 {
    font-size: 15px !important; /* Even larger on mobile for better touch accessibility */
  }
  aside .button.text-size-12 {
    font-size: 14px !important;
    padding: 10px 14px !important;
    color: white !important; /* Maintain white text on mobile */
  }
}
@media (max-width: 480px) {
  aside .text-size-12 {
    font-size: 16px !important; /* Largest on very small screens */
  }
}/*# sourceMappingURL=custom-style.css.map */