/**
 * Simple Test Script
 * 
 * This is a simplified script for testing AJAX functionality.
 */
(function($) {
    'use strict';
    
    // Initialize when document is ready
    $(document).ready(function() {
        console.log('Simple test script loaded');
        initSimpleTest();
    });
    
    /**
     * Initialize simple test
     */
    function initSimpleTest() {
        // Check if the test form exists
        var $form = $('#simple-test-form');
        if (!$form.length) {
            console.log('Simple test form not found');
            return;
        }
        
        console.log('Simple test form found, initializing');
        
        // Handle form submission
        $form.on('submit', function(e) {
            e.preventDefault();
            
            console.log('Simple test form submitted');
            
            // Show loading message
            $('#simple-test-result').html('<p>Sending test...</p>');
            
            // Get form data
            var formData = {
                action: 'simple_test_action',
                nonce: simple_test_vars.nonce,
                name: $('#simple-name').val(),
                message: $('#simple-message').val()
            };
            
            console.log('Sending data:', formData);
            console.log('AJAX URL:', simple_test_vars.ajax_url);
            
            // Send AJAX request
            $.ajax({
                url: simple_test_vars.ajax_url,
                type: 'POST',
                data: formData,
                success: function(response) {
                    console.log('AJAX success:', response);
                    
                    if (response.success) {
                        $('#simple-test-result').html('<p class="success">Success! Server response: ' + response.data.message + '</p>');
                    } else {
                        $('#simple-test-result').html('<p class="error">Error: ' + response.data.message + '</p>');
                    }
                },
                error: function(xhr, status, error) {
                    console.log('AJAX error:', status, error);
                    console.log('Response:', xhr.responseText);
                    
                    $('#simple-test-result').html('<p class="error">AJAX Error: ' + status + ' - ' + error + '</p><pre>' + xhr.responseText + '</pre>');
                }
            });
        });
    }
})(jQuery);