<?php
// Get governance management types to display in sidebar
$sidebar_management_types = get_posts(array(
    'post_type' => 'management_type',
    'posts_per_page' => -1,
    'orderby' => 'meta_value_num',
    'meta_key' => 'priority',
    'order' => 'ASC',
    'meta_query' => array(
        array(
            'key' => 'governance',
            'value' => '1',
            'compare' => '='
        )
    )
));

// Define CSS classes for different categories (will cycle through these)
$css_classes = array(
    'facebook',
    'twitter',
    'google',
    'linkedin',
    'instagram',
    'pinterest'
);

// Get the current page URL
$current_page_url = get_permalink();

// Check if we're on the governance page
$is_governance_page = is_page_template('page-governance.php') || is_page('governance');

// Get the currently requested type
$requested_type = isset($_GET['type']) ? sanitize_text_field($_GET['type']) : '';
?>
<div class="s-12 m-4 l-2">
  <aside>
    
    <div class="line">
      <h5 class="text-center text-uppercase margin-top-30 margin-bottom-20 text-strong">Governance</h5>
      <div class="margin">
        <?php 
        // Loop through each governance management type
        $class_index = 0;
        foreach ($sidebar_management_types as $type) :
            $type_name = get_field('management_type_name', $type->ID);
            $css_class = $css_classes[$class_index % count($css_classes)];
            $class_index++;
            
            // Get the governance page URL
            $governance_page_url = $current_page_url;
            
            // Create URL with type parameter
            $type_slug = sanitize_title($type_name);
            $type_url = add_query_arg('type', $type_slug, $governance_page_url);
            
            // Check if this is the currently selected type
            $is_active = ($requested_type === $type_slug);
        ?>
        <div class="s-12 m-12 l-12">
          <a href="<?php echo esc_url($type_url); ?>" class="rounded-div category-padding <?php echo esc_attr($css_class); ?> text-social margin-bottom <?php echo $is_active ? 'active-link' : ''; ?>">
            <?php echo esc_html($type_name); ?>
          </a>
        </div>
        <?php endforeach; ?>
      </div>
    </div>

  </aside>
</div> <!-- sidebar ends -->