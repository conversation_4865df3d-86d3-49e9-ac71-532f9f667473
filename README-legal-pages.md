# Legal Pages Setup Guide

This guide explains how to set up legal pages (Privacy Policy, Terms of Use, etc.) using the custom template.

## Using the Legal Page Template

1. **Create Legal Pages**:
   - Go to WordPress Admin > Pages > Add New
   - Create separate pages for each legal document with the following recommended slugs:
     - `privacy-policy` (for Privacy Policy)
     - `terms-of-use` (for Terms of Use)
     - `cookies` (for Cookies Policy)
     - `disclaimer` (for Disclaimer)

2. **Apply the Template**:
   - In the Page Editor, look for the "Page Attributes" section in the right sidebar
   - From the "Template" dropdown, select "Legal Page with Sidebar"
   - Update/Publish the page

3. **Add Content**:
   - Add your legal content to each page
   - Sample content is available in the theme's root directory:
     - `sample-legal-content.html` (Privacy Policy example)
     - `sample-terms-content.html` (Terms of Use example)

## Template Features

- **Responsive Layout**: Works on all device sizes
- **Navigation Sidebar**: Automatically shows links to all legal pages
- **Active Link Highlighting**: The current page is highlighted in the sidebar
- **Contact Section**: Includes a link to the Contact Us page

## Troubleshooting

If the template doesn't appear in the dropdown:
1. Go to Appearance > Theme Editor
2. Check if `legal-page-template.php` is present in the theme files
3. If not, contact the theme developer

## Footer Links

The footer's Legal section links are automatically set to point to these pages if they exist. If you use different slugs, you'll need to update the links in the footer.php file.