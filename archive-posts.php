<?php
/**
 * The template for displaying archived posts
 *
 * @package Nkhwazi_Primary_School
 */

get_header();
?>

<!-- MAIN -->
<main role="main">
  <!-- Content -->
  <article>
    <div class="line">
      <header class="rounded-div section background-blue background-transparent text-center"
        data-image-src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/parallax-02.webp">
        <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">Archived Posts</h1>
      </header>
    </div>

    <div class="section background-white">
      <div class="line">
        <!-- put page content below, do not edit above this comment -->
        <div class="margin">

          <div class="grid margin">
            <?php
            // Create a custom query for archived blog posts
            $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
            $args = array(
                'post_type' => 'post',
                'posts_per_page' => 10,
                'paged' => $paged,
                'meta_query' => array(
                    array(
                        'key' => 'archived',
                        'value' => '1',
                        'compare' => '='
                    )
                )
            );
            
            // If we have a category parameter, filter by that category
            if (isset($_GET['category']) && !empty($_GET['category'])) {
                // Get the category ID from the slug
                $category = get_category_by_slug($_GET['category']);
                if ($category) {
                    $args['cat'] = $category->term_id;
                }
            }
            
            $blog_query = new WP_Query($args);
            
            // For debugging - only visible to admins
            if (is_user_logged_in() && current_user_can('administrator')) {
                echo '<!-- Query Debug: Found posts: ' . esc_html($blog_query->found_posts) . ' -->';
                echo '<!-- Query Debug: Current page: ' . $paged . ' -->';
                echo '<!-- Query Debug: Max pages: ' . $blog_query->max_num_pages . ' -->';
                echo '<!-- Query Debug: Post type: post -->';
                echo '<!-- Query Debug: Category param: ' . (isset($_GET['category']) ? $_GET['category'] : 'none') . ' -->';
                echo '<!-- Query Debug: Args: ' . esc_html(json_encode($args)) . ' -->';
                
                // Show the SQL query for debugging
                echo '<!-- SQL Query: ' . esc_html($blog_query->request) . ' -->';
            }
            
            if ($blog_query->have_posts()) :
                while ($blog_query->have_posts()) : $blog_query->the_post();
                    // Get post categories for filtering
                    $post_categories = get_the_category();
                    $category_slugs = array();
                    $category_names = array();
                    
                    foreach ($post_categories as $cat) {
                        $category_slugs[] = $cat->slug;
                        $category_names[$cat->slug] = $cat->name;
                    }
                    
                    // Get the first category for display
                    $category_name = !empty($post_categories) ? $post_categories[0]->name : '';
                    $category_link = !empty($post_categories) ? get_category_link($post_categories[0]->term_id) : '';
            ?>
            <article class="s-12 m-6 l-4 margin-bottom-20 blog-post" data-post-id="<?php echo get_the_ID(); ?>">
              <?php 
              $blog_image = get_field('blog_image');
              if ($blog_image) : 
                $blog_image_url = wp_get_attachment_image_url($blog_image['ID'], 'blog-thumbnail'); // Use the blog-thumbnail size
                if (!$blog_image_url) {
                  $blog_image_url = $blog_image['url']; // Fallback to original if size doesn't exist
                }
              ?>
              <img class="rounded-image-top margin-bottom blog-image" src="<?php echo esc_url($blog_image_url); ?>"
                alt="<?php echo esc_attr($blog_image['alt'] ? $blog_image['alt'] : get_the_title()); ?>">
              <?php endif; ?>

              <h3 class="text-strong text-uppercase"><a class="text-dark text-blue-hover"
                  href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
              <div class="grid blog-metadata">
                <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12"><?php echo get_the_date(); ?></div>
                <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12 blog-author">
                  <a href="<?php echo esc_url(get_author_posts_url(get_the_author_meta('ID'))); ?>"
                    class="blog-author">By <?php the_author(); ?></a>
                </div>
                <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12 blog-category">
                  <?php if (!empty($category_name)) : ?>
                  <a href="<?php echo esc_url($category_link); ?>"
                    class="blog-category"><?php echo esc_html($category_name); ?></a>
                  <?php endif; ?>
                </div>
              </div>
              <p><?php echo wp_trim_words(get_the_excerpt(), 20); ?></p>
              <div class="text-center">
                <a class="text-more-info text-yellow-hover margin-bottom-30"
                  href="<?php the_permalink(); ?>">Read More</a>
              </div>

            </article>
            <?php
                endwhile;
            else:
            ?>
            <div class="s-12 margin-bottom-20 no-posts-message">
              <p>No archived posts found.</p>
              <p><a href="<?php echo esc_url(get_permalink(get_option('page_for_posts'))); ?>"
                  class="text-primary-hover">View all posts</a></p>
            </div>
            <?php 
            endif;
            // Reset post data
            wp_reset_postdata();
            ?>
          </div><!-- Grid -->

          <?php if ($blog_query->max_num_pages > 1): ?>
          <!-- Pagination -->
          <div class="line text-center pagination-container">
            <div class="s-12 margin-bottom">
              <nav class="pagination-nav">
                <?php
                // Get the current URL
                global $wp;
                $current_url = home_url($wp->request);
                
                // Create pagination links that preserve the category parameter
                $pagination_args = array(
                    'base' => str_replace(999999999, '%#%', esc_url(get_pagenum_link(999999999))),
                    'format' => '?paged=%#%',
                    'current' => max(1, $paged),
                    'total' => $blog_query->max_num_pages,
                    'prev_text' => '&laquo; ' . __('Previous', 'nkhwazischool'),
                    'next_text' => __('Next', 'nkhwazischool') . ' &raquo;',
                    'mid_size' => 2,
                    'end_size' => 1,
                    'type' => 'list'
                );
                
                // Add the category parameter to the pagination links if it exists
                if (isset($_GET['category']) && !empty($_GET['category'])) {
                    $pagination_args['add_args'] = array(
                        'category' => sanitize_text_field($_GET['category'])
                    );
                }
                
                echo paginate_links($pagination_args);
                
                // Reset post data
                wp_reset_postdata();
                ?>
              </nav>
            </div>
          </div>
          <?php endif; ?>
          
          <div class="s-12 margin-top-30 text-center">
            <a href="<?php echo esc_url(get_permalink(get_option('page_for_posts'))); ?>" class="button background-primary text-white">Back to Current Posts</a>
          </div>
        </div>

        <!-- put page content above, do not edit below this comment -->

      </div>
    </div>
  </article>
</main>

<?php get_footer(); ?>