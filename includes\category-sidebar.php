 <!-- Category Sidebar -->
 <div class="s-12 m-4 l-3">
   <aside>
     <h3 class="text-uppercase margin-top-20 margin-bottom-30 text-strong text-center">Other Categories</h3>
     
     <?php
     // Get current category from URL parameter or from the URL path
     $current_category = isset($_GET['category']) ? $_GET['category'] : '';
     
     // If no category is found in the query parameter, try to get it from the URL path
     if (empty($current_category)) {
         $uri = $_SERVER['REQUEST_URI'];
         if (preg_match('/^\/school-categories\/([^\/]+)\/?$/', $uri, $matches)) {
             $current_category = $matches[1];
         }
     }
     
     // Get all school categories from custom post type
     $args = array(
         'post_type' => 'school_categories',
         'posts_per_page' => -1,
         'orderby' => 'menu_order',
         'order' => 'ASC',
     );
     $categories_query = new WP_Query($args);
     ?>
     
     <div class="margin">
       <?php
       // If we have school categories from the custom post type
       if ($categories_query->have_posts()) :
           while ($categories_query->have_posts()) : $categories_query->the_post();
               // Get the slug to compare with current category
               $slug = sanitize_title(get_the_title());
               
               // Only show if this is not the current category
               if ($slug != $current_category) :
                   // Get ACF fields
                   $card_img = get_field('card_img');
                   $image_url = $card_img ? wp_get_attachment_image_url($card_img['ID'], 'thumbnail') : get_template_directory_uri() . '/assets/img/img-08.jpg';
       ?>
       <div class="s-12 margin-bottom-20">
         <a href="<?php echo esc_url(get_permalink()); ?>" class="category-sidebar-link">
           <div class="category-sidebar-item">
             <img src="<?php echo esc_url($image_url); ?>" alt="<?php echo esc_attr(get_the_title()); ?>" class="category-sidebar-image">
             <div class="category-sidebar-title"><?php echo esc_html(get_the_title()); ?></div>
           </div>
         </a>
       </div>
       <?php 
               endif;
           endwhile;
           wp_reset_postdata();
       else :
           // Fallback to hardcoded categories if no custom post types found
       ?>
           <!-- Lower School Category -->
           <?php if($current_category != 'lower'): ?>
           <div class="s-12 margin-bottom-20">
             <a href="<?php echo esc_url(home_url('/school-categories/lower/')); ?>" class="category-sidebar-link">
               <div class="category-sidebar-item">
                 <img src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/img-08.jpg" alt="Lower School" class="category-sidebar-image">
                 <div class="category-sidebar-title">Lower School</div>
               </div>
             </a>
           </div>
           <?php endif; ?>
           
           <!-- Middle School Category -->
           <?php if($current_category != 'middle'): ?>
           <div class="s-12 margin-bottom-20">
             <a href="<?php echo esc_url(home_url('/school-categories/middle/')); ?>" class="category-sidebar-link">
               <div class="category-sidebar-item">
                 <img src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/img-09.jpg" alt="Middle School" class="category-sidebar-image">
                 <div class="category-sidebar-title">Middle School</div>
               </div>
             </a>
           </div>
           <?php endif; ?>
           
           <!-- Upper School Category -->
           <?php if($current_category != 'upper'): ?>
           <div class="s-12 margin-bottom-20">
             <a href="<?php echo esc_url(home_url('/school-categories/upper/')); ?>" class="category-sidebar-link">
               <div class="category-sidebar-item">
                 <img src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/img-09.jpg" alt="Upper School" class="category-sidebar-image">
                 <div class="category-sidebar-title">Upper School</div>
               </div>
             </a>
           </div>
           <?php endif; ?>
       <?php endif; ?>
     </div>
     
     <h5 class="text-uppercase margin-top-30 margin-bottom-20 text-strong">Quick Links</h5>
     <div class="margin">
       <div class="s-12 margin-bottom-10">
         <a href="<?php echo esc_url(get_permalink(328)); ?>" class="text-more-info">All School Categories</a>
       </div>
       <div class="s-12 margin-bottom-20">
         <a href="<?php echo esc_url(get_permalink(376)); ?>" class="text-more-info">View Teaching Staff</a>
       </div>
     </div>
   </aside>
 </div>