/**
 * SCSS Custom Styles for Nkhwazi Primary School Website
 * custom-style.scss Compiles to custom-style.css
 * Author: <PERSON>
 * Version 1.00
 * Year: 2025
 */
/*
//Ubuntu styles
.ubuntu-light {
  font-family: "Ubuntu", sans-serif;
  font-weight: 300;
  font-style: normal;
}

.ubuntu-regular {
  font-family: "Ubuntu", sans-serif;
  font-weight: 400;
  font-style: normal;
}

.ubuntu-medium {
  font-family: "Ubuntu", sans-serif;
  font-weight: 500;
  font-style: normal;
}

.ubuntu-bold {
  font-family: "Ubuntu", sans-serif;
  font-weight: 700;
  font-style: normal;
}

.ubuntu-light-italic {
  font-family: "Ubuntu", sans-serif;
  font-weight: 300;
  font-style: italic;
}

.ubuntu-regular-italic {
  font-family: "Ubuntu", sans-serif;
  font-weight: 400;
  font-style: italic;
}

.ubuntu-medium-italic {
  font-family: "Ubuntu", sans-serif;
  font-weight: 500;
  font-style: italic;
}

.ubuntu-bold-italic {
  font-family: "Ubuntu", sans-serif;
  font-weight: 700;
  font-style: italic;
}

*/

/* Noto serif styles */
/**
 *
.noto-serif-georgian-<uniquifier> {
  font-family: "Noto Serif Georgian", serif;
  font-optical-sizing: auto;
  font-weight: <weight>;
  font-style: normal;
  font-variation-settings: "wdth" 100;
}
 */

/**
  * Primary Color
  *HEX Code: #3649e2
  *RGB Code: rgb(54,73,226)
  *complementary color
  *Secondary Color: #E2CF36;
  *Secondary color RGB Code: (rgb(226, 207, 54))
 */

$primary-color: rgb(54, 73, 226);
$secondary-color: rgb(226,207,54);
$secondary-color-darker: rgb(171, 153, 34);
$body-text: #222;
$link-color: #0000ff;
$visited-link-color: #800080;
$hover-link-color: #ff0000;
$active-link-color: #ff00ff;
$white-color: #ffffff;
$grey-color: #cccccc;
$light-grey-color: #eeeeee;
$button-hover: #e2cf36;
$black-color: #000000;

.text-yellow-darker {
  color: $secondary-color-darker !important;
}

.text-primary {
  color: $primary-color !important;
}

/*General Styles*/
body,
p {
  font-family: "ubuntu", sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 1rem;
  text-align: left;
}
/*** Headings */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: $secondary-color;
  font-family: "Noto Serif Georgian", serif;
  font-optical-sizing: auto;
  font-weight: 600;
  font-style: normal;
  line-height: 1.3;
  font-variation-settings: "wdth" 100;
  margin: 0.5rem 0;
  text-align: left;
}
h1 {
  font-size: 2.2rem;
  text-align: center;
}
h2 {
  font-size: 1.6rem;
}
h3 {
  font-size: 1.3rem;
}
h4 {
  font-size: 1.1rem;
}

/*** Lists */
/**targeting ONLY content within the main part of the page */
main li {
  font-size: 1rem;
  list-style-type: disc;
}
main ul,
main ol {
  margin-top: 1rem;
  margin-bottom: 1rem;
  margin-left: 1rem;
}

/*
 * Custom styles for Nkhwazi Primary School website
 */
/* Form progress steps */
.form-progress {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
  position: relative;

  /* Remove any secondary color line */
  &::after {
    display: none !important;
  }
}

/* Additional rule to target any potential secondary color line */
.form-progress::after {
  display: none !important;
}

.form-progress-step {
  position: relative;
  z-index: 2;
  text-align: center;
  transition: all 0.3s ease;

  &.clickable {
    cursor: pointer;

    &:hover .form-progress-step-number {
      background-color: #3649e2;
      color: white;
      transform: scale(1.1);
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }

    &:hover .form-progress-step-label {
      color: #3649e2;
      font-weight: bold;
    }
  }

  &.active .form-progress-step-number {
    background-color: #3649e2;
    color: white;
    transform: scale(1.1);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  }

  &.active .form-progress-step-label {
    color: #3649e2;
    font-weight: bold;
  }

  &.completed .form-progress-step-number {
    background-color: #2ecc71;
    color: white;
  }
}

/* The form-progress-step-number styles are defined in application-form.css */

.form-progress-step-label {
  font-size: 14px;
  color: #666;
}

/* Form steps */
.form-step {
  display: none !important;
  opacity: 0;
  transition: opacity 0.3s ease;

  &.active {
    display: block !important;
    opacity: 1;
  }
}

/**Header styles */
.website-name {
  color: $primary-color !important;
}

.rounded-div {
  border-radius: 10px;
  overflow: hidden;
  /* Add word breaking properties to prevent text overflow */
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
}

/* Management sidebar specific styles */
.management-sidebar a {
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
  display: block;
  width: 100%;
}

/* Logo and school name container */
.nav-text {
  background: none repeat scroll 0 0 $primary-color;
}
.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

.logo-container .logo {
  margin: 2px;
}

.logo-container h1 {
  margin: 0;
  color: $primary-color;
  font-size: 2.2rem;
  text-align: left;

  font-weight: 600;
}

/* Centered menu styles */
.centered-menu {
  display: flex;
  justify-content: center; /* Changed from left to center */
  flex-wrap: wrap;
  float: none !important;
}

.centered-menu > li {
  float: none !important;
  display: inline-block;
}

/* WordPress Menu Styles */
.top-nav ul.menu {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  float: none !important;
  margin: 0;
  padding: 0;
}

.top-nav ul.menu > li {
  float: none !important;
  display: inline-block;
  position: relative;
}

.top-nav ul.menu > li > a {
  display: block;
  padding: 0.7rem 1.25rem;
  text-decoration: none;
  color: #444;
  font-size: 1rem;
  transition: color 0.20s linear 0s;
}

.top-nav ul.menu > li > a:hover {
  color: #3649e2;
}

.top-nav ul.menu > li.menu-item-has-children > a:after,
.top-nav ul.menu > li.submenu > a:after {
  content: "\25BC";
  font-size: 0.6rem;
  margin-left: 0.5rem;
  position: relative;
  top: -0.2rem;
}

.top-nav ul.menu > li > ul.sub-menu {
  background: white none repeat scroll 0 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: absolute;
  display: none;
  top: 100%;
  width: 13rem;
  z-index: 10;
  margin: 0;
  padding: 0;
}

.top-nav ul.menu > li:hover > ul.sub-menu,
.top-nav ul.menu > li > ul.sub-menu.show-ul {
  display: block;
}

.top-nav ul.menu > li > ul.sub-menu > li {
  float: left;
  list-style: none;
  width: 100%;
}

.top-nav ul.menu > li > ul.sub-menu > li > a {
  color: #444;
  display: block;
  font-size: 0.9rem;
  padding: 0.6rem 1.25rem;
  text-decoration: none;
  transition: all 0.15s linear 0s;
}

.top-nav ul.menu > li > ul.sub-menu > li > a:hover {
  background: #3649e2 none repeat scroll 0 0;
  color: white;
}

/* Second level dropdown menu styles */
.top-nav ul.menu > li > ul.sub-menu > li.menu-item-has-children > a:after {
  content: "\25B6"; /* Right-pointing triangle */
  font-size: 0.6rem;
  margin-left: 0.5rem;
  position: relative;
  top: -0.1rem;
}

.top-nav ul.menu > li > ul.sub-menu > li > ul.sub-menu {
  background: white none repeat scroll 0 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: absolute;
  display: none;
  left: 100%; /* Position to the right of the first level dropdown */
  top: 0;
  width: 13rem;
  z-index: 11; /* Higher than first dropdown */
  margin: 0;
  padding: 0;
}

.top-nav ul.menu > li > ul.sub-menu > li:hover > ul.sub-menu,
.top-nav ul.menu > li > ul.sub-menu > li > ul.sub-menu.show-ul {
  display: block;
}

.top-nav ul.menu > li > ul.sub-menu > li > ul.sub-menu > li {
  float: left;
  list-style: none;
  width: 100%;
}

.top-nav ul.menu > li > ul.sub-menu > li > ul.sub-menu > li > a {
  color: #444;
  display: block;
  font-size: 0.9rem;
  padding: 0.6rem 1.25rem;
  text-decoration: none;
  transition: all 0.15s linear 0s;
}

.top-nav ul.menu > li > ul.sub-menu > li > ul.sub-menu > li > a:hover {
  background: #3649e2 none repeat scroll 0 0;
  color: white;
}

/* Footer Menu Styles */
.footer-links .menu-item {
  margin-bottom: 10px;
}

.footer-links .menu-item a {
  color: #fff;
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-links .menu-item a:hover {
  color: #E2CF36;
  text-decoration: underline;
}

.social-bar-footer {
  border-bottom: 2px solid rgba(54, 73, 226, 0.7);
}

/** Event styles */
.event-date {
  margin-top: 10px;
  background-color: $secondary-color;
  color: $white-color;
  padding: 6px 10px;
}

.event-card {
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  padding: 20px;
  transition: all 0.3s ease;
  height: 100%;
  border: 1px solid rgba(0, 0, 0, 0.08);

  &:hover {
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
    transform: translateY(-5px);
  }
}

.category-padding {
  padding: 15px 10px !important;
}

/* School Category Range Alignment Styles */
.category-ranges {
  margin: 10px 0;
  background-color: $light-grey-color;
  border-radius: 8px;
  border-left: 4px solid $primary-color;
  padding: 12px 15px;
  
  .grade-range,
  .age-range {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    line-height: 1.4;
    
    .range-label {
      display: inline-block;
      width: 100px; /* Fixed width for consistent alignment */
      font-weight: 500;
      color: $secondary-color-darker;
      flex-shrink: 0; /* Prevent shrinking */
      font-size: 0.95rem;
    }
    
    .range-value {
      font-weight: 600;
      color: $body-text;
      margin-left: 5px;
      font-size: 0.95rem;
    }
  }
  
  /* Remove bottom margin from last range item */
  .age-range {
    margin-bottom: 0;
  }
  
  /* Responsive adjustments for smaller screens */
  @media (max-width: 768px) {
    .grade-range,
    .age-range {
      .range-label {
        width: 90px; /* Slightly smaller on mobile */
        font-size: 0.9rem;
      }
      
      .range-value {
        font-size: 0.9rem;
      }
    }
  }
  
  @media (max-width: 480px) {
    .grade-range,
    .age-range {
      .range-label {
        width: 80px; /* Even smaller on very small screens */
      }
    }
  }
}

/* Blog Card Metadata Styling */
.blog-metadata {
  background-color: $light-grey-color;
  border-radius: 8px;
  padding: 10px 12px 5px 10px !important;
  margin: 10px 0 15px 0;
  border-left: 3px solid $secondary-color-darker;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  .s-4 {
    font-size: 0.85rem;
    line-height: 1.3;
    color: $body-text;
    
    a {
      color: $body-text;
      text-decoration: none;
      transition: color 0.3s ease;
      font-weight: 500;
      
      &:hover {
        color: $primary-color;
        text-decoration: underline;
      }
    }
  }
}

/* Active link in sidebar */
.active-link {
  border: 2px solid $secondary-color !important;
  background-color: white !important;
  color: $black-color !important; /* Make text black */
  font-weight: bold !important;
  position: relative;
  padding: 10px 30px 10px 15px !important; /* Increased padding all around, with extra on the right for the tick */
  line-height: 1.8 !important; /* Further increased line height for better readability */
  margin: 0.2rem 0 !important; /* Add margin for better spacing between items */
  
  &:after {
    content: "✓";
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: $secondary-color;
  }
}

/* WordPress Gutenberg Block Editor Styles */
/* 
 * Column Card Styles - Consistent with site-wide card design
 * 
 * Usage Instructions:
 * 1. Add these classes to individual columns in the Gutenberg editor
 * 2. In the block editor, select a column and add the class in "Advanced > Additional CSS class(es)"
 * 
 * Available Classes:
 * - column-card: Basic white card with shadow
 * - column-card-primary: White card with blue left border accent
 * - column-card-light: Light grey background card
 * - column-card-highlight: Light blue background card
 * 
 * Modifiers (can be combined):
 * - Add "-compact" for less padding (e.g., column-card-compact)
 * - Add "-spacious" for more padding (e.g., column-card-spacious)
 * - Add "-center" for centered text (e.g., column-card-center)
 * - Add "-static" to disable hover effects (e.g., column-card-static)
 * - Add "-icon-top" for icon/image at top layout (e.g., column-card-icon-top)
 * 
 * For equal height cards, add "equal-height-cards" class to the Columns block
 */

/* Basic column card - matches event-card styling */
.wp-block-columns .wp-block-column.column-card,
.wp-block-column.column-card {
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  padding: 20px;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  
  &:hover {
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
    transform: translateY(-5px);
  }
}

/* Column card with primary accent - matches extracurricular-summary styling */
.wp-block-columns .wp-block-column.column-card-primary,
.wp-block-column.column-card-primary {
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  padding: 20px;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-left: 4px solid $primary-color;
  margin-bottom: 20px;
  
  &:hover {
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
    transform: translateY(-5px);
  }
}

/* Light background column card - matches blog-metadata styling */
.wp-block-columns .wp-block-column.column-card-light,
.wp-block-column.column-card-light {
  background-color: $light-grey-color;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 20px;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  
  &:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-3px);
  }
}

/* Primary highlight column card - light blue background */
.wp-block-columns .wp-block-column.column-card-highlight,
.wp-block-column.column-card-highlight {
  background-color: rgba(54, 73, 226, 0.1); /* Light blue background using primary color with transparency */
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 20px;
  transition: all 0.3s ease;
  border: 1px solid rgba(54, 73, 226, 0.2);
  margin-bottom: 20px;
  
  &:hover {
    background-color: rgba(54, 73, 226, 0.15); /* Slightly darker on hover */
    box-shadow: 0 6px 15px rgba(54, 73, 226, 0.15);
    transform: translateY(-3px);
  }
}

/* Padding modifiers */
.wp-block-column.column-card-compact,
.wp-block-column.column-card-primary-compact,
.wp-block-column.column-card-light-compact,
.wp-block-column.column-card-highlight-compact {
  padding: 15px !important;
}

.wp-block-column.column-card-spacious,
.wp-block-column.column-card-primary-spacious,
.wp-block-column.column-card-light-spacious,
.wp-block-column.column-card-highlight-spacious {
  padding: 30px !important;
}

/* Remove bottom margin for last column cards in a row */
.wp-block-columns .wp-block-column:last-child.column-card,
.wp-block-columns .wp-block-column:last-child.column-card-primary,
.wp-block-columns .wp-block-column:last-child.column-card-light,
.wp-block-columns .wp-block-column:last-child.column-card-highlight {
  margin-bottom: 0;
}

/* Responsive adjustments for column cards */
@media (max-width: 768px) {
  .wp-block-column.column-card,
  .wp-block-column.column-card-primary,
  .wp-block-column.column-card-light,
  .wp-block-column.column-card-highlight {
    margin-bottom: 15px;
    padding: 15px;
    
    &.column-card-compact {
      padding: 12px !important;
    }
    
    &.column-card-spacious {
      padding: 20px !important;
    }
  }
}

/* Content styling within column cards */
.wp-block-column.column-card,
.wp-block-column.column-card-primary,
.wp-block-column.column-card-light,
.wp-block-column.column-card-highlight {
  
  /* Style headings within column cards */
  h1, h2, h3, h4, h5, h6 {
    margin-top: 0;
    margin-bottom: 15px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  /* Style paragraphs within column cards */
  p {
    margin-bottom: 15px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  /* Style lists within column cards */
  ul, ol {
    margin-bottom: 15px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  /* Style images within column cards */
  img {
    border-radius: 6px;
    max-width: 100%;
    height: auto;
  }
  
  /* Style buttons within column cards */
  .wp-block-button {
    margin-bottom: 10px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

/* Additional utility classes for column cards */
/* Center content within column cards */
.wp-block-column.column-card-center,
.wp-block-column.column-card-primary-center,
.wp-block-column.column-card-light-center,
.wp-block-column.column-card-highlight-center {
  text-align: center;
}

/* Equal height column cards (when used in a columns block) */
/* Using very specific selectors to override Gutenberg's CSS */
.wp-block-columns.equal-height-cards.is-layout-flex.wp-block-columns-is-layout-flex,
.wp-block-columns.equal-height-cards.is-layout-flex,
.wp-block-columns.equal-height-cards {
  align-items: stretch !important;
  min-height: 100% !important;
}

.wp-block-columns.equal-height-cards .wp-block-column.is-layout-flow.wp-block-column-is-layout-flow,
.wp-block-columns.equal-height-cards .wp-block-column.is-layout-flow,
.wp-block-columns.equal-height-cards .wp-block-column {
  
  &.column-card,
  &.column-card-primary,
  &.column-card-light,
  &.column-card-highlight {
    display: flex !important;
    flex-direction: column !important;
    align-self: stretch !important;
    height: auto !important;
    min-height: 100% !important;
    flex: 1 1 auto !important;
    
    /* Ensure all content within the card is properly spaced */
    > * {
      margin-bottom: 15px;
      flex-shrink: 0;
    }
    
    /* Remove margin from last element and make it grow */
    > *:last-child {
      margin-bottom: 0 !important;
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
    }
  }
}

/* Alternative approach using CSS Grid for equal heights */
.wp-block-columns.equal-height-cards-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
  gap: 20px !important;
  align-items: stretch !important;
  
  .wp-block-column {
    &.column-card,
    &.column-card-primary,
    &.column-card-light,
    &.column-card-highlight {
      display: flex !important;
      flex-direction: column !important;
      height: 100% !important;
      align-items: stretch !important;
      
      /* Reset all margins and ensure consistent spacing */
      > * {
        margin-top: 0 !important;
        margin-bottom: 15px !important;
        flex-shrink: 0;
      }
      
      /* First element should have no top margin */
      > *:first-child {
        margin-top: 0 !important;
      }
      
      /* Last element grows to fill space and has no bottom margin */
      > *:last-child {
        margin-bottom: 0 !important;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
      }
      
      /* Ensure headings have consistent spacing */
      h1, h2, h3, h4, h5, h6 {
        margin-top: 0 !important;
        margin-bottom: 15px !important;
        line-height: 1.3 !important;
      }
      
      /* Ensure paragraphs have consistent spacing */
      p {
        margin-top: 0 !important;
        margin-bottom: 15px !important;
        line-height: 1.5 !important;
      }
      
      /* Reset any WordPress block margins that might cause differences */
      .wp-block-heading {
        margin-top: 0 !important;
        margin-bottom: 15px !important;
      }
    }
  }
}

/* Improved flexbox approach with better height matching */
.wp-block-columns.equal-height-cards-flex {
  display: flex !important;
  align-items: stretch !important;
  gap: 20px !important;
  
  .wp-block-column {
    flex: 1 !important;
    
    &.column-card,
    &.column-card-primary,
    &.column-card-light,
    &.column-card-highlight {
      display: flex !important;
      flex-direction: column !important;
      height: 100% !important;
      
      /* Reset all spacing to ensure consistency */
      > * {
        margin-top: 0 !important;
        margin-bottom: 15px !important;
      }
      
      > *:first-child {
        margin-top: 0 !important;
      }
      
      > *:last-child {
        margin-bottom: 0 !important;
        flex-grow: 1;
      }
      
      /* Consistent heading and paragraph spacing */
      h1, h2, h3, h4, h5, h6 {
        margin-top: 0 !important;
        margin-bottom: 15px !important;
        line-height: 1.3 !important;
      }
      
      p {
        margin-top: 0 !important;
        margin-bottom: 15px !important;
        line-height: 1.5 !important;
      }
      
      .wp-block-heading {
        margin-top: 0 !important;
        margin-bottom: 15px !important;
      }
    }
  }
}

/* No hover effects variant */
.wp-block-column.column-card-static,
.wp-block-column.column-card-primary-static,
.wp-block-column.column-card-light-static,
.wp-block-column.column-card-highlight-static {
  &:hover {
    transform: none !important;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05) !important;
  }
}

/* Column card with icon/image at top */
.wp-block-column.column-card-icon-top,
.wp-block-column.column-card-primary-icon-top,
.wp-block-column.column-card-light-icon-top,
.wp-block-column.column-card-highlight-icon-top {
  text-align: center;
  
  .wp-block-image:first-child,
  img:first-child {
    margin-bottom: 15px;
    max-width: 80px;
    height: auto;
    border-radius: 50%;
  }
}

/* Responsee Framework Integration */
/* Ensure column cards work well with Responsee grid system */
.wp-block-columns {
  .wp-block-column {
    &.column-card,
    &.column-card-primary,
    &.column-card-light,
    &.column-card-highlight {
      box-sizing: border-box;
      width: 100%;
      
      @media (min-width: 769px) {
        margin-left: 0;
        margin-right: 0;
      }
    }
  }
}

/* Integration with existing Responsee classes */
.wp-block-column {
  &.column-card.background-white,
  &.column-card-primary.background-white,
  &.column-card-light.background-white,
  &.column-card-highlight.background-white {
    background-color: #ffffff !important;
  }
  
  &.column-card.background-grey-light,
  &.column-card-primary.background-grey-light,
  &.column-card-light.background-grey-light,
  &.column-card-highlight.background-grey-light {
    background-color: $light-grey-color !important;
  }
}

/* Reusable image styles */
.rounded-image-top {
  border-radius: 10px 10px 0 0;
  overflow: hidden;
}
.rounded-image {
  border-radius: 10px;
  overflow: hidden;

  img {
    border-radius: 10px;
    overflow: hidden;
  }
}

.rounded-image-full {
  border-radius: 10px;
  overflow: hidden;

  img {
    border-radius: 10px;
    overflow: hidden;
    width: 100%;
    height: auto;
    object-fit: cover;
    aspect-ratio: 16/9;
  }
}

/* Gallery specific styles */
.image-with-hover-overlay.rounded-image-full {
  margin-bottom: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  }
}

.full-img {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.full-width-img {
  width: 100%;
  height: auto;
  object-fit: cover;
  display: block;
  margin: 0 auto;
  max-width: 1720px;
}

/* Extracurricular page styles */
.extracurricular-summary {
  background-color: #f8f8f8;
  border-left: 4px solid $primary-color;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 10px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-left: 4px solid $primary-color; /* Maintain the left border */
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
  }

  p {
    margin-bottom: 10px;
    font-size: 1.1rem;
    line-height: 1.5;

    &:last-child {
      margin-bottom: 0;
    }

    strong {
      color: $primary-color;
      font-weight: 600;
    }
  }
}

/* Single Extracurricular Activity Styles */
.background-primary-hightlight {
  background-color: lighten($primary-color, 45%);
  padding: 15px; /* Reduced padding from 20px to 15px */
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(54, 73, 226, 0.1);
  transition: all 0.3s ease;
  
  /* Governance filter buttons */
  .teacher-filter-buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
    
    .teacher-filter-btn {
      margin: 5px;
      padding: 8px 15px;
      background-color: white;
      color: $primary-color;
      border: 1px solid $primary-color;
      transition: all 0.3s ease;
      
      &:hover {
        background-color: $primary-color;
        color: white;
        transform: translateY(-2px);
      }
      
      &.active {
        background-color: $primary-color;
        color: white;
        font-weight: bold;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
      }
    }
  }

  &:hover {
    box-shadow: 0 6px 15px rgba(54, 73, 226, 0.15);
  }

  .activity-excerpt {
    font-size: 0.95rem;
    line-height: 1.3;
    padding-bottom: 8px;
  }

  /* Style for each info row */
  .s-12.margin-bottom-10 {
    margin-bottom: 1px; /* Reduced from 15px to 8px */
    padding-bottom: 1px; /* Reduced from 12px to 8px */

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
      padding-bottom: 0;
    }
  }

  /* Reduce margin in grid */
  .grid.margin {
    margin: 0 -5px; /* Reduced margin */

    /* Reduce spacing between grid items */
    > div {
      padding: 0 5px;
    }
  }

  /* Special styling for Extracurricular Category section */
  .s-12.margin-bottom-10:nth-child(2) {
    background-color: rgba(54, 73, 226, 0.08);
    border-radius: 6px;
    padding: 6px;
    margin: 8px 0;
    border-left: 3px solid $primary-color;

    a.category-link {
      display: inline-block;
      position: relative;
      padding: 3px 20px 3px 3px;
      background-color: rgba(255, 255, 255, 0.7);
      border-radius: 3px;
      transition: all 0.3s ease;
      font-size: 0.9rem;

      i.icon-tag {
        color: $primary-color;
      }

      &:after {
        content: "→";
        position: absolute;
        right: 5px;
        top: 50%;
        transform: translateY(-50%);
        transition: all 0.2s ease;
        color: $primary-color;
        font-size: 0.8rem;
      }

      &:hover {
        background-color: rgba(255, 255, 255, 0.9);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

        &:after {
          right: 5px;
        }
      }
    }
  }
}

/* Activity Summary Table Styles */
.activity-summary-container {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(54, 73, 226, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 6px 15px rgba(54, 73, 226, 0.15);
  }
}

.activity-summary-table {
  width: 100%;
  border-collapse: collapse;
  
  /* Make table responsive */
  @media screen and (max-width: 600px) {
    display: block;
    
    tbody, tr, th, td {
      display: block;
      width: 100%;
    }
    
    th {
      text-align: left;
      background-color: lighten($primary-color, 48%);
      padding: 10px;
      border-bottom: 1px solid rgba(54, 73, 226, 0.1);
    }
    
    td {
      padding: 10px;
      border-bottom: 1px solid rgba(54, 73, 226, 0.1);
    }
    
    tr:last-child {
      td, th {
        border-bottom: none;
      }
    }
  }
  
  /* Table styles for larger screens */
  @media screen and (min-width: 601px) {
    th {
      text-align: left;
      padding: 5px 15px;
      width: 40%;
      font-weight: 600;
      color: $primary-color;
    }
    
    td {
      padding: 5px 15px;
    }
  }
  
  /* Alternating row backgrounds */
  .table-row-odd {
    background-color: lighten($primary-color, 45%);
  }
  
  .table-row-even {
    background-color: white;
  }
  
  /* Excerpt cell styling */
  .activity-excerpt-cell {
    padding: 10px 15px !important;
  }
  
  .activity-excerpt {
    font-size: 0.95rem;
    line-height: 1.4;
    color: $secondary-color-darker;
    font-weight: 500;
  }
  
  /* Category link styling */
  .category-link {
    display: inline-block;
    position: relative;
    padding: 3px 20px 3px 3px;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 3px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    
    &:after {
      content: '→';
      position: absolute;
      right: 5px;
      top: 50%;
      transform: translateY(-50%);
      transition: all 0.2s ease;
      color: $primary-color;
      font-size: 0.8rem;
    }
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.9);
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
  }
}

/* Extracurricular Item Page Image */
.extracurricular-page-image {
  width: 100%;
  height: auto;
  max-height: 500px;
  object-fit: cover;
  border-radius: 10px;
  margin-bottom: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Activity items on extracurricular pages */
.activities-list {
  /* Styles for the simplified related activities (title and date only) */
  a.activity-link {
    text-decoration: none;
    display: block;
    color: inherit;
    padding: 12px;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      text-decoration: none;
      background-color: rgba(255, 255, 255, 0.7);
      transform: translateY(-2px);
    }

    /* Add border-bottom to separate activities in the single rounded div */
    &.border-bottom {
      border-bottom: 1px solid rgba(54, 73, 226, 0.2);
      padding-bottom: 15px;
      margin-bottom: 10px;
    }

    h4 {
      margin-bottom: 5px;
      font-size: 1.1rem;
      line-height: 1.3;
      color: $primary-color;
    }

    p {
      margin: 0;
      color: #666;
    }
  }

  /* Separator line between activities */
  hr.activity-separator {
    border: 0;
    height: 1px;
    background: rgba(54, 73, 226, 0.2);
    margin: 10px 0;
  }

  /* Thumbnail image in activity cards */
  .activity-thumbnail {
    width: 100%;
    height: auto;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

/* No activities message styling */
.no-activities-message {
  .background-grey-light {
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }

  .icon-info-circled {
    color: $primary-color;
    display: inline-block;
    margin-bottom: 15px;
  }

  p {
    color: #555;
    line-height: 1.5;

    &.text-strong {
      font-size: 1.1rem;
      color: #333;
      margin-bottom: 8px;
    }
  }

  .button {
    margin-top: 10px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-3px);
    }
  }
}

/* Buttons with primary background */
.button.background-primary {
  display: inline-block;
  padding: 12px 24px;
  border-radius: 30px;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  border: 2px solid $primary-color;

  &:hover {
    background-color: transparent !important;
    color: $primary-color !important;
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
}

/* Blog author and category links */
.blog-author,
.blog-category {
  color: $link-color !important;

  &:hover {
    text-decoration: underline;
  }
}

/* About Nkhwazi Primary School section styles */
.grid.margin article.s-12.m-12.l-6.margin-m-bottom:first-child {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  text-align: left;

  h2,
  p,
  a {
    text-align: left;
  }
}

/* Apply rounded corners to the About Nkhwazi Primary School image */
.grid.margin article.s-12.m-12.l-6.margin-m-bottom:last-child img {
  @extend .rounded-image;
}

.nkhwazi-stats {
  background-color: #eaeaea;
  padding: 0.8rem;
  border-radius: 10px;
}

.number-stat {
  border: 1px solid $white-color;
  background-color: #f2f2f2;
  border-radius: 10px;
}

// Timer counter styles
.timer {
  display: inline-block;
  min-width: 1em;
  text-align: center;
  
  // Ensure the timer is visible from the start
  opacity: 1;
  visibility: visible;
}

/** Links */

/* Font colors */
.background-white,
.background-white p,
a.background-white,
.background-white a,
.background-white a:active {
  color: #444;
}
.background-white a:link {
  color: $link-color;
}

.background-white a:visited {
  color: $visited-link-color;
}

.background-white a:hover {
  color: $hover-link-color;
}
.category-filter {
  color: $body-text;
  font-size: 1.2rem;
  margin-bottom: 1rem;
  font-weight: bold;
}

/* Teacher filter section */
.teacher-filter-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-bottom: 20px;

  .button {
    margin: 5px;
    transition: all 0.3s ease;
  }
}

.teacher-filter-btn {
  background-color: white !important;
  color: black !important;
  border: 1px solid black !important;
  border-radius: 5px !important;
  padding: 8px 15px !important;
}

.teacher-filter-buttons .button {
  &.active {
    background-color: $primary-color !important;
    color: white !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  &:hover {
    background-color: $primary-color !important;
    color: white !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
}

.background-grey {
  background-color: #f5f5f5;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
  }
}

.box-shadow {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.08);

  &:hover {
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
    transform: translateY(-5px);
  }
}

/* Notification styles */
.notification-card {
  transition: all 0.3s ease;
  background-color: #ffffff !important;
  border: 1px solid #cccccc; /* Grey border around all sides */
  border-left: 4px solid $secondary-color; /* Thick left border with secondary color */
  border-radius: 10px !important;
  padding: 0 10px 15px 10px !important;

  h3 {
    padding: .5rem 1rem;
    color: #fff;
    border-radius: 10px;
    text-align: center;
    background: $primary-color;
  }

  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05) !important;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08) !important;
  }

  small {
    color: #777;
    font-size: 0.85rem;
    display: block;
    margin-bottom: 12px;
  }

  p {
    color: #333;
    line-height: 1.5;
  }
}

/* WordPress Login Page Styles */
body.login {
  background: url('../img/nkhwazi-bg.webp') no-repeat center center fixed !important;
  background-size: cover !important;
  position: relative;
  
  /* Add overlay for better readability */
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    z-index: -1;
  }
}

/* Login form container */
#login {
  width: 400px !important;
  padding: 8% 0 0;
  margin: auto;
  
  h1 {
    text-align: center;
    margin-bottom: 30px;
    
    a {
      background: rgba(255, 255, 255, 0.95) !important;
      text-decoration: none !important;
      padding: 20px !important;
      border-radius: 15px !important;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      transition: all 0.3s ease !important;
      width: 120px !important;
      height: 120px !important;
      margin: 0 auto !important;
      
      /* Hide the default WordPress logo text */
      text-indent: -9999px !important;
      overflow: hidden !important;
      
      /* Add the custom logo */
      &::before {
        content: '' !important;
        background: url('../img/nkhwazi-logo.svg') no-repeat center center !important;
        background-size: contain !important;
        width: 80px !important;
        height: 80px !important;
        display: block !important;
        text-indent: 0 !important;
      }
      
      &:hover {
        background: rgba(255, 255, 255, 1) !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3) !important;
        
        &::before {
          transform: scale(1.05) !important;
        }
      }
      
      &:focus {
        box-shadow: 0 0 0 3px rgba(54, 73, 226, 0.3) !important;
        outline: none !important;
      }
    }
  }
}

/* Login form styling */
.login form {
  background: rgba(255, 255, 255, 0.95) !important;
  padding: 30px !important;
  border-radius: 15px !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
  border: none !important;
  margin-top: 20px !important;
  backdrop-filter: blur(10px) !important;
  
  .forgetmenot {
    margin-bottom: 20px !important;
  }
  
  p {
    margin-bottom: 20px !important;
    
    label {
      color: $body-text !important;
      font-weight: 500 !important;
      font-size: 14px !important;
      margin-bottom: 8px !important;
      display: block !important;
    }
    
    input[type="text"],
    input[type="password"],
    input[type="email"] {
      width: 100% !important;
      padding: 12px 15px !important;
      border: 2px solid #e1e5e9 !important;
      border-radius: 8px !important;
      font-size: 16px !important;
      transition: all 0.3s ease !important;
      background: white !important;
      
      &:focus {
        border-color: $primary-color !important;
        box-shadow: 0 0 0 3px rgba(54, 73, 226, 0.1) !important;
        outline: none !important;
      }
    }
    
    input[type="checkbox"] {
      margin-right: 8px !important;
      transform: scale(1.2) !important;
    }
  }
  
  .submit {
    text-align: center !important;
    margin-top: 25px !important;
    
    input[type="submit"] {
      background: $primary-color !important;
      color: white !important;
      border: none !important;
      padding: 12px 30px !important;
      border-radius: 8px !important;
      font-size: 16px !important;
      font-weight: 600 !important;
      cursor: pointer !important;
      transition: all 0.3s ease !important;
      width: 100% !important;
      
      &:hover {
        background: darken($primary-color, 10%) !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 15px rgba(54, 73, 226, 0.3) !important;
      }
      
      &:active {
        transform: translateY(0) !important;
      }
    }
  }
}

/* Login links styling */
.login #nav,
.login #backtoblog {
  text-align: center !important;
  margin: 20px 0 !important;
  
  a {
    color: white !important;
    text-decoration: none !important;
    background: rgba(0, 0, 0, 0.6) !important;
    padding: 8px 15px !important;
    border-radius: 20px !important;
    font-size: 14px !important;
    transition: all 0.3s ease !important;
    display: inline-block !important;
    
    &:hover {
      background: rgba(0, 0, 0, 0.8) !important;
      color: $secondary-color !important;
      transform: translateY(-1px) !important;
    }
  }
}

/* Error and success messages */
.login .message,
.login #login_error {
  background: rgba(255, 255, 255, 0.95) !important;
  border-left: 4px solid $primary-color !important;
  border-radius: 8px !important;
  padding: 15px !important;
  margin: 20px 0 !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
  
  &.error {
    border-left-color: #dc3545 !important;
  }
  
  &.updated {
    border-left-color: #28a745 !important;
  }
}

/* Responsive adjustments for login page */
@media (max-width: 480px) {
  #login {
    width: 90% !important;
    padding: 5% 0 0 !important;
  }
  
  .login form {
    padding: 20px !important;
    margin: 10px !important;
  }
  
  #login h1 a {
    padding: 15px !important;
    width: 90px !important;
    height: 90px !important;
    
    &::before {
      width: 60px !important;
      height: 60px !important;
    }
  }
}

/* Sidebar Card Styles */
.blog-card,
.event-card {
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 15px;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);

  &:hover {
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  h6 a {
    color: $primary-color;
    text-decoration: none;
    font-weight: 600;
    
    &:hover {
      color: darken($primary-color, 10%);
      text-decoration: underline;
    }
  }

  p {
    color: #666;
    line-height: 1.4;
    margin: 0;
  }

  small {
    color: #888;
    font-size: 0.85rem;
  }
}

/* Sidebar notification card specific styles */
aside .notification-card {
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 15px;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-left: 4px solid $secondary-color;

  &:hover {
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  h6 a {
    color: $primary-color;
    text-decoration: none;
    font-weight: 600;
    
    &:hover {
      color: darken($primary-color, 10%);
      text-decoration: underline;
    }
  }

  p {
    color: #666;
    line-height: 1.4;
    margin: 0;
  }

  small {
    color: #888;
    font-size: 0.85rem;
  }
}

/* Sidebar button styles */
aside .button {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: darken($primary-color, 10%) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 10px rgba(54, 73, 226, 0.3);
  }
}

.notification-detail {
  background-color: #ffffff !important;
  border-radius: 0 10px 10px 0 !important;
  border-left: 4px solid $primary-color;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;

  h2 {
    color: $primary-color;
    font-weight: 600;
    margin-bottom: 10px;
  }

  .margin-bottom-20 {
    line-height: 1.6;

    p {
      margin-bottom: 15px;
    }

    ul,
    ol {
      margin-left: 20px;
      margin-bottom: 15px;
    }
  }

  h2 {
    color: $primary-color;
    border-bottom: 1px solid $light-grey-color;
    padding-bottom: 10px;
  }

  small {
    color: #777;
    font-size: 0.85rem;
    display: block;
    margin-bottom: 15px;
  }
}

.display-block {
  display: block;
}

/* Breadcrumbs Styling */
.position-relative {
  position: relative;
}

.breadcrumbs-container {
  position: absolute;
  bottom: 5px; /* Changed from -15px to keep it inside the header */
  left: 10px;
  z-index: 10;
}

.background-white-transparent {
  background-color: rgba(255, 255, 255, 0.85); /* Semi-transparent white */
}

.breadcrumbs {
  font-size: 0.8rem;
  text-align: left;

  .breadcrumb-item {
    display: inline-block;

    a {
      color: $link-color;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
        color: $hover-link-color;
      }
    }
  }

  .breadcrumb-separator {
    display: inline-block;
    margin: 0 5px;
    color: $grey-color;
  }
}

.padding-1x {
  padding: 10px 15px;
}

.padding-2x {
  padding: 2rem !important; /* Updated to match template-style.css but with a bit less padding */
}

/* Gallery thumbnails in single-gallery.php */
.image-with-hover-overlay img {
  border-radius: 10px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.image-with-hover-overlay {
  border-radius: 10px;
  overflow: hidden;
}

/* Responsive adjustments for breadcrumbs */
@media screen and (min-width: 992px) {
  /* Adjustments for large screens */
  .breadcrumbs-container {
    bottom: 15px;
  }
}

@media screen and (max-width: 768px) {
  /* Adjustments for small screens */
  .breadcrumbs-container {
    position: relative;
    bottom: auto;
  }
}

/* Lightcase Gallery Fixes */
/* Fix for iframe scrollbars in lightcase galleries */
#lightcase-content {
  overflow: hidden !important;
}

#lightcase-content .lightcase-contentInner {
  overflow: hidden !important;
}

/* Application Form Field with Counter Styles */
.field-with-counter {
  position: relative;
  margin-bottom: 20px; /* Add space between fields */
  width: 100%;

  input,
  textarea,
  select {
    width: 100%;
    margin-bottom: 0 !important; /* Remove default bottom margin */
  }
}

/* Application Form Character Counter Styles */
.form-header {
  background-color: $white-color;
  clear: both;
}
.character-counter {
  font-size: 0.8rem;
  text-align: right;

  color: #666;
  transition: all 0.3s ease;
  padding: 5px;
  border-radius: 3px;
  display: block;

  &.warning {
    color: #e2a736;
    font-weight: bold;
    background-color: rgba(226, 167, 54, 0.1);
  }

  &.error {
    color: #e74c3c;
    font-weight: bold;
    background-color: rgba(231, 76, 60, 0.1);
  }
}

.field-error-message {
  color: #e74c3c;
  font-size: 0.85rem;
  margin-top: 5px;
  display: block;

  /* When inside a field-with-counter */
  .field-with-counter & {
    margin-bottom: 5px;
  }
}

/* Form field validation styles */
input.error,
textarea.error,
select.error {
  border-color: #e74c3c !important;
  border-left-width: 3px !important;
}

input.valid,
textarea.valid,
select.valid {
  border-color: #2ecc71 !important;
  border-left-width: 3px !important;
}

/* Default border style for form fields */
input.default-border,
textarea.default-border,
select.default-border {
  border-color: #ddd !important;
  border-left-width: 1px !important;
}

/* Character counter styles */
.field-with-counter {
  position: relative;
  margin-bottom: 15px;
}

.character-counter {
  position: absolute;
  right: 10px;
  top: -20px;
  font-size: 12px;
  color: #777;
}

.character-counter.warning {
  color: #f39c12;
}

.character-counter.error {
  color: #e74c3c;
}

/* Field error message */
.field-error-message {
  display: block;
  color: #e74c3c;
  font-size: 12px;
  margin-top: 5px;
}

/* Form message container */
.form-message {
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  display: none;
}

.form-message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.form-message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Any error message */
.any-error {
  color: #e74c3c;
  font-weight: bold;
}

/* Ensure proper sizing for lightcase content */
#lightcase-case {
  max-width: 100vw !important;
  max-height: 100vh !important;
}

@media screen and (max-width: 768px) {
  /* Adjustments for small screens */
  .breadcrumbs-container {
    left: auto;
    margin-top: 10px;
    margin-bottom: -25px;
  }

  .breadcrumbs {
    font-size: 0.7rem;
    width: 100%;
  }
}

.background-white a:active {
  color: $active-link-color;
}

/* Blog Styles */

/* Blog post metadata container */
.blog-meta-container {
  background-color: $white-color;
  border-radius: 10px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  padding: 15px 10px !important;
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;

  .meta-item {
    font-size: 0.95rem;

    &.date {
      color: $body-text;
    }

    &.author,
    &.category {
      a {
        color: $link-color;

        &:hover {
          color: $hover-link-color;
          text-decoration: underline;
        }
      }
    }
  }

  /* Responsive adjustments */
  @media screen and (max-width: 768px) {
    .meta-item {
      font-size: 0.9rem;
    }
  }

  @media screen and (max-width: 480px) {
    flex-direction: column;

    .meta-item {
      width: 100%;
      margin-bottom: 5px;
      text-align: center !important;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

/* Blog Category Navigation - Horizontal */
.blog-categories-horizontal {
  margin-bottom: 30px;

  .blog-categories-menu {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    padding: 0;
    margin: 0;
    width: 100%; /* Ensure full width */
    box-sizing: border-box; /* Include padding in width calculation */

    li {
      margin: 0 10px 10px 0;
      list-style-type: none;
      box-sizing: border-box; /* Include padding in width calculation */
    }

    .blog-category-link {
      display: inline-block;
      padding: 8px 12px;
      background-color: #f5f5f5;
      border-radius: 5px;
      text-align: center;
      transition: all 0.3s ease;
      color: $body-text;
      font-weight: 500;
      cursor: pointer;
      width: 100%; /* Ensure full width */
      box-sizing: border-box; /* Include padding in width calculation */
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis; /* Add ellipsis for long category names */

      &:hover {
        background-color: #e0e0e0;
        color: $link-color;
        text-decoration: none;
      }

      &.active {
        background-color: $primary-color;
        color: white;

        &:hover {
          background-color: darken($primary-color, 10%);
          text-decoration: none;
        }
      }
    }
  }

  /* Responsive adjustments */
  /* Medium screens - 2 per line */
  @media screen and (max-width: 768px) and (min-width: 481px) {
    .blog-categories-menu {
      display: flex;
      flex-wrap: wrap;

      li {
        width: calc(50% - 10px); /* 2 per line with some margin */
        margin-right: 10px;
        margin-bottom: 10px;

        &:nth-child(2n) {
          margin-right: 0; /* Remove right margin for every second item */
        }
      }

      .blog-category-link {
        display: block;
        width: 100%;
      }
    }
  }

  /* Small screens - stacked */
  @media screen and (max-width: 480px) {
    .blog-categories-menu {
      flex-direction: column;

      li {
        margin-right: 0;
        width: 100%;
      }

      .blog-category-link {
        display: block;
        width: 100%;
      }
    }
  }
}

.category-filter {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: $primary-color;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 0.9rem;
}

.no-posts-message {
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 5px;
  text-align: center;
  margin-top: 20px;

  p {
    margin: 0;
    color: $body-text;
  }
}

/* Blog Images with Rounded Corners */
.single-post article img,
.blog article img,
.blog-sidebar img,
.s-12.m-4.l-4 img,
.s-12.m-12.l-12 img,
.wp-block-image img {
  border-radius: 10px !important;
  overflow: hidden !important;
}

/* WordPress Gutenberg Editor Content Spacing Styles */
/* Add breathing room to content blocks */

/* H2 Headings - Add more space above when following paragraphs */
.wp-block-heading h2,
h2.wp-block-heading,
.entry-content h2,
.post-content h2,
article h2 {
  margin-top: 2rem !important;
  margin-bottom: 1rem !important;
}

/* First H2 in content shouldn't have extra top margin */
.entry-content h2:first-child,
.post-content h2:first-child,
article h2:first-child,
.wp-block-heading:first-child h2,
h2.wp-block-heading:first-child {
  margin-top: 1rem !important;
}

/* Table Spacing - Add breathing room above and below tables */
.wp-block-table,
.entry-content table,
.post-content table,
article table,
table {
  margin-top: 1.5rem !important;
  margin-bottom: 1.5rem !important;
}

/* First table in content shouldn't have extra top margin */
.entry-content table:first-child,
.post-content table:first-child,
article table:first-child,
.wp-block-table:first-child {
  margin-top: .625rem !important;
}

/* Last table in content shouldn't have extra bottom margin */
.entry-content table:last-child,
.post-content table:last-child,
article table:last-child,
.wp-block-table:last-child {
  margin-bottom: .625rem !important;
}

/* Image Spacing - Add breathing room above and below images */
.wp-block-image,
.wp-block-media-text,
.entry-content img,
.post-content img,
article img,
.entry-content figure,
.post-content figure,
article figure {
  margin-top: 1.5rem !important;
  margin-bottom: 1.5rem !important;
}

/* Specific spacing for images when they follow or precede paragraphs */
p + .wp-block-image,
p + figure,
p + img,
.wp-block-paragraph + .wp-block-image,
.wp-block-paragraph + figure {
  margin-top: 1.8rem !important;
}

.wp-block-image + p,
figure + p,
img + p,
.wp-block-image + .wp-block-paragraph,
figure + .wp-block-paragraph {
  margin-top: 1.8rem !important;
}

/* First image in content shouldn't have extra top margin */
.entry-content img:first-child,
.post-content img:first-child,
article img:first-child,
.wp-block-image:first-child,
.entry-content figure:first-child,
.post-content figure:first-child,
article figure:first-child {
  margin-top: 1rem !important;
}

/* Last image in content shouldn't have extra bottom margin */
.entry-content img:last-child,
.post-content img:last-child,
article img:last-child,
.wp-block-image:last-child,
.entry-content figure:last-child,
.post-content figure:last-child,
article figure:last-child {
  margin-bottom: 1rem !important;
}

/* Ensure images don't break out of their containers */
.wp-block-image img,
.entry-content img,
.post-content img,
article img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Blog Home Button */
.rounded-btn {
  border-radius: 30px;
  padding: 10px 20px;
  transition: all 0.3s ease;
  display: inline-block;
  text-decoration: none;

  &:hover {
    background-color: darken($primary-color, 10%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
}

/* Blog Home Link in content area */
.blog-home-link-container {
  border-top: 1px solid $light-grey-color;
  padding-top: 20px;

  a {
    font-size: 1.1rem;
    font-weight: 500;
    display: inline-block;
    transition: all 0.3s ease;

    &:hover {
      transform: translateX(-5px);
      text-decoration: underline;
    }

    i {
      transition: all 0.3s ease;
    }

    &:hover i {
      transform: translateX(-3px);
    }
  }
}

/* Blog Category Navigation */
.blog-category-nav {
  margin-bottom: 20px !important;

  /* Add a subtle background to the filter section */
  background-color: rgba(255, 255, 255, 0.5);
  padding: 15px;
  border-radius: 8px;

  .category-link,
  .blog-category-link {
    color: $primary-color;
    text-decoration: none;
    padding: 8px 15px !important;
    margin: 0 5px;
    transition: all 0.3s ease;
    display: inline-block;
    border-radius: 20px;
    background-color: rgba(255, 255, 255, 0.7);

    &:hover {
      color: darken($primary-color, 15%);
      background-color: rgba(255, 255, 255, 0.9);
      transform: translateY(-2px);
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    &.active {
      font-weight: bold;
      background-color: $primary-color;
      color: white;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }
  }
}

/* Filter section in extracurricular activities page */
.filter-section {
  border-bottom: 1px solid rgba(54, 73, 226, 0.2);
  padding-bottom: 20px;
  margin-bottom: 30px;

  h3 {
    color: $primary-color;
    font-weight: 600;
  }
}

/* Activities container in extracurricular activities page */
.activities-container {
  padding-top: 10px;

  /* Add a subtle background to the activities section */
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  padding: 20px;

  /* Improve spacing between activity cards */
  .blog-post {
    margin-bottom: 30px;
  }

  /* No results message styling */
  .no-results-message {
    background-color: rgba(255, 255, 255, 0.8);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;

    p {
      color: $primary-color;
      font-weight: 500;
    }
  }
}

/* School Categories Styles */
/* Home page school category card */
.home-category-card {
  transition: all 0.3s ease;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);

  border-radius: 10px;
  position: relative;
  background-color: #ffffff;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.08);

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
  }
}
.school-category {
  transition: all 0.3s ease;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  padding: 0;
  border-radius: 10px;
  position: relative;
  background-color: #ffffff;
  height: 100%;
  line-height: 1.5rem;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.08);

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
  }

  img {
    width: 100%;
    height: auto;
    object-fit: cover;
    margin-bottom: 0 !important;
    border-radius: 10px 10px 0 0;
  }

  h3 {
    margin-top: 15px;
    text-align: center;
    padding: 0 15px;
  }

  p {
    flex-grow: 1;
    padding: 0 15px;
  }

  .text-more-info {
    display: inline-block;
    margin: 15px auto;
    text-align: center;
    width: auto;
    background-color: $primary-color;
    color: white !important;
    padding: 8px 20px;
    border-radius: 5px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      background-color: darken($primary-color, 10%);
      color: white !important;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
  }
}

/* Category Info Box Styles */
.category-info-box {
  border: 1px solid #eaeaea;
  margin-bottom: 10px;

  h3 {
    color: $primary-color;
    font-size: 1.2rem;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 5px;
  }

  p {
    margin-bottom: 10px;
  }
}

/* Category Sidebar Styles */
.category-sidebar-item {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

/* Single Extracurricular Page Styles */
.activity-item {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  }
}

.activity-link {
  display: block;
  text-decoration: none;
  color: inherit;

  &:hover {
    text-decoration: none;
    color: inherit;
  }
}

/* Activity Single Page Styles */
.activity-content {
  line-height: 1.6;

  p {
    margin-bottom: 1rem;
  }

  h3,
  h4 {
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
  }

  ul,
  ol {
    margin-left: 1.5rem;
    margin-bottom: 1.5rem;
  }

  img {
    max-width: 100%;
  }
}

/* 404 Error Page Styles */
.error-404 {
  text-align: center;

  .error-icon {
    font-size: 120px;
    color: $secondary-color-darker;
    margin-bottom: 20px;
  }

  .error-title {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: $primary-color;
  }

  .error-description {
    font-size: 1.2rem;
    margin-bottom: 30px;
  }

  .error-actions {
    margin-top: 30px;

    .text-more-info {
      margin: 0 10px;
    }
  }

  .search-form {
    max-width: 500px;
    margin: 0 auto;

    input[type="search"] {
      border: 1px solid $grey-color;
      border-radius: 5px;
      padding: 10px 15px;
      width: 70%;
    }

    button {
      background-color: $primary-color;
      color: white;
      border: none;
      border-radius: 5px;
      padding: 10px 20px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: darken($primary-color, 10%);
      }
    }
  }
}

/* Job Listings and Job Detail Page Styles */
/* Compatible with WordPress custom post types */
.job-card, 
.type-job, /* WordPress post_class() for job custom post type */
.hentry.job /* WordPress post_class() alternative */ {
  transition: all 0.3s ease;

  .job-card-inner,
  .entry-content-wrap /* WordPress common content wrapper class */ {
    border-radius: 10px;
    padding: 20px;
    background-color: #ffffff; /* Ensure white background */
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);

    &:hover {
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
      transform: translateY(-3px);
    }
  }

  h3, 
  .entry-title /* WordPress standard title class */ {
    margin-top: 0;

    a {
      color: inherit;
      text-decoration: none;

      &:hover {
        text-decoration: none;
      }
    }
  }
}

.job-meta {
  background-color: rgba(54, 73, 226, 0.05);
  padding: 10px;
  border-radius: 5px;
}

.job-excerpt {
  font-style: italic;
  border-left: 3px solid $primary-color;
  padding-left: 15px;
}

.job-description,
.entry-content /* WordPress standard content class */ {
  h3 {
    margin-top: 25px;
    margin-bottom: 10px;
    color: $primary-color;
  }

  ul,
  ol {
    margin-left: 20px;
  }

  /* WordPress image alignment classes */
  img.alignright {
    float: right;
    margin: 0 0 1em 1em;
  }

  img.alignleft {
    float: left;
    margin: 0 1em 1em 0;
  }

  img.aligncenter {
    display: block;
    margin-left: auto;
    margin-right: auto;
  }

  .alignright {
    float: right;
    margin: 0 0 1em 1em;
  }

  .alignleft {
    float: left;
    margin: 0 1em 1em 0;
  }

  .aligncenter {
    display: block;
    margin-left: auto;
    margin-right: auto;
  }
}

.job-application {
  background-color: $light-grey-color;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid $secondary-color;
}

.job-actions {
  margin-top: 20px;

  .button {
    margin-right: 10px;
    margin-bottom: 10px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
  }
}

.activity-header {
  padding: 10px;
  border-radius: 8px 8px 0 0;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f0f0f0;
  }
}

.activity-thumbnail {
  border-radius: 5px;
}

.activity-details {
  border-top: 1px solid #e0e0e0;
  border-radius: 0 0 8px 8px;
}

.cursor-pointer {
  cursor: pointer;
}

.background-light-grey {
  background-color: $light-grey-color;
  border-radius: 8px;
}

.category-sidebar-image {
  width: 100%;
  height: 100px;
  object-fit: cover;
  border-radius: 8px 8px 0 0;
}

.category-sidebar-title {
  padding: 10px;
  text-align: center;
  font-weight: 500;
  color: $body-text;
  background-color: #f8f8f8;
}

.category-sidebar-link {
  text-decoration: none;
  color: inherit;

  &:hover {
    text-decoration: none;
  }
}

/* Blog Post Styling */
.blog-post {
  transition: all 0.3s ease;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  padding: 10px;
  border-radius: 10px;
  position: relative;
  background-color: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.08);
}
.blog-image {
  margin-top: 8px;
}
.blog-post:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
}

/* Gallery card specific styling */
.gallery-card {
  padding: 0 0 0 0 !important; /* Remove all padding for gallery cards */
  background-color: #ffffff !important; /* Apply white background to entire card */
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05) !important; /* Enhanced shadow */
  overflow: hidden; /* Ensure content doesn't overflow */
  transition: all 0.3s ease;
  margin-bottom: 15px !important; /* Space between the info button and the bottom edge of the card*/
  border-radius: 10px !important; /* Round all corners of the card */
  border: 1px solid rgba(0, 0, 0, 0.08); /* Subtle border for better definition */
}

.gallery-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08) !important; /* Enhanced shadow on hover */
}

/* Team Card Styling */
.card {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
  margin-bottom: 0; /* Changed from 5px to remove extra space */
  padding: 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.08); /* Subtle border for better definition */

  &:hover {
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
    transform: translateY(-5px);
  }
  /* Ensure no internal spacing that might create gaps */
  box-sizing: border-box;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  }

  .team-photo {
    width: 100%;
    display: block;
    transition: all 0.3s ease;
    margin: 0;
    padding: 0;
  }

  h3 {
    margin-top: 10px;
    font-size: 1.3rem;
  }

  /* Content wrapper for all text and buttons */
  h3,
  .title,
  .read-all {
    padding-left: 15px;
    padding-right: 15px;
    margin-top: 15px; /* Add space between image and text */
  }

  .team-link {
    color: $primary-color;
    text-decoration: none;

    transition: color 0.3s ease;

    &:hover {
      color: darken($primary-color, 10%);
    }
  }

  .title {
    color: #222;
    font-size: 1.1rem !important;
    font-weight: 300;
    margin: 5px 0 5px 0;
    padding: 0 10px;
  }

  /* Remove spacer that was pushing content apart */
  &:after {
    display: none; /* Hide the spacer completely */
  }
  .read-all {
    display: inline-block;
    margin: 5px 0 0; /* Removed bottom margin completely */
    padding: 8px 20px;
    background-color: $primary-color;
    color: white;
    border-radius: 10px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      background-color: darken($primary-color, 10%);
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
  }
}

/* Fix for modal content */
.modal {
  border-radius: 12px;
  max-width: 800px;
  margin: 0 auto;

  h2,
  h3 {
    color: $primary-color;
  }

  .margin-bottom.padding {
    padding: 20px 30px;
  }

  .modal-close-button {
    background-color: $primary-color;
    color: white;
    border-radius: 30px;
    padding: 8px 20px;
    display: inline-block;
    margin: 0 auto 20px;
    transition: all 0.3s ease;

    &:hover {
      background-color: darken($primary-color, 10%);
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
  }
}

/* Gallery label styling */
.gallery-label {
  position: absolute;
  top: 25px;
  right: 15px;
  background-color: $primary-color;
  color: white;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  z-index: 2;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Extracurricular label styling */
.extracurricular-label {
  background-color: $secondary-color;
  color: #333;
  font-weight: 600;
}

/* Teacher category header styling */
.teacher-category-header {
  background-color: $primary-color;
  color: white;
  padding: 10px 0;
  font-size: 0.9rem;
  font-weight: 500;
  text-align: center;
  width: 100%;
  border-radius: 10px 10px 0 0;
  margin: 0;
  
  /* Category link styling */
  .category-link {
    color: white;
    text-decoration: none;
    display: block;
    width: 100%;
    height: 100%;
    transition: all 0.3s ease;
    
    &:hover {
      color: white;
      text-decoration: none;
      background-color: rgba(255, 255, 255, 0.1);
      border-radius: 10px 10px 0 0;
    }
    
    &:focus {
      color: white;
      text-decoration: none;
      outline: 2px solid rgba(255, 255, 255, 0.5);
      outline-offset: -2px;
    }
  }
}

/* Teaching staff card specific styling */
.card.gallery-card[data-category] {
  .gallery-content {
    padding: 15px;
    padding-bottom: 0; /* Removed bottom padding */
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    h3 {
      margin-top: 10px;
      margin-bottom: 5px;
    }

    .title {
      margin-bottom: 8px; /* Reduced bottom margin */
    }

    .read-all {
      display: inline-block;
      margin-bottom: 0; /* Removed bottom margin */
      margin-top: auto; /* Push the button to the bottom of the flex container */
    }
  }

  /* Team photo with sharp corners */
  .team-photo {
    width: 100%;
    display: block;
    margin: 0;
    padding: 0;
    border-radius: 0 !important; /* Sharp corners */
  }
}

/* Mini teacher cards for school category pages */
.teacher-mini-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
  
  .teacher-mini-photo {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    margin: 0 auto 10px;
    display: block;
  }
  
  h4 {
    font-size: 1rem;
    font-weight: 600;
    color: $primary-color;
  }
  
  p {
    color: #666;
    margin-bottom: 0;
  }
}

/* Management page specific styles */
.page-management {
  .gallery-card {
    padding-bottom: 0 !important;

    .gallery-content {
      padding-bottom: 0 !important;

      div {
        margin-bottom: 0 !important;
      }

      .read-all {
        margin-bottom: 0 !important;
      }
    }
  }
}

/* Gallery image hover effect */
.gallery-card a img.rounded-image,
.gallery-card a .team-photo.rounded-image {
  transition: all 0.3s ease;
  border-radius: 10px 10px 0 0 !important; /* Rounded top corners only for gallery images */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Add shadow to the image */
  width: 100%; /* Ensure image takes full width */
  display: block; /* Remove any extra space */
}

.gallery-card a:hover img.rounded-image,
.gallery-card a:hover .team-photo.rounded-image {
  opacity: 0.85;
  transform: scale(1.02);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Gallery card text content */
.gallery-content {
  padding: 15px 15px 0px; /* Removed bottom padding to fix extra space */
  margin-top: 0;
  margin-bottom: 0; /* Added to remove bottom margin */

  h3 {
    margin-top: 0;
    margin-bottom: 8px; /* Reduced bottom margin */
  }

  p {
    margin-bottom: 8px; /* Reduced bottom margin */
  }

  .text-more-info {
    display: inline-block;
    margin-bottom: 0; /* Removed bottom margin */
  }

  /* Fix for the container of the View Bio button */
  div {
    margin-bottom: 0 !important; /* Remove bottom margin from button container */
  }
}

.blog-post img.rounded-image {
  width: 100%;
  border-radius: 10px 10px 0 0;
}

.blog-post h3 {
  line-height: 1.3;
}

.blog-post p {
  margin: 10px 0;
  line-height: 1.5;
}

.blog-author a,
.blog-category a {
  color: #555;
  text-decoration: none;
  transition: color 0.3s ease;
}

.blog-author a:hover,
.blog-category a:hover {
  color: #0074d9;
}

/* Nationality Dropdown Styling */
select[name="nationality"],
select[name="nationality_f"],
select[name="nationality_m"] {
  max-height: 300px;
  overflow-y: auto !important;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;

  option {
    padding: 8px 12px;

    &:hover {
      background-color: #f5f5f5;
    }
  }
}

/* Nationality Dropdown Styling */
select[name="nationality"],
select[name="nationality_f"],
select[name="nationality_m"] {
  max-height: 300px;
  overflow-y: auto !important;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;

  option {
    padding: 8px 12px;

    &:hover {
      background-color: #f5f5f5;
    }
  }
}

/* Pagination Styling - Removed duplicate styles */

/* Blog Details Link Styling */
.blog-categories {
  background-color: $secondary-color-darker;
  padding: 0.8rem;
  margin-bottom: 20px;
  color: $white-color;
}
.blog-post .text-more-info {
  display: inline-block;
  margin-top: 10px;
  font-weight: 500;
  position: relative;
  padding-right: 20px;
}

.blog-post .text-more-info:hover:after {
  transform: translateX(5px);
}

/* Blog Post Category Tags */
.blog-category a {
  background-color: #f1f1f1;
  padding: 3px 8px;
  border-radius: 3px;
  font-size: 0.85em;
  transition: background-color 0.3s ease;
}

.blog-category a:hover {
  background-color: #e0e0e0;
}

/* Footer styles */
footer {
  .section.background-blue {
    border-radius: 10px 10px 0 0;
    overflow: hidden;
  }
  .bottom-footer {
    margin-bottom: 0.5rem;
  }
  .padding.background-yellow {
    border-radius: 0 0 10px 10px;
    overflow: hidden;
  }
}

.footer-links {
  border-top: 1px solid $secondary-color;
  padding-top: 0.625rem;

  p {
    margin-bottom: 15px; /* Adjust this value to increase/decrease the gap */
  }
}
.admin-contact-info {
  padding-top: 1rem;
  margin-top: 2rem;
}
.admin-info-item {
  background: rgba(54, 73, 226, 0.6);
  padding: 0.8rem !important;
  padding-top: 0.8rem !important;
  border-radius: 10px;
  overflow: hidden;
}
.footer-links {
  border-top: 1px solid $secondary-color;
  padding-top: 1rem;

  p {
    margin-bottom: 10px; /* Adjust this value to increase/decrease the gap */
  }
}

footer h4 {
  color: $secondary-color !important;
  font-family: "Noto Serif Georgian", serif;
  font-optical-sizing: auto;
  font-weight: 600;
  font-style: normal;
  font-variation-settings: "wdth" 100;
}
.admin-name {
  word-wrap: break-word;
}

.admin-phone {
  /* Prevent aggressive word breaking but allow natural wrapping */
  word-break: keep-all;
  overflow-wrap: break-word;
  hyphens: none;
  
  /* Ensure phone numbers don't overflow their container */
  max-width: 100%;
  box-sizing: border-box;
  
  /* Allow wrapping at natural break points (spaces, hyphens) */
  white-space: normal;
  
  /* For very long phone numbers without spaces, allow breaking as last resort */
  word-wrap: break-word;
}

/* Slider styles */
.carousel-default .item img {
  @extend .rounded-image;
}

/*** Navigation bar styles */
.top-nav li a,
.background-white .top-nav li a {
  color: #002633;
  font-size: 1rem;
  padding: 0.7em 1.25em;
}
nav {
  border-bottom: 4px solid rgba(0, 0, 0, 0.05);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: 0.5rem 0;
  position: relative;
  z-index: 15;
}
.top-nav ul ul {
  background: $secondary-color none repeat scroll 0 0;
}
.top-nav li ul li {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}
.top-nav li ul li:last-child {
  border-bottom: 0;
}
.top-nav li ul li a,
.background-white .top-nav li ul li a,
.top-nav .active-item li a {
  background: $light-grey-color none repeat scroll 0 0;
  color: $primary-color;
}

@media screen and (min-width: 769px) {
  footer h4 {
    text-align: center !important;
  }
  .developer-credit {
    text-align: right;
  }
}

/* Application Form Styles */
.application-form {
  color: $primary-color;
  font-weight: 600;
}

.any-error {
  color: #c81010;
  font-weight: bold;
}

.any-success {
  color: #06a10b;
  font-weight: bold;
}

/* Style for required fields */
form.customform input.required,
form.customform select.required,
form.customform textarea.required {
  border-left: 4px solid #c81010;
}

/* Improve form field spacing */
form.customform input,
form.customform select,
form.customform textarea {
  margin-bottom: 1rem;
}

/* Style for form section headings */
h3.application-form {
  margin-top: 2rem;
  font-size: 1.4rem;
}

h4.text-left {
  margin-top: 1.5rem;
  color: $primary-color;
  font-weight: 600;
}

/* Style for submit and reset buttons */
.submit-btn {
  background-color: $primary-color !important;
  transition: background-color 0.3s ease;
}

.submit-btn:hover {
  background-color: darken($primary-color, 10%) !important;
}

.cancel-btn {
  background-color: #c81010 !important;
  transition: background-color 0.3s ease;
}

.cancel-btn:hover {
  background-color: darken(#c81010, 10%) !important;
}

/* Datepicker styling */
.ui-datepicker {
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
  padding: 10px;
}

.ui-datepicker-header {
  background-color: $primary-color;
  color: white;
  border-radius: 3px;
  padding: 5px;
}

.ui-datepicker-calendar th {
  padding: 5px;
  text-align: center;
}

.ui-datepicker-calendar td {
  padding: 2px;
  text-align: center;
}

.ui-datepicker-calendar a {
  display: block;
  padding: 5px;
  text-decoration: none;
  border-radius: 3px;
}

.ui-datepicker-calendar a:hover {
  background-color: $secondary-color;
  color: white;
}

/* Mobile styles */
@media screen and (max-width: 768px) {
  /* Ensure the hamburger menu is visible */
  .nav-text {
    display: block !important;
  }
  .top-nav li a,
  .background-white .top-nav li a {
    background: $light-grey-color none repeat scroll 0 0;
    color: #000;
    font-size: 1.2em;
    padding: 1em;
    text-align: center;
  }

  .top-nav li ul li ul li a {
    background: none repeat scroll 0 0 #456274;
  }
  .top-nav {
    background: none repeat scroll 0 0 $light-grey-color;
  }

  .top-nav li ul li a,
  .background-white .top-nav li ul li a,
  .top-nav .active-item li a {
    background: $grey-color none repeat scroll 0 0;
    color: #000;
  }
  h1 {
    font-size: 1.7rem;
    text-align: center;
  }
  h2 {
    font-size: 1.4rem;
  }
  h3 {
    font-size: 1.1rem;
  }
  h4 {
    font-size: 1rem;
  }

  .blog-post h3 {
    font-size: 1.1rem;
  }

  .homepage-headers,
  .page-headers {
    font-size: 1.4rem;
  }

  .number-stat {
    margin-bottom: 15px;
  }
  /* More info button */
  a.text-more-info {
    margin-bottom: 2rem;
  }

  hr.break-alt {
    margin: 5px 0 !important;
  }

  /* Mobile centered menu styles */
  .centered-menu {
    display: block;
  }

  .centered-menu > li {
    display: block;
  }
  .admin-info-item {
    margin-bottom: 1rem;
    text-align: center;
  }
  footer h4 {
    text-align: left;
  }
}

@media screen and (max-width: 480px) {
  .developer-credit {
    margin-top: 1rem !important;
  }
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .blog-post {
    margin-bottom: 30px;
  }
}

@media screen and (max-width: 480px) {
  .pagination li a {
    padding: 6px 12px;
  }
  .blog-author,
  .blog-category,
  .blog-date {
    font-size: 0.8rem;
  }
}

/* Fix for teacher cards grid to prevent cards from bumping into each other */
.grid.margin {
  grid-row-gap: 20px; /* Add vertical spacing between rows for all grids */
}

/* Specific styling for teacher cards grid */
.teacher-cards-grid {
  grid-row-gap: 50px !important; /* Add more vertical spacing between rows for teacher cards */
  display: grid !important; /* Ensure grid display is enforced */
  margin-bottom: 30px !important; /* Add bottom margin to the entire grid */
}

/* Utility class for smaller bottom margins */
.margin-bottom-5 {
  margin-bottom: 5px !important;
}

/* Specific styling for management page cards */
.page-management .gallery-card .gallery-content {
  padding: 10px 10px 5px; /* Further reduced padding for management page */
}

/* Pagination Styles */
.pagination {
  display: flex;
  justify-content: center;
  list-style: none;
  padding: 0;
  margin: 30px 0;
  flex-wrap: wrap;
}

.pagination li {
  margin: 0 5px;
  display: inline-block;
}

.pagination a {
  display: block;
  padding: 8px 15px;
  text-decoration: none;
  color: $body-text;
  background-color: $white-color;
  border: 1px solid $grey-color;
  border-radius: 5px;
  transition: all 0.3s ease;

  &:hover {
    background-color: lighten($primary-color, 40%);
    color: $primary-color;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }

  &.active-page {
    background-color: $primary-color;
    color: $white-color;
    border-color: $primary-color;
    font-weight: bold;
  }

  &.previous-page,
  &.next-page {
    background-color: $light-grey-color;

    &:hover {
      background-color: $grey-color;
      color: $body-text;
    }
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }
}

/* WordPress Default Pagination Styles */
.pagination-nav {
  margin: 0; /* Remove margin since the container has padding */
  background-color: transparent !important; /* Override Responsee background */

  .page-numbers {
    display: flex !important;
    justify-content: center !important;
    flex-wrap: wrap !important;
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
    background-color: transparent !important; /* Ensure no background from Responsee */

    li {
      margin: 0 5px 5px 0 !important;
      display: inline-block !important;
      list-style-type: none !important;
      background-color: transparent !important; /* Ensure no background from Responsee */
      padding: 0 !important; /* Reset padding */
    }

    a,
    span {
      display: inline-block !important;
      padding: 8px 15px !important;
      background-color: $white-color !important;
      border: 1px solid $grey-color !important;
      border-radius: 5px !important;
      color: $body-text !important;
      text-decoration: none !important;
      transition: all 0.3s ease !important;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;

      &:hover {
        background-color: lighten($primary-color, 40%) !important;
        color: $primary-color !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15) !important;
      }
    }

    .current {
      background-color: $primary-color !important;
      color: $white-color !important;
      border-color: $primary-color !important;
      font-weight: bold !important;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
    }

    .dots {
      background: none !important;
      border: none !important;
      padding: 8px 5px !important;
    }

    /* Style for previous and next buttons */
    .prev,
    .next {
      background-color: $secondary-color !important;
      color: $white-color !important;
      border-color: $secondary-color !important;
      font-weight: bold !important;

      &:hover {
        background-color: darken($secondary-color, 10%) !important;
        border-color: darken($secondary-color, 10%) !important;
        color: $white-color !important;
      }
    }
  }
}

/* Add specific spacing control for pagination container */
.pagination-container {
  margin-top: 30px; /* Keep space above pagination */
  margin-bottom: 15px; /* Reduce space below pagination by half */
  background-color: rgba(
    255,
    255,
    255,
    0.7
  ) !important; /* Semi-transparent white background */
  border-radius: 10px !important; /* Rounded corners */
  padding: 15px !important; /* Add some padding */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05) !important; /* Subtle shadow */

  /* Special styling when inside a rounded div */
  .rounded-div & {
    background-color: rgba(255, 255, 255, 0.8) !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
    margin-top: 20px;
    margin-bottom: 0;
    border-top: 1px solid rgba(54, 73, 226, 0.2);
    padding-top: 20px !important;

    /* Make pagination more visible for testing */
    .pagination a {
      font-weight: 600;
      padding: 10px 18px;
      margin: 0 3px;

      &.active-page {
        transform: scale(1.1);
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
      }
    }
  }
}

/* Additional spacing control for specific sections */
.section .line:last-child .pagination-container {
  margin-bottom: 15px; /* Ensure consistent spacing before footer */
}

/* Ensure proper spacing after content */
.grid.margin + .pagination-container {
  margin-top: 30px; /* Maintain space after content */
}

/* Responsive adjustments for pagination */
@media screen and (max-width: 768px) {
  .pagination-nav .page-numbers li {
    margin-bottom: 10px;
  }
}

/* Contact Form Styles */
.contact-form {
  position: relative;

  .form-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 100;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
  }

  .spinner {
    display: inline-block;
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid $primary-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .form-message {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
    display: none;
    text-align: center;
    font-weight: bold;

    &.success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    &.error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
}

/* Admin phone number call button styles */
.admin-phone .call-button {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 5px;
  transition: all 0.3s ease;
  text-decoration: none;
  background-color: rgba(226, 207, 54, 0.2);
  
  &:hover, &:focus {
    background-color: rgba(226, 207, 54, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  }
  
  /* Make more touch-friendly on mobile */
  @media (max-width: 768px) {
    padding: 8px 15px;
    margin: 5px 0;
    display: block;
    font-size: 1.1rem;
  }
}

/* Legal Pages Template Styles */
.legal-content {
  font-size: 1rem;
  line-height: 1.6;
  
  h2 {
    font-size: 1.5rem;
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: $primary-color;
  }
  
  h3 {
    font-size: 1.3rem;
    margin-top: 1.5rem;
    margin-bottom: 0.8rem;
  }
  
  p {
    margin-bottom: 1rem;
  }
  
  ul, ol {
    margin-left: 1.5rem;
    margin-bottom: 1.5rem;
  }
  
  li {
    margin-bottom: 0.5rem;
  }
  
  table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1.5rem;
    
    th, td {
      padding: 0.75rem;
      border: 1px solid #ddd;
    }
    
    th {
      background-color: lighten($primary-color, 45%);
      font-weight: bold;
    }
    
    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
  }
}

.legal-sidebar {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  
  &.padding-2x {
    padding: 2rem !important; /* Override the padding-2x class for legal-sidebar */
  }
}

} /* End of .contact-form */

/* Standalone legal navigation styles that apply everywhere */
.legal-nav {
  list-style: none;
  padding: 0;
  margin: 0 0 1.5rem 0;
  
  li {
    margin-bottom: 1rem; /* Further increased spacing between list items */
  }
  
  a {
    display: block;
    padding: 0.5rem 1.5rem; /* Increased padding for all links */
    background-color: #f5f5f5;
    border-left: 3px solid transparent;
    transition: all 0.3s ease;
    text-decoration: none;
    color: $body-text;
    border-radius: 5px;
   
    
    &:hover {
      background-color: #e9e9e9;
      border-left-color: $secondary-color;
      transform: translateX(3px);
      padding: .5rem 1.5rem !important; /* Ensure there's padding inside the border */
    }
    
    &.active-link {
      background-color: lighten($primary-color, 45%);
      border-left-color: $primary-color;
      color: $primary-color;
      font-weight: bold;
      border: 2px solid $secondary-color; /* Add border with secondary color */
      padding: .2rem 1.5rem !important; /* Ensure there's padding inside the border */
      margin: 0.2rem 0; /* Add margin for better spacing between items */
      line-height: 1.8; /* Further increased line height for better readability */
    }
  }
}
  
.legal-contact {
  background-color: lighten($secondary-color, 35%);
  padding: 1.5rem;
  border-radius: 4px;
  
  a {
    color: $primary-color;
    font-weight: bold;
    text-decoration: underline;
    
    &:hover {
      color: darken($primary-color, 15%);
    }
  }
}

.background-light-gray {
  background-color: #f8f8f8;
}

.submit-btn {
  position: relative;
  transition: all 0.3s ease;

  &.submitting {
    background-color: #cccccc !important;
    cursor: not-allowed;
  }

  &:hover {
    background-color: darken($primary-color, 10%) !important;
  }
}

.spinner-text {
  display: inline-block;
  position: relative;

  &:after {
    content: "...";
    position: absolute;
    right: -12px;
    animation: ellipsis 1.5s infinite;
  }
}

@keyframes ellipsis {
  0% {
    content: ".";
  }
  33% {
    content: "..";
  }
  66% {
    content: "...";
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Breadcrumb Styles */
.breadcrumb-container {
  background-color: $white-color;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 0;
  margin: 0;
  position: relative;
  z-index: 5;
  
  .breadcrumb-wrapper {
    background-color: $white-color;
    padding: 4px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin: .625rem 0;
    border-left: 3px solid $primary-color;
    
    /* Style the breadcrumb navigation */
    nav,
    .flexy-breadcrumb,
    .breadcrumb {
      margin: 0;
      padding: 0;
      
      ol,
      ul {
        list-style: none;
        margin: 0;
        padding: 0;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        
        li {
          display: inline-flex;
          align-items: center;
          font-size: 0.9rem;
          line-height: 1.4;
          
          &:not(:last-child)::after {
            content: "›";
            margin: 0 8px;
            color: $grey-color;
            font-weight: bold;
            font-size: 1rem;
          }
          
          a {
            color: $primary-color;
            text-decoration: none;
            transition: color 0.3s ease;
            font-weight: 500;
            
            &:hover {
              color: $secondary-color-darker;
              text-decoration: underline;
            }
          }
          
          /* Current page (last item) styling */
          &:last-child {
            color: $body-text;
            font-weight: 600;
          }
        }
      }
    }
    
    /* Handle different breadcrumb plugin structures */
    .breadcrumb-item,
    .breadcrumb-link {
      display: inline-flex;
      align-items: center;
      
      &:not(:last-child)::after {
        content: "›";
        margin: 0 8px;
        color: $grey-color;
        font-weight: bold;
        font-size: 1rem;
      }
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
      padding: 10px 15px;
      margin: 8px 0;
      
      nav,
      .flexy-breadcrumb,
      .breadcrumb {
        ol,
        ul {
          li {
            font-size: 0.85rem;
            
            &:not(:last-child)::after {
              margin: 0 6px;
              font-size: 0.9rem;
            }
          }
        }
      }
    }
    
    @media (max-width: 480px) {
      padding: 8px 12px;
      
      nav,
      .flexy-breadcrumb,
      .breadcrumb {
        ol,
        ul {
          li {
            font-size: 0.8rem;
            
            &:not(:last-child)::after {
              margin: 0 4px;
              font-size: 0.85rem;
            }
          }
        }
      }
    }
  }
}

/* ========================================
 * COMPREHENSIVE GUTENBERG LIST STYLES
 * ========================================
 * 
 * Beautiful and functional list styles for WordPress Gutenberg editor
 * 
 * Usage Instructions:
 * 1. In Gutenberg editor, select a List block
 * 2. In the block settings sidebar, go to "Advanced" > "Additional CSS class(es)"
 * 3. Add one of the classes below
 * 
 * Available List Classes:
 * - list-checkmark: Green checkmarks for completed items
 * - list-star: Golden star bullets for featured items
 * - list-arrow: Modern arrow bullets
 * - list-numbered-circle: Circular numbered items
 * - list-timeline: Timeline-style list with connecting lines
 * - list-feature: Feature list with icons and descriptions
 * - list-steps: Step-by-step process list
 * - list-highlight: Highlighted important items
 * - list-minimal: Clean minimal style
 * - list-card: Card-style list items
 * - list-gradient: Gradient bullet points
 * - list-academic: Academic/educational style
 */

/* Base List Improvements */
.wp-block-list,
.entry-content ul,
.entry-content ol,
.post-content ul,
.post-content ol {
  margin: 1.5rem 0;
  padding-left: 0;
  
  li {
    margin-bottom: 0.75rem;
    line-height: 1.6;
    position: relative;
  }
}

/* Remove default list markers for all custom list styles */
.list-checkmark,
.list-star,
.list-arrow,
.list-numbered-circle,
.list-timeline,
.list-feature,
.list-steps,
.list-highlight,
.list-minimal,
.list-card,
.list-gradient,
.list-academic {
  list-style: none !important;
  padding-left: 0 !important;
  margin-left: 0 !important;
  
  li {
    list-style: none !important;
    list-style-type: none !important;
    
    &::marker {
      display: none !important;
    }
  }
  
  /* Target nested lists too */
  ul, ol {
    list-style: none !important;
    padding-left: 0 !important;
    margin-left: 0 !important;
    
    li {
      list-style: none !important;
      list-style-type: none !important;
      
      &::marker {
        display: none !important;
      }
    }
  }
}

/* 1. Checkmark List - Perfect for completed tasks, achievements */
.list-checkmark {
  
  li {
    position: relative;
    padding-left: 2.5rem;
    margin-bottom: 1rem;
    
    &::before {
      content: "✓";
      position: absolute;
      left: 0;
      top: 0;
      width: 1.8rem;
      height: 1.8rem;
      background: linear-gradient(135deg, #2ecc71, #27ae60);
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 0.9rem;
      box-shadow: 0 2px 4px rgba(46, 204, 113, 0.3);
      transition: all 0.3s ease;
    }
    
    &:hover::before {
      transform: scale(1.1);
      box-shadow: 0 4px 8px rgba(46, 204, 113, 0.4);
    }
  }
}

/* 2. Star List - Perfect for featured items, highlights */
.list-star {
  
  li {
    position: relative;
    padding-left: 2.5rem;
    margin-bottom: 1rem;
    
    &::before {
      content: "★";
      position: absolute;
      left: 0;
      top: 0;
      width: 1.8rem;
      height: 1.8rem;
      background: linear-gradient(135deg, #f39c12, #e67e22);
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 1rem;
      box-shadow: 0 2px 4px rgba(243, 156, 18, 0.3);
      transition: all 0.3s ease;
    }
    
    &:hover::before {
      transform: rotate(72deg) scale(1.1);
      box-shadow: 0 4px 8px rgba(243, 156, 18, 0.4);
    }
  }
}

/* 3. Arrow List - Modern and directional */
.list-arrow {
  
  li {
    position: relative;
    padding-left: 2.5rem;
    margin-bottom: 1rem;
    
    &::before {
      content: "→";
      position: absolute;
      left: 0;
      top: 0;
      width: 1.8rem;
      height: 1.8rem;
      background: linear-gradient(135deg, $primary-color, #2c3e50);
      color: white;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 1rem;
      box-shadow: 0 2px 4px rgba(54, 73, 226, 0.3);
      transition: all 0.3s ease;
    }
    
    &:hover::before {
      transform: translateX(3px);
      box-shadow: 0 4px 8px rgba(54, 73, 226, 0.4);
    }
  }
}

/* 4. Numbered Circle List - Elegant numbered items */
.list-numbered-circle {
  counter-reset: circle-counter;
  
  li {
    position: relative;
    padding-left: 3rem;
    margin-bottom: 1.2rem;
    counter-increment: circle-counter;
    
    &::before {
      content: counter(circle-counter);
      position: absolute;
      left: 0;
      top: 0;
      width: 2rem;
      height: 2rem;
      background: linear-gradient(135deg, $secondary-color, $secondary-color-darker);
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 0.9rem;
      box-shadow: 0 2px 6px rgba(226, 207, 54, 0.3);
      transition: all 0.3s ease;
    }
    
    &:hover::before {
      transform: scale(1.15);
      box-shadow: 0 4px 12px rgba(226, 207, 54, 0.4);
    }
  }
}

/* 5. Timeline List - Perfect for chronological events */
.list-timeline {
  position: relative;
  
  &::before {
    content: "";
    position: absolute;
    left: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, $primary-color, $secondary-color);
    border-radius: 1px;
  }
  
  li {
    position: relative;
    padding-left: 3.5rem;
    margin-bottom: 2rem;
    
    &::before {
      content: "";
      position: absolute;
      left: 0.5rem;
      top: 0.3rem;
      width: 1rem;
      height: 1rem;
      background: $primary-color;
      border: 3px solid white;
      border-radius: 50%;
      box-shadow: 0 0 0 2px $primary-color, 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }
    
    &:hover::before {
      transform: scale(1.3);
      background: $secondary-color;
      box-shadow: 0 0 0 2px $secondary-color, 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    &:last-child {
      margin-bottom: 1rem;
    }
  }
}

/* 6. Feature List - Perfect for features, benefits */
.list-feature {
  
  li {
    background: white;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    padding-left: 4rem;
    
    &::before {
      content: "◆";
      position: absolute;
      left: 1.5rem;
      top: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
      background: linear-gradient(135deg, $primary-color, $secondary-color);
      color: white;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.8rem;
      font-weight: bold;
    }
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
      border-color: $primary-color;
    }
  }
}

/* 7. Steps List - Perfect for processes, instructions */
.list-steps {
  counter-reset: step-counter;
  
  li {
    position: relative;
    padding: 1.5rem 1.5rem 1.5rem 4.5rem;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-left: 4px solid $primary-color;
    border-radius: 0 8px 8px 0;
    counter-increment: step-counter;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    
    &::before {
      content: "Step " counter(step-counter);
      position: absolute;
      left: 1rem;
      top: 1rem;
      background: $primary-color;
      color: white;
      padding: 0.3rem 0.8rem;
      border-radius: 12px;
      font-size: 0.75rem;
      font-weight: bold;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    &:hover {
      transform: translateX(5px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      background: linear-gradient(135deg, #ffffff, #f8f9fa);
    }
  }
}

/* 8. Highlight List - Perfect for important points */
.list-highlight {
  
  li {
    position: relative;
    padding: 1rem 1rem 1rem 3rem;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, rgba(226, 207, 54, 0.1), rgba(226, 207, 54, 0.05));
    border-left: 4px solid $secondary-color;
    border-radius: 4px;
    transition: all 0.3s ease;
    
    &::before {
      content: "!";
      position: absolute;
      left: 0.8rem;
      top: 50%;
      transform: translateY(-50%);
      width: 1.5rem;
      height: 1.5rem;
      background: $secondary-color;
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 0.9rem;
    }
    
    &:hover {
      background: linear-gradient(135deg, rgba(226, 207, 54, 0.15), rgba(226, 207, 54, 0.08));
      transform: translateX(3px);
    }
  }
}

/* 9. Minimal List - Clean and simple */
.list-minimal {
  
  li {
    position: relative;
    padding-left: 2rem;
    margin-bottom: 0.8rem;
    
    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 0.7rem;
      width: 6px;
      height: 6px;
      background: $primary-color;
      border-radius: 50%;
      transition: all 0.3s ease;
    }
    
    &:hover::before {
      transform: scale(1.5);
      background: $secondary-color;
    }
  }
}

/* 10. Card List - Each item as a card */
.list-card {
  
  li {
    background: white;
    border: 1px solid #e1e8ed;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      border-color: $primary-color;
    }
  }
}

/* 11. Gradient List - Colorful gradient bullets */
.list-gradient {
  
  li {
    position: relative;
    padding-left: 2.5rem;
    margin-bottom: 1rem;
    
    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 0.3rem;
      width: 1.2rem;
      height: 1.2rem;
      background: linear-gradient(135deg, $primary-color, $secondary-color);
      border-radius: 50%;
      transition: all 0.3s ease;
    }
    
    &:nth-child(2n)::before {
      background: linear-gradient(135deg, $secondary-color, $primary-color);
    }
    
    &:nth-child(3n)::before {
      background: linear-gradient(135deg, #e74c3c, #f39c12);
    }
    
    &:nth-child(4n)::before {
      background: linear-gradient(135deg, #9b59b6, #3498db);
    }
    
    &:hover::before {
      transform: scale(1.3) rotate(180deg);
    }
  }
}

/* 12. Academic List - Perfect for educational content */
.list-academic {
  
  li {
    position: relative;
    padding: 1rem 1rem 1rem 3.5rem;
    margin-bottom: 1rem;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    transition: all 0.3s ease;
    
    &::before {
      content: "📚";
      position: absolute;
      left: 1rem;
      top: 50%;
      transform: translateY(-50%);
      font-size: 1.2rem;
      transition: all 0.3s ease;
    }
    
    &:nth-child(2n)::before {
      content: "📝";
    }
    
    &:nth-child(3n)::before {
      content: "🎓";
    }
    
    &:nth-child(4n)::before {
      content: "📖";
    }
    
    &:hover {
      background: white;
      border-color: $primary-color;
      transform: translateX(5px);
    }
    
    &:hover::before {
      transform: translateY(-50%) scale(1.2);
    }
  }
}

/* Responsive Adjustments for All List Styles */
@media (max-width: 768px) {
  .list-checkmark,
  .list-star,
  .list-arrow,
  .list-numbered-circle,
  .list-timeline,
  .list-feature,
  .list-steps,
  .list-highlight,
  .list-minimal,
  .list-card,
  .list-gradient,
  .list-academic {
    li {
      font-size: 0.95rem;
      line-height: 1.5;
    }
  }
  
  .list-feature,
  .list-card {
    li {
      padding: 1rem;
    }
  }
  
  .list-steps li {
    padding: 1rem 1rem 1rem 3.5rem;
  }
  
  .list-timeline {
    &::before {
      left: 0.8rem;
    }
    
    li {
      padding-left: 3rem;
      
      &::before {
        left: 0.3rem;
      }
    }
  }
}

@media (max-width: 480px) {
  .list-checkmark,
  .list-star,
  .list-arrow,
  .list-numbered-circle,
  .list-minimal,
  .list-gradient {
    li {
      padding-left: 2rem;
      
      &::before {
        width: 1.4rem;
        height: 1.4rem;
        font-size: 0.8rem;
      }
    }
  }
  
  .list-numbered-circle li::before {
    width: 1.6rem;
    height: 1.6rem;
  }
  
  .list-feature,
  .list-academic {
    li {
      padding-left: 3rem;
    }
  }
  
  .list-steps li {
    padding: 1rem 0.8rem 1rem 3rem;
    
    &::before {
      font-size: 0.7rem;
      padding: 0.2rem 0.6rem;
    }
  }
}

/* ========================================
 * SIDEBAR TEXT LEGIBILITY IMPROVEMENTS
 * ========================================
 * Addressing small text issues in main sidebar
 * Improving readability by increasing font sizes
 */

/* Main Sidebar Text Size Improvements */
aside {
  /* Override small text sizes in sidebar for better legibility */
  .text-size-12 {
    font-size: 14px !important; /* Increased from 12px to 14px */
    line-height: 1.5 !important; /* Improved line height for readability */
  }
  
  /* Ensure sidebar headings are appropriately sized */
  h5 {
    font-size: 1.1rem !important; /* Slightly larger than default */
    margin-bottom: 15px !important;
  }
  
  h6 {
    font-size: 1rem !important; /* Ensure h6 titles are readable */
    line-height: 1.4 !important;
    margin-bottom: 8px !important;
  }
  
  /* Improve button text readability in sidebar */
  .button.text-size-12 {
    font-size: 13px !important; /* Slightly larger button text */
    padding: 8px 12px !important; /* Adjust padding for better proportion */
    line-height: 1.3 !important;
    color: white !important; /* Ensure white text on primary color buttons */
  }
  
  /* Ensure small text elements maintain minimum readable size */
  small,
  .text-dark {
    font-size: 13px !important; /* Minimum readable size for small text */
    line-height: 1.4 !important;
  }
  
  /* Card content improvements */
  .notification-card,
  .event-card,
  .blog-card {
    .text-size-12 {
      font-size: 14px !important;
      color: #555 !important; /* Slightly darker for better contrast */
    }
    
    /* Ensure adequate spacing between elements */
    .notification-item,
    .event-item,
    .blog-item {
      margin-bottom: 12px !important;
      
      &:last-child {
        margin-bottom: 0 !important;
      }
    }
  }
}

/* Responsive adjustments for sidebar text */
@media (max-width: 768px) {
  aside {
    .text-size-12 {
      font-size: 15px !important; /* Even larger on mobile for better touch accessibility */
    }
    
    .button.text-size-12 {
      font-size: 14px !important;
      padding: 10px 14px !important;
      color: white !important; /* Maintain white text on mobile */
    }
  }
}

@media (max-width: 480px) {
  aside {
    .text-size-12 {
      font-size: 16px !important; /* Largest on very small screens */
    }
  }
}