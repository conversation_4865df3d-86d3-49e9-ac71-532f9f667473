<?php
/**
 * Simple Contact Form Functionality
 *
 * @package NkhwaziSchool
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * Enqueue scripts and styles for the contact form
 */
function nkhwazi_enqueue_contact_form_script() {
    global $post;
    
    // Check if we're on a page that might contain the contact form
    $enqueue_scripts = false;
    
    if (is_page()) {
        // Check if the page is the contact page
        if (is_page('contact') || is_page('contact-us')) {
            $enqueue_scripts = true;
        }
        
        // Check if the page content contains our shortcode
        if (isset($post->post_content) && has_shortcode($post->post_content, 'nkhwazi_contact_form')) {
            $enqueue_scripts = true;
        }
    }
    
    if ($enqueue_scripts) {
        // Enqueue the contact form script
        wp_enqueue_script(
            'nkhwazischool-simple-contact-form',
            get_template_directory_uri() . '/assets/js/simple-contact-form.js',
            array('jquery'),
            '1.0.2', // Increment version to bust cache
            true
        );
        
        // Pass AJAX URL to script
        wp_localize_script(
            'nkhwazischool-simple-contact-form',
            'nkhwazi_contact_form',
            array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('nkhwazi_contact_form_nonce'),
                'recaptcha_site_key' => '6Lf79rcaAAAAALdqWY74mw_n6DtTxR_C5AEL6cfL'
            )
        );
        
        // Add inline script to define ajaxurl
        wp_add_inline_script('nkhwazischool-simple-contact-form', 'var ajaxurl = "' . admin_url('admin-ajax.php') . '";', 'before');
    }
}
add_action('wp_enqueue_scripts', 'nkhwazi_enqueue_contact_form_script');

/**
 * Contact Form Shortcode
 * 
 * Usage: [nkhwazi_contact_form]
 */
function nkhwazi_contact_form_shortcode($atts) {
    // Extract shortcode attributes
    $atts = shortcode_atts(array(
        'title' => __('Contact Us', 'nkhwazischool'),
        'submit_text' => __('Send Message', 'nkhwazischool'),
    ), $atts);
    
    // Start output buffering
    ob_start();
    
    // Form message container
    echo '<div id="form-message" class="margin-bottom" style="display: none;"></div>';
    
    // Start form
    echo '<form class="customform simple-contact-form nkhwazi-contact-form" method="post" action="javascript:void(0);">';
    
    // Form message container for validation
    echo '<div class="form-message"></div>';
    
    // Add a note about required fields
    echo '<div class="required-fields-note margin-bottom"><small>* Fields with an asterisk and red border are required</small></div>';
    
    // Get admin contacts for recipient dropdown
    $admin_contacts = nkhwazi_get_admin_contacts();
    
    if (count($admin_contacts) > 1) {
        // Show dropdown only if there are multiple contacts
        echo '<div class="grid margin">
            <div class="s-12 m-8 l-6">
                <select name="recipient" class="required" required>
                    <option value="">' . __('Select Recipient *', 'nkhwazischool') . '</option>';
        
        foreach ($admin_contacts as $contact) {
            echo '<option value="' . esc_attr($contact['email']) . '">' . esc_html($contact['job_title']) . '</option>';
        }
        
        echo '</select>
            </div>
        </div>';
    } elseif (count($admin_contacts) == 1) {
        // If only one contact, use it as the default recipient
        echo '<input type="hidden" name="recipient" value="' . esc_attr($admin_contacts[0]['email']) . '">';
    }
    
    // Form fields
    echo '<div class="grid margin">
        <div class="s-12 m-6 l-7">
            <input name="name" class="required" placeholder="' . __('Your name *', 'nkhwazischool') . '" title="' . __('Your name (Required)', 'nkhwazischool') . '" type="text" required data-min-length="2" data-max-length="50" />
        </div>
        <div class="s-12 m-6 l-5">
            <input name="phone" placeholder="' . __('Your Phone Number', 'nkhwazischool') . '" title="' . __('Your Phone Number (Optional)', 'nkhwazischool') . '" type="text" data-min-length="0" data-max-length="20" />
        </div>
        <div class="s-12 m-12 l-8">
            <input name="email" class="required email" placeholder="' . __('Your e-mail *', 'nkhwazischool') . '" title="' . __('Your e-mail (Required)', 'nkhwazischool') . '" type="email" required />
        </div>
        
        <div class="s-12">
            <input name="subject" class="required" placeholder="' . __('Subject *', 'nkhwazischool') . '" title="' . __('Subject (Required)', 'nkhwazischool') . '" type="text" required data-min-length="3" data-max-length="100" />
        </div>
        <div class="s-12">
            <textarea name="message" class="required" placeholder="' . __('Your message *', 'nkhwazischool') . '" rows="6" required data-min-length="20" data-max-length="2000"></textarea>
        </div>
        
        <!-- reCAPTCHA -->
        <div class="s-12 m-12 l-12 text-center margin-bottom">
            <div class="g-recaptcha" data-sitekey="6Lf79rcaAAAAALdqWY74mw_n6DtTxR_C5AEL6cfL"></div>
        </div>
        
        <!--submit button -->
        <div class="s-12 m-6 l-6">
            <button type="submit" class="button rounded-btn submit-btn s-12 margin-bottom text-white background-blue">' . esc_html($atts['submit_text']) . '</button>
        </div>
    </div>';
    
    // End form
    echo '</form>';
    
    // Add inline styles
    echo '<style>
    /* Simple Contact Form Styles */
    .simple-contact-form input,
    .simple-contact-form textarea,
    .simple-contact-form select {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-bottom: 15px;
    }
    
    .simple-contact-form input:focus,
    .simple-contact-form textarea:focus,
    .simple-contact-form select:focus {
        border-color: #0073aa;
        outline: none;
    }
    
    .simple-contact-form button {
        cursor: pointer;
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        background-color: #0073aa;
        color: white;
        font-weight: bold;
    }
    
    .simple-contact-form button:hover {
        background-color: #005a87;
    }
    
    .simple-contact-form button:disabled {
        background-color: #cccccc;
        cursor: not-allowed;
    }
    
    .simple-contact-form button.loading {
        position: relative;
        padding-right: 40px;
    }
    
    .simple-contact-form button.loading:after {
        content: "";
        position: absolute;
        right: 10px;
        top: 50%;
        margin-top: -8px;
        width: 16px;
        height: 16px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-top-color: white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    #form-message {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 4px;
        text-align: center;
        font-weight: bold;
    }
    
    #form-message.success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    #form-message.error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    /* Center reCAPTCHA */
    .g-recaptcha {
        display: inline-block;
        margin-bottom: 20px;
    }
    
    @media screen and (max-width: 768px) {
        .g-recaptcha {
            transform: scale(0.85);
            transform-origin: 0 0;
        }
    }
    </style>';
    
    // Return the buffered content
    return ob_get_clean();
}
add_shortcode('nkhwazi_contact_form', 'nkhwazi_contact_form_shortcode');

/**
 * Get admin contacts from the database
 * 
 * @return array Array of admin contacts with job title and email
 */
function nkhwazi_get_admin_contacts() {
    $contacts = array();
    
    $args = array(
        'post_type' => 'admin_contact_info',
        'posts_per_page' => -1,
        'meta_key' => 'priority',
        'orderby' => 'meta_value_num',
        'order' => 'ASC', // Ascending order ensures lowest priority number comes first
    );
    
    $query = new WP_Query($args);
    
    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            $priority = get_field('priority');
            $job_title = get_field('job_title');
            $email = get_field('email');
            $phone = get_field('phone');
            $display_email = get_field('display');
            
            if (!empty($job_title) && (!empty($email) || !empty($phone))) {
                $contacts[] = array(
                    'priority' => $priority,
                    'job_title' => $job_title,
                    'email' => $email,
                    'phone' => $phone,
                    'display_email' => $display_email
                );
            }
        }
    }
    
    wp_reset_postdata();
    
    return $contacts;
}

/**
 * Get school address from the database
 * 
 * @return array|null Address information or null if not found
 */
function nkhwazi_get_school_address() {
    $args = array(
        'post_type' => 'address',
        'posts_per_page' => 1,
        'orderby' => 'date',
        'order' => 'DESC',
    );
    
    $query = new WP_Query($args);
    
    if ($query->have_posts()) {
        $query->the_post();
        
        $address = array(
            'line1' => get_field('address_line1'),
            'line2' => get_field('address_line2'),
            'phone1' => get_field('phone1'),
            'phone2' => get_field('phone2'),
            'email' => get_field('email'),
            'display_email' => get_field('display_email')
        );
        
        wp_reset_postdata();
        return $address;
    }
    
    wp_reset_postdata();
    return null;
}

/**
 * Handle contact form submission via AJAX
 */
function nkhwazi_submit_contact_form() {
    // Log the request for debugging
    error_log('Contact form submission received');
    error_log('POST data: ' . print_r($_POST, true));
    
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'nkhwazi_contact_form_nonce')) {
        error_log('Nonce verification failed');
        wp_send_json_error(array(
            'message' => __('Security check failed. Please refresh the page and try again.', 'nkhwazischool')
        ));
        wp_die();
    }
    
    // Verify reCAPTCHA
    if (!isset($_POST['g-recaptcha-response']) || empty($_POST['g-recaptcha-response'])) {
        error_log('reCAPTCHA response missing');
        wp_send_json_error(array(
            'message' => __('Please complete the reCAPTCHA verification.', 'nkhwazischool')
        ));
        wp_die();
    }
    
    // Verify with Google reCAPTCHA API
    $recaptcha_secret = '6Lf79rcaAAAAACnBRBWJW8M7XLGq4o-tPhvn7r-J';
    $recaptcha_response = $_POST['g-recaptcha-response'];
    
    error_log('Verifying reCAPTCHA response: ' . substr($recaptcha_response, 0, 20) . '...');
    
    $verify_response = wp_remote_post('https://www.google.com/recaptcha/api/siteverify', array(
        'body' => array(
            'secret' => $recaptcha_secret,
            'response' => $recaptcha_response,
            'remoteip' => $_SERVER['REMOTE_ADDR']
        )
    ));
    
    if (is_wp_error($verify_response)) {
        error_log('reCAPTCHA verification error: ' . $verify_response->get_error_message());
        wp_send_json_error(array(
            'message' => __('reCAPTCHA verification failed. Please try again.', 'nkhwazischool')
        ));
        wp_die();
    }
    
    $response_body = wp_remote_retrieve_body($verify_response);
    $response_data = json_decode($response_body, true);
    
    error_log('reCAPTCHA API response: ' . print_r($response_data, true));
    
    if (!isset($response_data['success']) || !$response_data['success']) {
        error_log('reCAPTCHA verification failed');
        wp_send_json_error(array(
            'message' => __('reCAPTCHA verification failed. Please try again.', 'nkhwazischool')
        ));
        wp_die();
    }
    
    error_log('reCAPTCHA verification successful');
    
    // Validate required fields
    $required_fields = array(
        'name' => __('Your Name', 'nkhwazischool'),
        'email' => __('Your Email', 'nkhwazischool'),
        'subject' => __('Subject', 'nkhwazischool'),
        'message' => __('Message', 'nkhwazischool')
    );
    
    $missing_fields = array();
    foreach ($required_fields as $field => $label) {
        if (empty($_POST[$field])) {
            $missing_fields[] = $label;
        }
    }
    
    if (!empty($missing_fields)) {
        wp_send_json_error(array(
            'message' => __('Please fill in all required fields.', 'nkhwazischool'),
            'fields' => $missing_fields
        ));
        wp_die();
    }
    
    // Validate email
    if (!is_email($_POST['email'])) {
        wp_send_json_error(array(
            'message' => __('Please enter a valid email address.', 'nkhwazischool')
        ));
        wp_die();
    }
    
    // Sanitize input
    $name = sanitize_text_field($_POST['name']);
    $email = sanitize_email($_POST['email']);
    $phone = isset($_POST['phone']) ? sanitize_text_field($_POST['phone']) : '';
    $subject = sanitize_text_field($_POST['subject']);
    $message = sanitize_textarea_field($_POST['message']);
    
    // Get recipient email
    $recipient_email = '';
    if (isset($_POST['recipient']) && !empty($_POST['recipient'])) {
        $recipient_email = sanitize_email($_POST['recipient']);
    } else {
        // If no recipient selected, get the first admin contact
        $contacts = nkhwazi_get_admin_contacts();
        if (!empty($contacts)) {
            $recipient_email = $contacts[0]['email'];
        } else {
            // Fallback to admin email
            $recipient_email = get_option('admin_email');
        }
    }
    
    // Create email headers
    $headers = array(
        'Content-Type: text/html; charset=UTF-8',
        'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>',
        'Reply-To: ' . $name . ' <' . $email . '>'
    );
    
    // Create email body
    $email_body = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Contact Form Submission</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; }
        .header { background-color: #0073aa; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; }
        .field { margin-bottom: 15px; }
        .field-label { font-weight: bold; }
        .footer { background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 12px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>New Contact Form Submission</h1>
    </div>
    <div class="content">
        <p>You have received a new message from your website contact form.</p>
        
        <div class="field">
            <div class="field-label">Name:</div>
            <div>' . esc_html($name) . '</div>
        </div>
        
        <div class="field">
            <div class="field-label">Email:</div>
            <div>' . esc_html($email) . '</div>
        </div>';
        
    if (!empty($phone)) {
        $email_body .= '
        <div class="field">
            <div class="field-label">Phone:</div>
            <div>' . esc_html($phone) . '</div>
        </div>';
    }
    
    $email_body .= '
        <div class="field">
            <div class="field-label">Subject:</div>
            <div>' . esc_html($subject) . '</div>
        </div>
        
        <div class="field">
            <div class="field-label">Message:</div>
            <div>' . nl2br(esc_html($message)) . '</div>
        </div>
    </div>
    <div class="footer">
        <p>This email was sent from the contact form on ' . get_bloginfo('name') . ' (' . site_url() . ')</p>
        <p>IP Address: ' . $_SERVER['REMOTE_ADDR'] . '</p>
    </div>
</body>
</html>';
    
    // Send email
    $mail_sent = wp_mail($recipient_email, 'Contact Form: ' . $subject, $email_body, $headers);
    
    if ($mail_sent) {
        wp_send_json_success(array(
            'message' => __('Thank you for your message. We will get back to you as soon as possible.', 'nkhwazischool')
        ));
    } else {
        wp_send_json_error(array(
            'message' => __('There was an error sending your message. Please try again later.', 'nkhwazischool')
        ));
    }
    
    wp_die();
}
add_action('wp_ajax_nkhwazi_submit_contact_form', 'nkhwazi_submit_contact_form');
add_action('wp_ajax_nopriv_nkhwazi_submit_contact_form', 'nkhwazi_submit_contact_form');