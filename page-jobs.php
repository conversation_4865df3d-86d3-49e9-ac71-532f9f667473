<?php
/**
 * Template Name: Job Listings
 * Description: A template for displaying all available job opportunities at the school.
 * Features a list of job postings with title, reference, and excerpt.
 * Page ID: 436
 * Author: <PERSON>
 */

get_header();

// Query for active jobs
$args = array(
    'post_type' => 'job',
    'posts_per_page' => -1,
    'meta_query' => array(
        array(
            'key' => 'job_status',
            'value' => 'active',
            'compare' => '='
        )
    ),
    'orderby' => 'date',
    'order' => 'DESC'
);
$job_query = new WP_Query($args);
?>

<!-- MAIN -->
<main role="main">
  <!-- Content -->
  <article>
    <div class="line">
      <header class="rounded-div section background-primary background-transparent text-center"
        data-image-src="<?php echo get_template_directory_uri(); ?>/assets/img/parallax-05.webp">
        <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">Career Opportunities</h1>
      </header>
    </div>

    <!-- Breadcrumb Navigation -->
    <?php get_template_part('includes/breadcrumb'); ?>

    <div class="section background-white">
      <div class="line">
        <div class="margin">
          <div class="s-12 m-12 l-12">
            <!-- Introduction -->
            <div class="margin-bottom-20">
               <?php 
                            while (have_posts()) :
                                the_post();
                                the_content();
                            endwhile;
                            ?>
            </div>
            
            <!-- Job Listings -->
            <div class="job-listings">
              <?php if (!$job_query->have_posts()): ?>
                <div class="job-empty-state margin-bottom-30">
                  <p class='any-error'>There are currently no job openings available. Please check back later for future opportunities.</p>
                </div>
              <?php else: ?>
                <?php while ($job_query->have_posts()): $job_query->the_post(); 
                  // Get job meta fields
                  $job_reference = get_field('job_reference');
                  $job_deadline = get_field('job_deadline');
                  $job_excerpt = get_field('job_excerpt');
                ?>
                  <div id="job-<?php the_ID(); ?>" class="job-card margin-bottom-30">
                    <div class="job-card-inner background-white padding">
                      <h3 class="text-primary margin-bottom-10"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
                      
                      <div class="job-meta margin-bottom-15">
                        <div class="grid">
                          <div class="s-12 m-6 l-6">
                            <p><strong>Job Reference:</strong> <?php echo esc_html($job_reference); ?></p>
                          </div>
                          <div class="s-12 m-6 l-6">
                            <p><strong>Application Deadline:</strong> <?php echo esc_html($job_deadline); ?></p>
                          </div>
                        </div>
                      </div>
                      
                      <div class="job-excerpt margin-bottom-15">
                        <p><?php echo esc_html($job_excerpt); ?></p>
                      </div>
                      
                      <div class="job-actions">
                        <a href="<?php the_permalink(); ?>" class="button rounded-btn background-primary text-white">View Details</a>
                      </div>
                    </div>
                  </div>
                <?php endwhile; ?>
              <?php 
                wp_reset_postdata(); 
              endif; ?>
            </div>
          </div>
        </div>
      </div>
    </div>
  </article>
</main>

<?php get_footer(); ?>