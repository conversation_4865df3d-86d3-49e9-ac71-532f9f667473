!function(t){"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof exports?t(require("jquery")):t(jQuery)}(function(t){var e=function(i,s){this.$element=t(i),this.options=t.extend({},e.DEFAULTS,this.dataOptions(),s),this.init()};e.DEFAULTS={from:0,to:0,speed:1e3,refreshInterval:100,decimals:0,formatter:function(t,e){return t.toFixed(e.decimals)},onUpdate:null,onComplete:null},e.prototype.init=function(){this.value=this.options.from,this.loops=Math.ceil(this.options.speed/this.options.refreshInterval),this.loopCount=0,this.increment=(this.options.to-this.options.from)/this.loops},e.prototype.dataOptions=function(){var t={from:this.$element.data("from"),to:this.$element.data("to"),speed:this.$element.data("speed"),refreshInterval:this.$element.data("refresh-interval"),decimals:this.$element.data("decimals")},e=Object.keys(t);for(var i in e){var s=e[i];void 0===t[s]&&delete t[s]}return t},e.prototype.update=function(){this.value+=this.increment,this.loopCount++,this.render(),"function"==typeof this.options.onUpdate&&this.options.onUpdate.call(this.$element,this.value),this.loopCount>=this.loops&&(clearInterval(this.interval),this.value=this.options.to,"function"==typeof this.options.onComplete&&this.options.onComplete.call(this.$element,this.value))},e.prototype.render=function(){var t=this.options.formatter.call(this.$element,this.value,this.options);this.$element.text(t)},e.prototype.restart=function(){this.stop(),this.init(),this.start()},e.prototype.start=function(){this.stop(),this.render(),this.interval=setInterval(this.update.bind(this),this.options.refreshInterval)},e.prototype.stop=function(){this.interval&&clearInterval(this.interval)},e.prototype.toggle=function(){this.interval?this.stop():this.start()},t.fn.countTo=function(i){return this.each(function(){var s=t(this),n=s.data("countTo"),o="object"==typeof i?i:{},a="string"==typeof i?i:"start";(!n||"object"==typeof i)&&(n&&n.stop(),s.data("countTo",n=new e(this,o))),n[a].call(n)})}}),function(t,e,i){"function"==typeof define&&define.amd?define(["jquery"],function(s){return i(s,t,e),s.mobile}):i(t.jQuery,t,e)}(this,document,function(t,e,i,s){var n,o;(function(t,e,i,s){function n(t){for(;t&&void 0!==t.originalEvent;)t=t.originalEvent;return t}function o(e){for(var i,s,n={};e;){for(s in i=t.data(e,y))i[s]&&(n[s]=n.hasVirtualBinding=!0);e=e.parentNode}return n}function a(){q=!0}function c(){q=!1}function r(){l(),T=setTimeout(function(){T=0,N=0,A.length=0,H=!1,a()},t.vmouse.resetTimerDuration)}function l(){T&&(clearTimeout(T),T=0)}function u(e,i,o){var a;return(o&&o[e]||!o&&function(e,i){for(var s;e;){if((s=t.data(e,y))&&(!i||s[i]))return e;e=e.parentNode}return null}(i.target,e))&&(a=function(e,i){var o,a,c,r,l,u,h,d,p,f=e.type;if((e=t.Event(e)).type=i,o=e.originalEvent,a=t.event.props,f.search(/^(mouse|click)/)>-1&&(a=k),o)for(h=a.length;h;)e[r=a[--h]]=o[r];if(f.search(/mouse(down|up)|click/)>-1&&!e.which&&(e.which=1),-1!==f.search(/^touch/)&&(f=(c=n(o)).touches,l=c.changedTouches,u=f&&f.length?f[0]:l&&l.length?l[0]:s))for(d=0,p=D.length;d<p;d++)e[r=D[d]]=u[r];return e}(i,e),t(i.target).trigger(a)),a}function h(e){var i,s=t.data(e.target,x);!H&&(!N||N!==s)&&((i=u("v"+e.type,e))&&(i.isDefaultPrevented()&&e.preventDefault(),i.isPropagationStopped()&&e.stopPropagation(),i.isImmediatePropagationStopped()&&e.stopImmediatePropagation()))}function d(e){var i,s,a,r=n(e).touches;r&&1===r.length&&((s=o(i=e.target)).hasVirtualBinding&&(N=M++,t.data(i,x,N),l(),c(),C=!1,a=n(e).touches[0],O=a.pageX,S=a.pageY,u("vmouseover",e,s),u("vmousedown",e,s)))}function p(t){q||(C||u("vmousecancel",t,o(t.target)),C=!0,r())}function f(e){if(!q){var i=n(e).touches[0],s=C,a=t.vmouse.moveDistanceThreshold,c=o(e.target);(C=C||Math.abs(i.pageX-O)>a||Math.abs(i.pageY-S)>a)&&!s&&u("vmousecancel",e,c),u("vmousemove",e,c),r()}}function b(t){if(!q){a();var e,i,s=o(t.target);u("vmouseup",t,s),C||(e=u("vclick",t,s))&&e.isDefaultPrevented()&&(i=n(t).changedTouches[0],A.push({touchID:N,x:i.clientX,y:i.clientY}),H=!0),u("vmouseout",t,s),C=!1,r()}}function g(e){var i,s=t.data(e,y);if(s)for(i in s)if(s[i])return!0;return!1}function v(){}function m(e){var i=e.substr(1);return{setup:function(){g(this)||t.data(this,y,{}),t.data(this,y)[e]=!0,I[e]=(I[e]||0)+1,1===I[e]&&E.bind(i,h),t(this).bind(i,v),W&&(I.touchstart=(I.touchstart||0)+1,1===I.touchstart&&E.bind("touchstart",d).bind("touchend",b).bind("touchmove",f).bind("scroll",p))},teardown:function(){--I[e],I[e]||E.unbind(i,h),W&&(--I.touchstart,I.touchstart||E.unbind("touchstart",d).unbind("touchmove",f).unbind("touchend",b).unbind("scroll",p));var s=t(this),n=t.data(this,y);n&&(n[e]=!1),s.unbind(i,v),g(this)||s.removeData(y)}}}var j,w,y="virtualMouseBindings",x="virtualTouchID",_="vmouseover vmousedown vmousemove vmouseup vclick vmouseout vmousecancel".split(" "),D="clientX clientY pageX pageY screenX screenY".split(" "),P=t.event.mouseHooks?t.event.mouseHooks.props:[],k=t.event.props.concat(P),I={},T=0,O=0,S=0,C=!1,A=[],H=!1,q=!1,W="addEventListener"in i,E=t(i),M=1,N=0;for(t.vmouse={moveDistanceThreshold:10,clickDistanceThreshold:10,resetTimerDuration:1500},w=0;w<_.length;w++)t.event.special[_[w]]=m(_[w]);W&&i.addEventListener("click",function(e){var i,s,n,o,a,c=A.length,r=e.target;if(c)for(i=e.clientX,s=e.clientY,j=t.vmouse.clickDistanceThreshold,n=r;n;){for(o=0;o<c;o++)if(a=A[o],0,n===r&&Math.abs(a.x-i)<j&&Math.abs(a.y-s)<j||t.data(n,x)===a.touchID)return e.preventDefault(),void e.stopPropagation();n=n.parentNode}},!0)})(t,0,i),t.mobile={},o={touch:"ontouchend"in i},(n=t).mobile.support=n.mobile.support||{},n.extend(n.support,o),n.extend(n.mobile.support,o),function(t,e,s){function n(e,i,n,o){var a=n.type;n.type=i,o?t.event.trigger(n,s,e):t.event.dispatch.call(e,n),n.type=a}var o=t(i),a=t.mobile.support.touch,c="touchmove scroll",r=a?"touchstart":"mousedown",l=a?"touchend":"mouseup",u=a?"touchmove":"mousemove";t.each("touchstart touchmove touchend tap taphold swipe swipeleft swiperight scrollstart scrollstop".split(" "),function(e,i){t.fn[i]=function(t){return t?this.bind(i,t):this.trigger(i)},t.attrFn&&(t.attrFn[i]=!0)}),t.event.special.scrollstart={enabled:!0,setup:function(){function e(t,e){n(o,(i=e)?"scrollstart":"scrollstop",t)}var i,s,o=this;t(o).bind(c,function(n){t.event.special.scrollstart.enabled&&(i||e(n,!0),clearTimeout(s),s=setTimeout(function(){e(n,!1)},50))})},teardown:function(){t(this).unbind(c)}},t.event.special.tap={tapholdThreshold:750,emitTapOnTaphold:!0,setup:function(){var e=this,i=t(e),s=!1;i.bind("vmousedown",function(a){function c(){clearTimeout(u)}function r(){c(),i.unbind("vclick",l).unbind("vmouseup",c),o.unbind("vmousecancel",r)}function l(t){r(),s||h!==t.target?s&&t.preventDefault():n(e,"tap",t)}if(s=!1,a.which&&1!==a.which)return!1;var u,h=a.target;i.bind("vmouseup",c).bind("vclick",l),o.bind("vmousecancel",r),u=setTimeout(function(){t.event.special.tap.emitTapOnTaphold||(s=!0),n(e,"taphold",t.Event("taphold",{target:h}))},t.event.special.tap.tapholdThreshold)})},teardown:function(){t(this).unbind("vmousedown").unbind("vclick").unbind("vmouseup"),o.unbind("vmousecancel")}},t.event.special.swipe={scrollSupressionThreshold:30,durationThreshold:1e3,horizontalDistanceThreshold:30,verticalDistanceThreshold:30,getLocation:function(t){var i=e.pageXOffset,s=e.pageYOffset,n=t.clientX,o=t.clientY;return 0===t.pageY&&Math.floor(o)>Math.floor(t.pageY)||0===t.pageX&&Math.floor(n)>Math.floor(t.pageX)?(n-=i,o-=s):(o<t.pageY-s||n<t.pageX-i)&&(n=t.pageX-i,o=t.pageY-s),{x:n,y:o}},start:function(e){var i=e.originalEvent.touches?e.originalEvent.touches[0]:e,s=t.event.special.swipe.getLocation(i);return{time:(new Date).getTime(),coords:[s.x,s.y],origin:t(e.target)}},stop:function(e){var i=e.originalEvent.touches?e.originalEvent.touches[0]:e,s=t.event.special.swipe.getLocation(i);return{time:(new Date).getTime(),coords:[s.x,s.y]}},handleSwipe:function(e,i,s,o){if(i.time-e.time<t.event.special.swipe.durationThreshold&&Math.abs(e.coords[0]-i.coords[0])>t.event.special.swipe.horizontalDistanceThreshold&&Math.abs(e.coords[1]-i.coords[1])<t.event.special.swipe.verticalDistanceThreshold){var a=e.coords[0]>i.coords[0]?"swipeleft":"swiperight";return n(s,"swipe",t.Event("swipe",{target:o,swipestart:e,swipestop:i}),!0),n(s,a,t.Event(a,{target:o,swipestart:e,swipestop:i}),!0),!0}return!1},eventInProgress:!1,setup:function(){var e,i=this,s=t(i),n={};(e=t.data(this,"mobile-events"))||(e={length:0},t.data(this,"mobile-events",e)),e.length++,e.swipe=n,n.start=function(e){if(!t.event.special.swipe.eventInProgress){t.event.special.swipe.eventInProgress=!0;var s,a=t.event.special.swipe.start(e),c=e.target,r=!1;n.move=function(e){a&&!e.isDefaultPrevented()&&(s=t.event.special.swipe.stop(e),r||(r=t.event.special.swipe.handleSwipe(a,s,i,c))&&(t.event.special.swipe.eventInProgress=!1),Math.abs(a.coords[0]-s.coords[0])>t.event.special.swipe.scrollSupressionThreshold&&e.preventDefault())},n.stop=function(){r=!0,t.event.special.swipe.eventInProgress=!1,o.off(u,n.move),n.move=null},o.on(u,n.move).one(l,n.stop)}},s.on(r,n.start)},teardown:function(){var e,i;(e=t.data(this,"mobile-events"))&&(i=e.swipe,delete e.swipe,e.length--,0===e.length&&t.removeData(this,"mobile-events")),i&&(i.start&&t(this).off(r,i.start),i.move&&o.off(u,i.move),i.stop&&o.off(l,i.stop))}},t.each({scrollstop:"scrollstart",taphold:"tap",swipeleft:"swipe.left",swiperight:"swipe.right"},function(e,i){t.event.special[e]={setup:function(){t(this).bind(i,t.noop)},teardown:function(){t(this).unbind(i)}}})}(t,this)}),function(t){"use strict";var e={cache:{},support:{},objects:{},init:function(e){return this.each(function(){t(this).unbind("click.lightcase").bind("click.lightcase",function(i){i.preventDefault(),t(this).lightcase("start",e)})})},start:function(i){e.origin=lightcase.origin=this,e.settings=lightcase.settings=t.extend(!0,{idPrefix:"lightcase-",classPrefix:"lightcase-",attrPrefix:"lc-",transition:"elastic",transitionIn:null,transitionOut:null,cssTransitions:!0,speedIn:250,speedOut:250,maxWidth:800,maxHeight:500,forceWidth:!1,forceHeight:!1,liveResize:!0,fullScreenModeForMobile:!0,mobileMatchExpression:/(iphone|ipod|ipad|android|blackberry|symbian)/,disableShrink:!1,shrinkFactor:.75,overlayOpacity:.9,slideshow:!1,timeout:5e3,swipe:!0,useKeys:!0,useCategories:!0,navigateEndless:!0,closeOnOverlayClick:!0,title:null,caption:null,showTitle:!0,showCaption:!0,showSequenceInfo:!0,inline:{width:"auto",height:"auto"},ajax:{width:"auto",height:"auto",type:"get",dataType:"html",data:{}},iframe:{width:800,height:500,frameborder:0},flash:{width:400,height:205,wmode:"transparent"},video:{width:400,height:225,poster:"",preload:"auto",controls:!0,autobuffer:!0,autoplay:!0,loop:!1},attr:"data-rel",href:null,type:null,typeMapping:{image:"jpg,jpeg,gif,png,bmp",flash:"swf",video:"mp4,mov,ogv,ogg,webm",iframe:"html,php",ajax:"json,txt",inline:"#"},errorMessage:function(){return'<p class="'+e.settings.classPrefix+'error">'+e.settings.labels.errorMessage+"</p>"},labels:{errorMessage:"Source could not be found...","sequenceInfo.of":" of ",close:"Close","navigator.prev":"Prev","navigator.next":"Next","navigator.play":"Play","navigator.pause":"Pause"},markup:function(){t("body").append(e.objects.overlay=t('<div id="'+e.settings.idPrefix+'overlay"></div>'),e.objects.loading=t('<div id="'+e.settings.idPrefix+'loading" class="'+e.settings.classPrefix+'icon-spin"></div>'),e.objects.case=t('<div id="'+e.settings.idPrefix+'case" aria-hidden="true" role="dialog"></div>')),e.objects.case.after(e.objects.nav=t('<div id="'+e.settings.idPrefix+'nav"></div>')),e.objects.nav.append(e.objects.close=t('<a href="#" class="'+e.settings.classPrefix+'icon-close"><span>'+e.settings.labels.close+"</span></a>"),e.objects.prev=t('<a href="#" class="'+e.settings.classPrefix+'icon-prev"><span>'+e.settings.labels["navigator.prev"]+"</span></a>").hide(),e.objects.next=t('<a href="#" class="'+e.settings.classPrefix+'icon-next"><span>'+e.settings.labels["navigator.next"]+"</span></a>").hide(),e.objects.play=t('<a href="#" class="'+e.settings.classPrefix+'icon-play"><span>'+e.settings.labels["navigator.play"]+"</span></a>").hide(),e.objects.pause=t('<a href="#" class="'+e.settings.classPrefix+'icon-pause"><span>'+e.settings.labels["navigator.pause"]+"</span></a>").hide()),e.objects.case.append(e.objects.content=t('<div id="'+e.settings.idPrefix+'content"></div>'),e.objects.info=t('<div id="'+e.settings.idPrefix+'info"></div>')),e.objects.content.append(e.objects.contentInner=t('<div class="'+e.settings.classPrefix+'contentInner"></div>')),e.objects.info.append(e.objects.sequenceInfo=t('<div id="'+e.settings.idPrefix+'sequenceInfo"></div>'),e.objects.title=t('<h4 id="'+e.settings.idPrefix+'title"></h4>'),e.objects.caption=t('<p id="'+e.settings.idPrefix+'caption"></p>'))},onInit:{},onStart:{},onFinish:{},onClose:{},onCleanup:{}},i),e._callHooks(e.settings.onInit),e.objectData=e._setObjectData(this),e._cacheScrollPosition(),e._watchScrollInteraction(),e._addElements(),e._open(),e.dimensions=e.getViewportDimensions()},get:function(t){return e.objects[t]},getObjectData:function(){return e.objectData},_setObjectData:function(i){var s=t(i),n={title:e.settings.title||s.attr(e._prefixAttributeName("title"))||s.attr("title"),caption:e.settings.caption||s.attr(e._prefixAttributeName("caption"))||s.children("img").attr("alt"),url:e._determineUrl(),requestType:e.settings.ajax.type,requestData:e.settings.ajax.data,requestDataType:e.settings.ajax.dataType,rel:s.attr(e._determineAttributeSelector()),type:e.settings.type||e._verifyDataType(e._determineUrl()),isPartOfSequence:e._isPartOfSequence(s.attr(e.settings.attr),":"),isPartOfSequenceWithSlideshow:e._isPartOfSequence(s.attr(e.settings.attr),":slideshow"),currentIndex:t(e._determineAttributeSelector()).index(s),sequenceLength:t(e._determineAttributeSelector()).length};return n.sequenceInfo=n.currentIndex+1+e.settings.labels["sequenceInfo.of"]+n.sequenceLength,n.prevIndex=n.currentIndex-1,n.nextIndex=n.currentIndex+1,n},_prefixAttributeName:function(t){return"data-"+e.settings.attrPrefix+t},_determineLinkTarget:function(){return e.settings.href||t(e.origin).attr(e._prefixAttributeName("href"))||t(e.origin).attr("href")},_determineAttributeSelector:function(){var i=t(e.origin),s="";if(void 0!==e.cache.selector)s=e.cache.selector;else if(!0===e.settings.useCategories&&i.attr(e._prefixAttributeName("categories"))){var n=i.attr(e._prefixAttributeName("categories")).split(" ");t.each(n,function(t,i){t>0&&(s+=","),s+="["+e._prefixAttributeName("categories")+'~="'+i+'"]'})}else s="["+e.settings.attr+'="'+i.attr(e.settings.attr)+'"]';return e.cache.selector=s,s},_determineUrl:function(){var i,s=e._verifyDataUrl(e._determineLinkTarget()),n=0,o=0;return t.each(s,function(t,s){e._devicePixelRatio()>=s.density&&s.density>=o&&e._matchMedia()("screen and (min-width:"+s.width+"px)")&&s.width>=n&&(n=s.width,o=s.density,i=s.url)}),i},_normalizeUrl:function(t){var e=/^\d+$/;return t.split(",").map(function(t){var i={width:0,density:0};return t.trim().split(/\s+/).forEach(function(t,s){if(0===s)return i.url=t;var n=t.substring(0,t.length-1),o=t[t.length-1],a=parseInt(n,10),c=parseFloat(n);"w"===o&&e.test(n)?i.width=a:"h"===o&&e.test(n)?i.height=a:"x"!==o||isNaN(c)||(i.density=c)}),i})},_isPartOfSequence:function(i,s){var n=t("["+e.settings.attr+'="'+i+'"]');return new RegExp(s).test(i)&&n.length>1},isSlideshowEnabled:function(){return e.objectData.isPartOfSequence&&(!0===e.settings.slideshow||!0===e.objectData.isPartOfSequenceWithSlideshow)},_loadContent:function(){e.cache.originalObject&&e._restoreObject(),e._createObject()},_createObject:function(){var i;switch(e.objectData.type){case"image":(i=t(new Image)).attr({src:e.objectData.url,alt:e.objectData.title});break;case"inline":(i=t('<div class="'+e.settings.classPrefix+'inlineWrap"></div>')).html(e._cloneObject(t(e.objectData.url))),t.each(e.settings.inline,function(t,s){i.attr(e._prefixAttributeName(t),s)});break;case"ajax":i=t('<div class="'+e.settings.classPrefix+'inlineWrap"></div>'),t.each(e.settings.ajax,function(t,s){"data"!==t&&i.attr(e._prefixAttributeName(t),s)});break;case"flash":i=t('<embed src="'+e.objectData.url+'" type="application/x-shockwave-flash"></embed>'),t.each(e.settings.flash,function(t,e){i.attr(t,e)});break;case"video":(i=t("<video></video>")).attr("src",e.objectData.url),t.each(e.settings.video,function(t,e){i.attr(t,e)});break;default:(i=t("<iframe></iframe>")).attr({src:e.objectData.url}),t.each(e.settings.iframe,function(t,e){i.attr(t,e)})}e._addObject(i),e._loadObject(i)},_addObject:function(t){e.objects.contentInner.html(t),e._loading("start"),e._callHooks(e.settings.onStart),!0===e.settings.showSequenceInfo&&e.objectData.isPartOfSequence?(e.objects.sequenceInfo.html(e.objectData.sequenceInfo),e.objects.sequenceInfo.show()):(e.objects.sequenceInfo.empty(),e.objects.sequenceInfo.hide()),!0===e.settings.showTitle&&void 0!==e.objectData.title&&""!==e.objectData.title?(e.objects.title.html(e.objectData.title),e.objects.title.show()):(e.objects.title.empty(),e.objects.title.hide()),!0===e.settings.showCaption&&void 0!==e.objectData.caption&&""!==e.objectData.caption?(e.objects.caption.html(e.objectData.caption),e.objects.caption.show()):(e.objects.caption.empty(),e.objects.caption.hide())},_loadObject:function(i){switch(e.objectData.type){case"inline":t(e.objectData.url)?e._showContent(i):e.error();break;case"ajax":t.ajax(t.extend({},e.settings.ajax,{url:e.objectData.url,type:e.objectData.requestType,dataType:e.objectData.requestDataType,data:e.objectData.requestData,success:function(t,s,n){"json"===e.objectData.requestDataType?e.objectData.data=t:i.html(t),e._showContent(i)},error:function(t,i,s){e.error()}}));break;case"flash":e._showContent(i);break;case"video":"function"==typeof i.get(0).canPlayType||0===e.objects.case.find("video").length?e._showContent(i):e.error();break;default:e.objectData.url?(i.load(function(){e._showContent(i)}),i.error(function(){e.error()})):e.error()}},error:function(){e.objectData.type="error";var i=t('<div class="'+e.settings.classPrefix+'inlineWrap"></div>');i.html(e.settings.errorMessage),e.objects.contentInner.html(i),e._showContent(e.objects.contentInner)},_calculateDimensions:function(t){e._cleanupDimensions();var i={objectWidth:t.attr("width")?t.attr("width"):t.attr(e._prefixAttributeName("width")),objectHeight:t.attr("height")?t.attr("height"):t.attr(e._prefixAttributeName("height"))};if(!e.settings.disableShrink)switch(i.maxWidth=parseInt(e.dimensions.windowWidth*e.settings.shrinkFactor),i.maxHeight=parseInt(e.dimensions.windowHeight*e.settings.shrinkFactor),i.maxWidth>e.settings.maxWidth&&(i.maxWidth=e.settings.maxWidth),i.maxHeight>e.settings.maxHeight&&(i.maxHeight=e.settings.maxHeight),i.differenceWidthAsPercent=parseInt(100/i.maxWidth*i.objectWidth),i.differenceHeightAsPercent=parseInt(100/i.maxHeight*i.objectHeight),e.objectData.type){case"image":case"flash":case"video":i.differenceWidthAsPercent>100&&i.differenceWidthAsPercent>i.differenceHeightAsPercent&&(i.objectWidth=i.maxWidth,i.objectHeight=parseInt(i.objectHeight/i.differenceWidthAsPercent*100)),i.differenceHeightAsPercent>100&&i.differenceHeightAsPercent>i.differenceWidthAsPercent&&(i.objectWidth=parseInt(i.objectWidth/i.differenceHeightAsPercent*100),i.objectHeight=i.maxHeight),i.differenceHeightAsPercent>100&&i.differenceWidthAsPercent<i.differenceHeightAsPercent&&(i.objectWidth=parseInt(i.maxWidth/i.differenceHeightAsPercent*i.differenceWidthAsPercent),i.objectHeight=i.maxHeight);break;case"error":!isNaN(i.objectWidth)&&i.objectWidth>i.maxWidth&&(i.objectWidth=i.maxWidth);break;default:(isNaN(i.objectWidth)||i.objectWidth>i.maxWidth)&&!e.settings.forceWidth&&(i.objectWidth=i.maxWidth),(isNaN(i.objectHeight)&&"auto"!==i.objectHeight||i.objectHeight>i.maxHeight)&&!e.settings.forceHeight&&(i.objectHeight=i.maxHeight)}e.settings.forceWidth?i.maxWidth=i.objectWidth:t.attr(e._prefixAttributeName("max-width"))&&(i.maxWidth=t.attr(e._prefixAttributeName("max-width"))),e.settings.forceHeight?i.maxHeight=i.objectHeight:t.attr(e._prefixAttributeName("max-height"))&&(i.maxHeight=t.attr(e._prefixAttributeName("max-height"))),e._adjustDimensions(t,i)},_adjustDimensions:function(t,i){t.css({width:i.objectWidth,height:i.objectHeight,"max-width":i.maxWidth,"max-height":i.maxHeight}),e.objects.contentInner.css({width:t.outerWidth(),height:t.outerHeight(),"max-width":"100%"}),e.objects.case.css({width:e.objects.contentInner.outerWidth()}),e.objects.case.css({"margin-top":parseInt(-e.objects.case.outerHeight()/2),"margin-left":parseInt(-e.objects.case.outerWidth()/2)})},_loading:function(t){"start"===t?(e.objects.case.addClass(e.settings.classPrefix+"loading"),e.objects.loading.show()):"end"===t&&(e.objects.case.removeClass(e.settings.classPrefix+"loading"),e.objects.loading.hide())},getViewportDimensions:function(){return{windowWidth:t(window).innerWidth(),windowHeight:t(window).innerHeight()}},_verifyDataUrl:function(t){return!(!t||void 0===t||""===t)&&(t.indexOf("#")>-1&&(t="#"+(t=t.split("#"))[t.length-1]),e._normalizeUrl(t.toString()))},_verifyDataType:function(t){var i=e.settings.typeMapping;if(!t)return!1;for(var s in i)if(i.hasOwnProperty(s))for(var n=i[s].split(","),o=0;o<n.length;o++){var a=n[o].toLowerCase(),c=new RegExp(".("+a+")$","i"),r=t.toLowerCase().split("?")[0].substr(-5);if(!0===c.test(r)||"inline"===s&&t.indexOf(a)>-1)return s}return"iframe"},_addElements:function(){void 0!==e.objects.case&&t("#"+e.objects.case.attr("id")).length||e.settings.markup()},_showContent:function(t){switch(e.objects.case.attr(e._prefixAttributeName("type"),e.objectData.type),e.cache.object=t,e._calculateDimensions(t),e._callHooks(e.settings.onFinish),e.settings.transitionIn){case"scrollTop":case"scrollRight":case"scrollBottom":case"scrollLeft":case"scrollHorizontal":case"scrollVertical":e.transition.scroll(e.objects.case,"in",e.settings.speedIn),e.transition.fade(e.objects.contentInner,"in",e.settings.speedIn);break;case"elastic":e.objects.case.css("opacity")<1&&(e.transition.zoom(e.objects.case,"in",e.settings.speedIn),e.transition.fade(e.objects.contentInner,"in",e.settings.speedIn));case"fade":case"fadeInline":e.transition.fade(e.objects.case,"in",e.settings.speedIn),e.transition.fade(e.objects.contentInner,"in",e.settings.speedIn);break;default:e.transition.fade(e.objects.case,"in",0)}e._loading("end"),e.isBusy=!1},_processContent:function(){switch(e.isBusy=!0,e.settings.transitionOut){case"scrollTop":case"scrollRight":case"scrollBottom":case"scrollLeft":case"scrollVertical":case"scrollHorizontal":e.objects.case.is(":hidden")?(e.transition.fade(e.objects.case,"out",0,0,function(){e._loadContent()}),e.transition.fade(e.objects.contentInner,"out",0)):e.transition.scroll(e.objects.case,"out",e.settings.speedOut,function(){e._loadContent()});break;case"fade":e.objects.case.is(":hidden")?e.transition.fade(e.objects.case,"out",0,0,function(){e._loadContent()}):e.transition.fade(e.objects.case,"out",e.settings.speedOut,0,function(){e._loadContent()});break;case"fadeInline":case"elastic":e.objects.case.is(":hidden")?e.transition.fade(e.objects.case,"out",0,0,function(){e._loadContent()}):e.transition.fade(e.objects.contentInner,"out",e.settings.speedOut,0,function(){e._loadContent()});break;default:e.transition.fade(e.objects.case,"out",0,0,function(){e._loadContent()})}},_handleEvents:function(){e._unbindEvents(),e.objects.nav.children().not(e.objects.close).hide(),e.isSlideshowEnabled()&&(e.objects.nav.hasClass(e.settings.classPrefix+"paused")?e._stopTimeout():e._startTimeout()),e.settings.liveResize&&e._watchResizeInteraction(),e.objects.close.click(function(t){t.preventDefault(),e.close()}),!0===e.settings.closeOnOverlayClick&&e.objects.overlay.css("cursor","pointer").click(function(t){t.preventDefault(),e.close()}),!0===e.settings.useKeys&&e._addKeyEvents(),e.objectData.isPartOfSequence&&(e.objects.nav.attr(e._prefixAttributeName("ispartofsequence"),!0),e.objects.nav.data("items",e._setNavigation()),e.objects.prev.click(function(t){t.preventDefault(),!0!==e.settings.navigateEndless&&e.item.isFirst()||(e.objects.prev.unbind("click"),e.cache.action="prev",e.objects.nav.data("items").prev.click(),e.isSlideshowEnabled()&&e._stopTimeout())}),e.objects.next.click(function(t){t.preventDefault(),!0!==e.settings.navigateEndless&&e.item.isLast()||(e.objects.next.unbind("click"),e.cache.action="next",e.objects.nav.data("items").next.click(),e.isSlideshowEnabled()&&e._stopTimeout())}),e.isSlideshowEnabled()&&(e.objects.play.click(function(t){t.preventDefault(),e._startTimeout()}),e.objects.pause.click(function(t){t.preventDefault(),e._stopTimeout()})),!0===e.settings.swipe&&(t.isPlainObject(t.event.special.swipeleft)&&e.objects.case.on("swipeleft",function(t){t.preventDefault(),e.objects.next.click(),e.isSlideshowEnabled()&&e._stopTimeout()}),t.isPlainObject(t.event.special.swiperight)&&e.objects.case.on("swiperight",function(t){t.preventDefault(),e.objects.prev.click(),e.isSlideshowEnabled()&&e._stopTimeout()})))},_addKeyEvents:function(){t(document).bind("keyup.lightcase",function(t){if(!e.isBusy)switch(t.keyCode){case 27:e.objects.close.click();break;case 37:e.objectData.isPartOfSequence&&e.objects.prev.click();break;case 39:e.objectData.isPartOfSequence&&e.objects.next.click()}})},_startTimeout:function(){e.objects.play.hide(),e.objects.pause.show(),e.cache.action="next",e.objects.nav.removeClass(e.settings.classPrefix+"paused"),e.timeout=setTimeout(function(){e.objects.nav.data("items").next.click()},e.settings.timeout)},_stopTimeout:function(){e.objects.play.show(),e.objects.pause.hide(),e.objects.nav.addClass(e.settings.classPrefix+"paused"),clearTimeout(e.timeout)},_setNavigation:function(){var i=t(e.cache.selector||e.settings.attr),s=e.objectData.sequenceLength-1,n={prev:i.eq(e.objectData.prevIndex),next:i.eq(e.objectData.nextIndex)};return e.objectData.currentIndex>0?e.objects.prev.show():n.prevItem=i.eq(s),e.objectData.nextIndex<=s?e.objects.next.show():n.next=i.eq(0),!0===e.settings.navigateEndless&&(e.objects.prev.show(),e.objects.next.show()),n},item:{isFirst:function(){return 0===e.objectData.currentIndex},isLast:function(){return e.objectData.currentIndex===e.objectData.sequenceLength-1}},_cloneObject:function(t){var i=t.clone(),s=t.attr("id");return t.is(":hidden")?(e._cacheObjectData(t),t.attr("id",e.settings.idPrefix+"temp-"+s).empty()):i.removeAttr("id"),i.show()},isMobileDevice:function(){return!!navigator.userAgent.toLowerCase().match(e.settings.mobileMatchExpression)},isTransitionSupported:function(){var i=t("body").get(0),s=!1,n={transition:"",WebkitTransition:"-webkit-",MozTransition:"-moz-",OTransition:"-o-",MsTransition:"-ms-"};for(var o in n)n.hasOwnProperty(o)&&o in i.style&&(e.support.transition=n[o],s=!0);return s},transition:{fade:function(t,i,s,n,o){var a="in"===i,c={},r=t.css("opacity"),l={},u=n||(a?1:0);!e.isOpen&&a||(c.opacity=r,l.opacity=u,t.css(c).show(),e.support.transitions?(l[e.support.transition+"transition"]=s+"ms ease",setTimeout(function(){t.css(l),setTimeout(function(){t.css(e.support.transition+"transition",""),!o||!e.isOpen&&a||o()},s)},15)):(t.stop(),t.animate(l,s,o)))},scroll:function(t,i,s,n){var o="in"===i,a=o?e.settings.transitionIn:e.settings.transitionOut,c="left",r={},l=o?0:1,u=o?"-50%":"50%",h={},d=o?1:0,p=o?"50%":"-50%";if(e.isOpen||!o){switch(a){case"scrollTop":c="top";break;case"scrollRight":u=o?"150%":"50%",p=o?"50%":"150%";break;case"scrollBottom":c="top",u=o?"150%":"50%",p=o?"50%":"150%";break;case"scrollHorizontal":u=o?"150%":"50%",p=o?"50%":"-50%";break;case"scrollVertical":c="top",u=o?"-50%":"50%",p=o?"50%":"150%"}if("prev"===e.cache.action)switch(a){case"scrollHorizontal":u=o?"-50%":"50%",p=o?"50%":"150%";break;case"scrollVertical":u=o?"150%":"50%",p=o?"50%":"-50%"}r.opacity=l,r[c]=u,h.opacity=d,h[c]=p,t.css(r).show(),e.support.transitions?(h[e.support.transition+"transition"]=s+"ms ease",setTimeout(function(){t.css(h),setTimeout(function(){t.css(e.support.transition+"transition",""),!n||!e.isOpen&&o||n()},s)},15)):(t.stop(),t.animate(h,s,n))}},zoom:function(t,i,s,n){var o="in"===i,a={},c=t.css("opacity"),r=o?"scale(0.75)":"scale(1)",l={},u=o?1:0,h=o?"scale(1)":"scale(0.75)";!e.isOpen&&o||(a.opacity=c,a[e.support.transition+"transform"]=r,l.opacity=u,t.css(a).show(),e.support.transitions?(l[e.support.transition+"transform"]=h,l[e.support.transition+"transition"]=s+"ms ease",setTimeout(function(){t.css(l),setTimeout(function(){t.css(e.support.transition+"transform",""),t.css(e.support.transition+"transition",""),!n||!e.isOpen&&o||n()},s)},15)):(t.stop(),t.animate(l,s,n)))}},_callHooks:function(i){"object"==typeof i&&t.each(i,function(t,i){"function"==typeof i&&i.call(e.origin)})},_cacheObjectData:function(i){t.data(i,"cache",{id:i.attr("id"),content:i.html()}),e.cache.originalObject=i},_restoreObject:function(){var i=t('[id^="'+e.settings.idPrefix+'temp-"]');i.attr("id",t.data(e.cache.originalObject,"cache").id),i.html(t.data(e.cache.originalObject,"cache").content)},resize:function(){e.isOpen&&(e.isSlideshowEnabled()&&e._stopTimeout(),e.dimensions=e.getViewportDimensions(),e._calculateDimensions(e.cache.object))},_cacheScrollPosition:function(){var i=t(window),s=t(document),n={top:i.scrollTop(),left:i.scrollLeft()};e.cache.scrollPosition=e.cache.scrollPosition||{},s.width()>i.width()&&(e.cache.scrollPosition.left=n.left),s.height()>i.height()&&(e.cache.scrollPosition.top=n.top)},_watchResizeInteraction:function(){t(window).resize(e.resize)},_unwatchResizeInteraction:function(){t(window).off("resize",e.resize)},_watchScrollInteraction:function(){t(window).scroll(e._cacheScrollPosition)},_unwatchScrollInteraction:function(){t(window).off("scroll",e._cacheScrollPosition)},_restoreScrollPosition:function(){t(window).scrollTop(parseInt(e.cache.scrollPosition.top)).scrollLeft(parseInt(e.cache.scrollPosition.left)).resize()},_switchToFullScreenMode:function(){e.settings.shrinkFactor=1,e.settings.overlayOpacity=1,t("html").addClass(e.settings.classPrefix+"fullScreenMode")},_open:function(){switch(e.isOpen=!0,e.support.transitions=!!e.settings.cssTransitions&&e.isTransitionSupported(),e.support.mobileDevice=e.isMobileDevice(),e.support.mobileDevice&&(t("html").addClass(e.settings.classPrefix+"isMobileDevice"),e.settings.fullScreenModeForMobile&&e._switchToFullScreenMode()),e.settings.transitionIn||(e.settings.transitionIn=e.settings.transition),e.settings.transitionOut||(e.settings.transitionOut=e.settings.transition),e.settings.transitionIn){case"fade":case"fadeInline":case"elastic":case"scrollTop":case"scrollRight":case"scrollBottom":case"scrollLeft":case"scrollVertical":case"scrollHorizontal":e.objects.case.is(":hidden")&&(e.objects.close.css("opacity",0),e.objects.overlay.css("opacity",0),e.objects.case.css("opacity",0),e.objects.contentInner.css("opacity",0)),e.transition.fade(e.objects.overlay,"in",e.settings.speedIn,e.settings.overlayOpacity,function(){e.transition.fade(e.objects.close,"in",e.settings.speedIn),e._handleEvents(),e._processContent()});break;default:e.transition.fade(e.objects.overlay,"in",0,e.settings.overlayOpacity,function(){e.transition.fade(e.objects.close,"in",0),e._handleEvents(),e._processContent()})}t("html").addClass(e.settings.classPrefix+"open"),e.objects.case.attr("aria-hidden","false")},close:function(){switch(e.isOpen=!1,e.isSlideshowEnabled()&&(e._stopTimeout(),e.objects.nav.removeClass(e.settings.classPrefix+"paused")),e.objects.loading.hide(),e._unbindEvents(),e._unwatchResizeInteraction(),e._unwatchScrollInteraction(),t("html").removeClass(e.settings.classPrefix+"open"),e.objects.case.attr("aria-hidden","true"),e.objects.nav.children().hide(),e._restoreScrollPosition(),e._callHooks(e.settings.onClose),e.settings.transitionOut){case"fade":case"fadeInline":case"scrollTop":case"scrollRight":case"scrollBottom":case"scrollLeft":case"scrollHorizontal":case"scrollVertical":e.transition.fade(e.objects.case,"out",e.settings.speedOut,0,function(){e.transition.fade(e.objects.overlay,"out",e.settings.speedOut,0,function(){e.cleanup()})});break;case"elastic":e.transition.zoom(e.objects.case,"out",e.settings.speedOut,function(){e.transition.fade(e.objects.overlay,"out",e.settings.speedOut,0,function(){e.cleanup()})});break;default:e.cleanup()}},_unbindEvents:function(){e.objects.overlay.unbind("click"),t(document).unbind("keyup.lightcase"),e.objects.case.unbind("swipeleft").unbind("swiperight"),e.objects.prev.unbind("click"),e.objects.next.unbind("click"),e.objects.play.unbind("click"),e.objects.pause.unbind("click"),e.objects.close.unbind("click")},_cleanupDimensions:function(){var t=e.objects.contentInner.css("opacity");e.objects.case.css({width:"",height:"",top:"",left:"","margin-top":"","margin-left":""}),e.objects.contentInner.removeAttr("style").css("opacity",t),e.objects.contentInner.children().removeAttr("style")},cleanup:function(){e._cleanupDimensions(),e.objects.loading.hide(),e.objects.overlay.hide(),e.objects.case.hide(),e.objects.prev.hide(),e.objects.next.hide(),e.objects.play.hide(),e.objects.pause.hide(),e.objects.case.removeAttr(e._prefixAttributeName("type")),e.objects.nav.removeAttr(e._prefixAttributeName("ispartofsequence")),e.objects.contentInner.empty().hide(),e.objects.info.children().empty(),e.cache.originalObject&&e._restoreObject(),e._callHooks(e.settings.onCleanup),e.cache={}},_matchMedia:function(){return window.matchMedia||window.msMatchMedia},_devicePixelRatio:function(){return window.devicePixelRatio||1},_isPublicMethod:function(t){return"function"==typeof e[t]&&"_"!==t.charAt(0)},_export:function(){window.lightcase={},t.each(e,function(t){e._isPublicMethod(t)&&(lightcase[t]=e[t])})}};e._export(),t.fn.lightcase=function(i){return e._isPublicMethod(i)?e[i].apply(this,Array.prototype.slice.call(arguments,1)):"object"!=typeof i&&i?void t.error("Method "+i+" does not exist on jQuery.lightcase"):e.init.apply(this,arguments)}}(jQuery),jQuery(document).ready(function(t){t("[data-image-src]").each(function(){image_src=t(this).data("image-src"),t(this).addClass("background-parallax-container"),t(this).append('<div class="background-parallax" style="background-image:url('+image_src+')"></div>')})}),function(t){"use strict";var e=function(e,i){this.el=t(e),this.options=t.extend({},t.fn.typed.defaults,i),this.isInput=this.el.is("input"),this.attr=this.options.attr,this.showCursor=!this.isInput&&this.options.showCursor,this.elContent=this.attr?this.el.attr(this.attr):this.el.text(),this.contentType=this.options.contentType,this.typeSpeed=this.options.typeSpeed,this.startDelay=this.options.startDelay,this.backSpeed=this.options.backSpeed,this.backDelay=this.options.backDelay,this.stringsElement=this.options.stringsElement,this.strings=this.options.strings,this.strPos=0,this.arrayPos=0,this.stopNum=0,this.loop=this.options.loop,this.loopCount=this.options.loopCount,this.curLoop=0,this.stop=!1,this.cursorChar=this.options.cursorChar,this.shuffle=this.options.shuffle,this.sequence=[],this.build()};e.prototype={constructor:e,init:function(){var t=this;t.timeout=setTimeout(function(){for(var e=0;e<t.strings.length;++e)t.sequence[e]=e;t.shuffle&&(t.sequence=t.shuffleArray(t.sequence)),t.typewrite(t.strings[t.sequence[t.arrayPos]],t.strPos)},t.startDelay)},build:function(){var e=this;if(!0===this.showCursor&&(this.cursor=t('<span class="typed-cursor">'+this.cursorChar+"</span>"),this.el.after(this.cursor)),this.stringsElement){e.strings=[],this.stringsElement.hide();var i=this.stringsElement.find("p");t.each(i,function(i,s){e.strings.push(t(s).html())})}this.init()},typewrite:function(t,e){if(!0!==this.stop){var i=Math.round(70*Math.random())+this.typeSpeed,s=this;s.timeout=setTimeout(function(){var i=0,n=t.substr(e);if("^"===n.charAt(0)){var o=1;/^\^\d+/.test(n)&&(o+=(n=/\d+/.exec(n)[0]).length,i=parseInt(n)),t=t.substring(0,e)+t.substring(e+o)}if("html"===s.contentType){var a=t.substr(e).charAt(0);if("<"===a||"&"===a){var c;for(c="<"===a?">":";";t.substr(e).charAt(0)!==c;)t.substr(e).charAt(0),e++;e++,c}}s.timeout=setTimeout(function(){if(e===t.length){if(s.options.onStringTyped(s.arrayPos),s.arrayPos===s.strings.length-1&&(s.options.callback(),s.curLoop++,!1===s.loop||s.curLoop===s.loopCount))return;s.timeout=setTimeout(function(){s.backspace(t,e)},s.backDelay)}else{0===e&&s.options.preStringTyped(s.arrayPos);var i=t.substr(0,e+1);s.attr?s.el.attr(s.attr,i):s.isInput?s.el.val(i):"html"===s.contentType?s.el.html(i):s.el.text(i),e++,s.typewrite(t,e)}},i)},i)}},backspace:function(t,e){if(!0!==this.stop){var i=Math.round(70*Math.random())+this.backSpeed,s=this;s.timeout=setTimeout(function(){if("html"===s.contentType&&">"===t.substr(e).charAt(0)){for(;"<"!==t.substr(e).charAt(0);)t.substr(e).charAt(0),e--;e--,"<"}var i=t.substr(0,e);s.attr?s.el.attr(s.attr,i):s.isInput?s.el.val(i):"html"===s.contentType?s.el.html(i):s.el.text(i),e>s.stopNum?(e--,s.backspace(t,e)):e<=s.stopNum&&(s.arrayPos++,s.arrayPos===s.strings.length?(s.arrayPos=0,s.shuffle&&(s.sequence=s.shuffleArray(s.sequence)),s.init()):s.typewrite(s.strings[s.sequence[s.arrayPos]],e))},i)}},shuffleArray:function(t){var e,i,s=t.length;if(s)for(;--s;)e=t[i=Math.floor(Math.random()*(s+1))],t[i]=t[s],t[s]=e;return t},reset:function(){clearInterval(this.timeout);var t=this.el.attr("id");this.el.after('<span id="'+t+'"/>'),this.el.remove(),void 0!==this.cursor&&this.cursor.remove(),this.options.resetCallback()}},t.fn.typed=function(i){return this.each(function(){var s=t(this),n=s.data("typed"),o="object"==typeof i&&i;n||s.data("typed",n=new e(this,o)),"string"==typeof i&&n[i]()})},t.fn.typed.defaults={strings:["These are the default values...","You know what you should do?","Use your own!","Have a great day!"],stringsElement:null,typeSpeed:0,startDelay:0,backSpeed:0,shuffle:!1,backDelay:500,loop:!1,loopCount:!1,showCursor:!0,cursorChar:"|",attr:null,contentType:"html",callback:function(){},preStringTyped:function(){},onStringTyped:function(){},resetCallback:function(){}}}(window.jQuery);