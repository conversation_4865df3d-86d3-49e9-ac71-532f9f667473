<?php
/**
 * Direct AJAX Test
 * 
 * This file directly tests AJAX functionality without using WordPress templates.
 */

// Load WordPress
require_once('../../../../wp-load.php');

// Set up the header
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct AJAX Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #2980b9;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            background: #f1f1f1;
            display: none;
        }
        .success {
            color: #27ae60;
        }
        .error {
            color: #e74c3c;
        }
        pre {
            background: #f1f1f1;
            padding: 10px;
            border-radius: 4px;
            overflow: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Direct AJAX Test</h1>
        <p>This page tests AJAX functionality directly with WordPress loaded.</p>
        
        <div class="form-group">
            <label for="name">Your Name:</label>
            <input type="text" id="name" placeholder="Enter your name">
        </div>
        
        <div class="form-group">
            <label for="message">Your Message:</label>
            <textarea id="message" rows="4" placeholder="Enter your message"></textarea>
        </div>
        
        <button id="submit-btn">Send Test</button>
        
        <div id="result"></div>
        
        <div style="margin-top: 30px;">
            <h2>WordPress Information</h2>
            <p>AJAX URL: <code><?php echo admin_url('admin-ajax.php'); ?></code></p>
            <p>WordPress Version: <code><?php echo get_bloginfo('version'); ?></code></p>
            <p>Theme: <code><?php echo get_template(); ?></code></p>
            <p>PHP Version: <code><?php echo phpversion(); ?></code></p>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#submit-btn').on('click', function() {
                var name = $('#name').val();
                var message = $('#message').val();
                
                if (!name || !message) {
                    $('#result').html('<p class="error">Please fill in all fields</p>').show();
                    return;
                }
                
                $('#result').html('<p>Sending test...</p>').show();
                
                $.ajax({
                    url: '<?php echo admin_url('admin-ajax.php'); ?>',
                    type: 'POST',
                    data: {
                        action: 'simple_test_action',
                        nonce: '<?php echo wp_create_nonce('simple_test_nonce'); ?>',
                        name: name,
                        message: message
                    },
                    success: function(response) {
                        console.log('Success:', response);
                        
                        if (response.success) {
                            $('#result').html('<p class="success">Success! Server response: ' + response.data.message + '</p>');
                        } else {
                            $('#result').html('<p class="error">Error: ' + (response.data ? response.data.message : 'Unknown error') + '</p>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log('Error:', status, error);
                        console.log('Response:', xhr.responseText);
                        
                        $('#result').html('<p class="error">AJAX Error: ' + status + ' - ' + error + '</p><pre>' + xhr.responseText + '</pre>');
                    }
                });
            });
        });
    </script>
</body>
</html>