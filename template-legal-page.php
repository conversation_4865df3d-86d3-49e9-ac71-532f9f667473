<?php
/**
 * Template Name: Legal Page
 * Template Post Type: page
 * A template for legal pages with a sidebar for navigation between legal pages.
 * @package Nkhwazi_Primary_School
 */

get_header();
?>

<!-- MAIN -->
<main role="main">
  <!-- Content -->
  <article>
    <div class="line">
      <header class="rounded-div section background-primary background-transparent text-center"
        data-image-src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/parallax-06.webp">
        <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1"><?php the_title(); ?></h1>
      </header>
    </div>

    <div class="section background-white">
      <div class="line">
        <div class="margin">
          <!-- Main Content (8 columns) -->
          <div class="s-12 m-8 l-9">
            <div class="legal-content padding-2x">
              <?php
              while (have_posts()) :
                  the_post();
                  
                  the_content();
                  
              endwhile; // End of the loop.
              ?>
            </div>
          </div>
          
          <!-- Sidebar (4 columns) -->
          <div class="s-12 m-4 l-3">
            <div class="legal-sidebar padding-2x background-light-gray rounded-div">
              <h3 class="text-strong margin-bottom-20">Legal Documents</h3>
              
              <ul class="legal-nav">
                <?php
                // Get all legal pages
                $legal_pages = array(
                    'privacy-policy' => 'Privacy Policy',
                    'terms-of-use' => 'Terms of Use',
                    'cookies' => 'Cookies',
                    'disclaimer' => 'Disclaimer'
                );
                
                $current_page_slug = get_post_field('post_name', get_post());
                
                foreach ($legal_pages as $slug => $title) {
                    $page = get_page_by_path($slug);
                    if ($page) {
                        $class = ($current_page_slug === $slug) ? 'active-link' : '';
                        echo '<li><a href="' . esc_url(get_permalink($page->ID)) . '" class="' . $class . '">' . esc_html($title) . '</a></li>';
                    }
                }
                ?>
              </ul>
              
              <div class="legal-contact margin-top-30">
                <h4 class="text-strong margin-bottom-10">Questions?</h4>
                <p>If you have any questions about our legal policies, please <a href="<?php echo esc_url(get_permalink(407)); ?>">contact us</a>.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </article>
</main>

<?php get_footer(); ?>