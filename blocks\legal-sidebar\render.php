<?php
/**
 * Render the Legal Sidebar block
 *
 * @package Nkhwazi_Primary_School
 */

// Get the current page slug
$current_page_slug = get_post_field('post_name', get_post());

// Define legal pages
$legal_pages = array(
    'privacy-policy' => 'Privacy Policy',
    'terms-of-use' => 'Terms of Use',
    'cookies' => 'Cookies',
    'disclaimer' => 'Disclaimer'
);
?>

<div <?php echo get_block_wrapper_attributes(['class' => 'legal-sidebar padding-2x background-light-gray rounded-div']); ?>>
    <h3 class="text-strong margin-bottom-20">Legal Documents</h3>
    
    <ul class="legal-nav">
        <?php
        foreach ($legal_pages as $slug => $title) {
            $page = get_page_by_path($slug);
            if ($page) {
                $class = ($current_page_slug === $slug) ? 'active-link' : '';
                echo '<li><a href="' . esc_url(get_permalink($page->ID)) . '" class="' . $class . '">' . esc_html($title) . '</a></li>';
            }
        }
        ?>
    </ul>
    
    <div class="legal-contact margin-top-30">
        <h4 class="text-strong margin-bottom-10">Questions?</h4>
        <p>If you have any questions about our legal policies, please <a href="<?php echo esc_url(get_permalink(407)); ?>">contact us</a>.</p>
    </div>
</div>