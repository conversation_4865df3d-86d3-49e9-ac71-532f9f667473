<?php
/**
 * Upcoming Events Section for Homepage
 */
?>
<div class="grid margin">
  <?php
  // Get upcoming events
  $args = array(
      'post_type' => 'upcoming_event',
      'posts_per_page' => 2,
      'meta_key' => 'due_date',
      'orderby' => 'meta_value',
      'order' => 'ASC',
      // No meta_query to ensure we get events regardless of date
  );
  $events_query = new WP_Query($args);
  
  // Debug events query
  echo '<!-- Events query found: ' . $events_query->found_posts . ' posts -->';
  
  if ($events_query->have_posts()) :
      while ($events_query->have_posts()) : $events_query->the_post();
          // Get ACF fields
          $due_date = get_field('due_date');
          $event_name = get_field('event_name');
          $event_excerpt = get_field('event_excerpt');
          
          // Debug ACF fields
          echo '<!-- Event: ' . get_the_title() . ' | Due Date: ' . $due_date . ' | Name: ' . $event_name . ' -->';
          
          // Get permalink for the event
          $event_link = get_permalink();
  ?>
  <article class="s-12 m-12 l-6 margin-m-bottom event-card padding">
    <div class="grid text-left">
      <div class="s-12 m-12 l-1 text-left">
        <i class="icon-sli-clock text-strong text-secondary text-size-20"></i>
      </div>
      <div class="s-11 m-11 l-11 margin-bottom-10">
        <div class="admin-name margin-left-10 text-size-16">Due: <span
            class="text-size-14 rounded-div text-primary-dark event-date"><?php echo esc_html($due_date); ?></span>
        </div>
      </div>
    </div>
    <h3 class="text-strong"><a class="text-blue text-yellow-hover"
        href="<?php echo esc_url($event_link); ?>"><?php echo esc_html($event_name); ?></a>
    </h3>
    <p><?php echo esc_html(wp_trim_words($event_excerpt, 20)); ?></p>
    <a class="text-more-info text-primary-hover" href="<?php echo esc_url($event_link); ?>">Event Details</a>
  </article>
  <?php
      endwhile;
      wp_reset_postdata();
  endif; ?>

  <div class="center margin-bottom-10 s-12 m-6 l-4">
    <a href="<?php echo esc_url(get_permalink(434)); ?>" class="button background-primary text-white">View All Upcoming Events</a>
  </div>
</div>