<?php
/**
 * Template Name: Page with Sidebar
 * Template Post Type: page
 * 
 * A balanced layout with a main content area (9 columns) and a sidebar (3 columns). 
 * Ideal for most pages, allowing readers to consume main content while staying updated 
 * via sidebar elements like recent blog posts, events, or announcements.
 *
 * @package Nkhwazi_Primary_School
 */

get_header();
?>

<!-- MAIN -->
<main role="main">
  <!-- third section -->
  <article>
    <div class="line">
      <header class="rounded-div section background-primary background-transparent text-center"
        data-image-src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/parallax-02.webp">
        <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1"><?php the_title(); ?></h1>
      </header>
    </div>

    <div class="section background-white">
      <div class="line">
        <div class="margin">
          <div class="s-12 m-8 l-9">
            <?php 
            while (have_posts()) :
                the_post();
                the_content();
            endwhile;
            ?>
          </div><!-- Ends content -->

          <!-- Sidebar -->
          <?php get_template_part('includes/main_sidebar'); ?>

        </div>
      </div>
    </div>
  </article>
</main>

<?php get_footer(); ?>