/**
 * Contact Form Validation
 * 
 * Handles live validation for the contact form
 */
(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initContactFormValidation();
    });

    /**
     * Initialize contact form validation
     */
    function initContactFormValidation() {
        const $form = $('.nkhwazi-contact-form');
        
        if (!$form.length) {
            console.log('Contact form not found');
            return;
        }
        
        console.log('Initializing contact form validation');
        
        // Initialize character counters for fields with min/max length
        initCharacterCounters($form);
        
        // Set default border style for all fields
        // Note: We're still adding default-border class but the required class styling will take precedence for the left border
        $form.find('input, textarea, select').addClass('default-border');
        
        // Validate required fields on blur
        $form.find('.required').on('blur', function() {
            validateField($(this));
        });
        
        // Validate optional fields with min/max length on blur
        $form.find('input:not(.required), textarea:not(.required)').filter('[data-min-length], [data-max-length]').on('blur', function() {
            // Only validate if the field has content
            if ($(this).val().trim() !== '') {
                validateField($(this));
            }
        });
        
        // Validate email fields on blur
        $form.find('.email').on('blur', function() {
            validateEmail($(this));
        });
        
        // Validate fields on change for select elements
        $form.find('select.required').on('change', function() {
            validateField($(this));
        });
        
        // Live validation as user types (after first blur)
        $form.find('input.required, textarea.required').on('input', function() {
            if ($(this).hasClass('error') || $(this).hasClass('valid') || $(this).val().trim() !== '') {
                validateField($(this));
            }
            updateCharacterCount($(this));
        });
        
        // Live validation for email fields
        $form.find('input.email').on('input', function() {
            if ($(this).hasClass('error') || $(this).hasClass('valid') || $(this).val().trim() !== '') {
                validateEmail($(this));
            }
        });
        
        // Live validation for all fields with min/max length
        $form.find('[data-min-length], [data-max-length]').on('input', function() {
            updateCharacterCount($(this));
            // For optional fields, only validate if they have content
            if ($(this).hasClass('required') || $(this).val().trim() !== '') {
                validateField($(this));
            } else {
                // If optional field is empty, remove any validation classes
                $(this).removeClass('error valid').addClass('default-border');
            }
        });
        
        // Initialize character counters on page load
        $form.find('[data-min-length], [data-max-length]').each(function() {
            updateCharacterCount($(this));
        });
        
        // Handle form submission
        $form.on('submit', function(e) {
            // Validate all fields before submission
            const isValid = validateAllFields($form);
            
            if (!isValid) {
                e.preventDefault();
                showFormMessage($form, 'Please fill in all required fields correctly.', 'error');
            }
        });
    }
    
    /**
     * Initialize character counters for fields with min/max length
     */
    function initCharacterCounters($form) {
        $form.find('[data-min-length], [data-max-length]').each(function() {
            const $field = $(this);
            
            // Create a container for the field and counter if it doesn't exist
            if (!$field.parent().hasClass('field-with-counter')) {
                // Get the field's current container
                const $fieldContainer = $field.parent();
                
                // Create a wrapper for the field
                $field.wrap('<div class="field-with-counter"></div>');
                
                // Add character counter after the field inside the wrapper (positioned below the field)
                $field.after('<div class="character-counter"></div>');
                updateCharacterCount($field);
            }
        });
    }
    
    /**
     * Update character count display
     */
    function updateCharacterCount($field) {
        const minLength = $field.data('min-length');
        const maxLength = $field.data('max-length');
        
        if (!minLength && !maxLength) {
            return;
        }
        
        const currentLength = $field.val().length;
        
        // Find the counter within the field's wrapper
        let $counter;
        if ($field.parent().hasClass('field-with-counter')) {
            $counter = $field.parent().find('.character-counter');
        } else {
            // For backward compatibility
            $counter = $field.next('.character-counter');
        }
        
        // Create counter if it doesn't exist
        if (!$counter.length) {
            if ($field.parent().hasClass('field-with-counter')) {
                $field.after('<div class="character-counter"></div>');
                $counter = $field.parent().find('.character-counter');
            } else {
                $field.after('<div class="character-counter"></div>');
                $counter = $field.next('.character-counter');
            }
        }
        
        let counterText = '';
        let counterClass = '';
        
        // Different messages based on validation state
        if (currentLength === 0 && !$field.hasClass('required')) {
            // Optional field that's empty
            counterText = 'Optional';
        } else if (minLength > 0 && currentLength < minLength && (currentLength > 0 || $field.hasClass('required'))) {
            // Below minimum length (only show warning if field has content or is required)
            counterText = currentLength + '/' + minLength + ' min';
            counterClass = 'warning';
        } else if (maxLength && currentLength > maxLength) {
            // Exceeds maximum length
            counterText = currentLength + '/' + maxLength + ' (too long)';
            counterClass = 'error';
        } else if (maxLength && currentLength >= maxLength - 3) {
            // Approaching maximum length (3 characters before max)
            const remaining = maxLength - currentLength;
            counterText = remaining + ' character' + (remaining !== 1 ? 's' : '') + ' left';
            counterClass = 'warning';
        } else if (maxLength) {
            // Within limits with maximum
            counterText = currentLength + '/' + maxLength;
        } else if (minLength > 0 && currentLength >= minLength) {
            // Above minimum length with no maximum
            counterText = currentLength + ' characters';
        }
        
        $counter.text(counterText).removeClass('warning error').addClass(counterClass);
    }
    
    /**
     * Validate a single field
     */
    function validateField($field) {
        // Get the container (either field-with-counter or parent)
        const $container = $field.parent().hasClass('field-with-counter') ? $field.parent() : $field.parent();
        
        // Remove existing error message
        $container.find('.field-error-message').remove();
        
        // Get field value and trim it
        const fieldValue = $field.val() ? $field.val().trim() : '';
        
        // Check if field is empty
        if (fieldValue === '' && $field.hasClass('required')) {
            $field.removeClass('valid default-border').addClass('error');
            
            // Add error message after the counter
            if ($container.hasClass('field-with-counter')) {
                // If using the new structure, add after the counter
                const $counter = $container.find('.character-counter');
                if ($counter.length) {
                    $counter.after('<span class="field-error-message">This field is required</span>');
                } else {
                    $field.after('<span class="field-error-message">This field is required</span>');
                }
            } else {
                // Legacy support
                $field.after('<span class="field-error-message">This field is required</span>');
            }
            return false;
        }
        
        // Skip validation if field is empty and not required
        if (fieldValue === '' && !$field.hasClass('required')) {
            $field.removeClass('error valid').addClass('default-border');
            return true;
        }
        
        // For select elements, we only need to validate that a selection was made
        if ($field.is('select')) {
            // If we got here, the select field has a value and is valid
            $field.removeClass('error default-border').addClass('valid');
            return true;
        }
        
        // Check for alphanumeric content (only for text inputs and textareas)
        // First, ensure the field contains at least one alphanumeric character
        const hasAlphanumericRegex = /[a-zA-Z0-9]/;
        if (!hasAlphanumericRegex.test(fieldValue) && fieldValue !== '') {
            $field.removeClass('valid default-border').addClass('error');
            
            // Add error message after the counter
            const errorMsg = 'Please enter at least one letter or number';
            if ($container.hasClass('field-with-counter')) {
                const $counter = $container.find('.character-counter');
                if ($counter.length) {
                    $counter.after('<span class="field-error-message">' + errorMsg + '</span>');
                } else {
                    $field.after('<span class="field-error-message">' + errorMsg + '</span>');
                }
            } else {
                $field.after('<span class="field-error-message">' + errorMsg + '</span>');
            }
            return false;
        }
        
        // Special validation for phone fields
        if ($field.attr('name') && $field.attr('name').includes('phone') && fieldValue !== '') {
            // Check if the phone field contains at least one digit
            const phoneRegex = /[0-9]/;
            if (!phoneRegex.test(fieldValue)) {
                $field.removeClass('valid default-border').addClass('error');
                
                // Add error message
                const errorMsg = 'Phone number must contain at least one digit';
                if ($container.hasClass('field-with-counter')) {
                    const $counter = $container.find('.character-counter');
                    if ($counter.length) {
                        $counter.after('<span class="field-error-message">' + errorMsg + '</span>');
                    } else {
                        $field.after('<span class="field-error-message">' + errorMsg + '</span>');
                    }
                } else {
                    $field.after('<span class="field-error-message">' + errorMsg + '</span>');
                }
                return false;
            }
        }
        
        // Check min and max length if specified (only for text inputs and textareas)
        const minLength = $field.data('min-length');
        const maxLength = $field.data('max-length');
        
        if (minLength > 0 && fieldValue.length < minLength) {
            $field.removeClass('valid default-border').addClass('error');
            
            // Add error message after the counter
            const errorMsg = 'Please enter at least ' + minLength + ' characters';
            if ($container.hasClass('field-with-counter')) {
                const $counter = $container.find('.character-counter');
                if ($counter.length) {
                    $counter.after('<span class="field-error-message">' + errorMsg + '</span>');
                } else {
                    $field.after('<span class="field-error-message">' + errorMsg + '</span>');
                }
            } else {
                $field.after('<span class="field-error-message">' + errorMsg + '</span>');
            }
            return false;
        }
        
        if (maxLength && fieldValue.length > maxLength) {
            $field.removeClass('valid default-border').addClass('error');
            
            // Add error message after the counter
            const errorMsg = 'Please enter no more than ' + maxLength + ' characters';
            if ($container.hasClass('field-with-counter')) {
                const $counter = $container.find('.character-counter');
                if ($counter.length) {
                    $counter.after('<span class="field-error-message">' + errorMsg + '</span>');
                } else {
                    $field.after('<span class="field-error-message">' + errorMsg + '</span>');
                }
            } else {
                $field.after('<span class="field-error-message">' + errorMsg + '</span>');
            }
            return false;
        }
        
        // If we got here, the field is valid
        // Remove error and default-border classes, add valid class (this will override the required red border with a green one)
        $field.removeClass('error default-border').addClass('valid');
        return true;
    }
    
    /**
     * Validate email field
     */
    function validateEmail($field) {
        // Get the container (either field-with-counter or parent)
        const $container = $field.parent().hasClass('field-with-counter') ? $field.parent() : $field.parent();
        
        // Remove existing error message
        $container.find('.field-error-message').remove();
        
        // Get field value and trim it
        const fieldValue = $field.val().trim();
        
        // Check if field is empty and required
        if (fieldValue === '' && $field.hasClass('required')) {
            $field.removeClass('valid default-border').addClass('error');
            
            // Add error message after the counter
            if ($container.hasClass('field-with-counter')) {
                const $counter = $container.find('.character-counter');
                if ($counter.length) {
                    $counter.after('<span class="field-error-message">Email is required</span>');
                } else {
                    $field.after('<span class="field-error-message">Email is required</span>');
                }
            } else {
                $field.after('<span class="field-error-message">Email is required</span>');
            }
            return false;
        }
        
        // Skip validation if field is empty and not required
        if (fieldValue === '' && !$field.hasClass('required')) {
            $field.removeClass('error valid').addClass('default-border');
            return true;
        }
        
        // Simple email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(fieldValue)) {
            $field.removeClass('valid default-border').addClass('error');
            
            // Add error message
            if ($container.hasClass('field-with-counter')) {
                const $counter = $container.find('.character-counter');
                if ($counter.length) {
                    $counter.after('<span class="field-error-message">Please enter a valid email address</span>');
                } else {
                    $field.after('<span class="field-error-message">Please enter a valid email address</span>');
                }
            } else {
                $field.after('<span class="field-error-message">Please enter a valid email address</span>');
            }
            return false;
        }
        
        // If we got here, the email is valid
        $field.removeClass('error default-border').addClass('valid');
        return true;
    }
    
    /**
     * Validate all fields in the form
     */
    function validateAllFields($form) {
        let isValid = true;
        
        // Validate required fields
        $form.find('.required').each(function() {
            if ($(this).hasClass('email')) {
                if (!validateEmail($(this))) {
                    isValid = false;
                }
            } else {
                if (!validateField($(this))) {
                    isValid = false;
                }
            }
        });
        
        // Validate optional fields with content
        $form.find('input:not(.required), textarea:not(.required)').filter('[data-min-length], [data-max-length]').each(function() {
            if ($(this).val().trim() !== '') {
                if (!validateField($(this))) {
                    isValid = false;
                }
            }
        });
        
        return isValid;
    }
    
    /**
     * Show form message
     */
    function showFormMessage($form, message, type) {
        const $messageContainer = $form.find('.form-message');
        
        if (!$messageContainer.length) {
            $form.prepend('<div class="form-message"></div>');
            $messageContainer = $form.find('.form-message');
        }
        
        $messageContainer.removeClass('success error').addClass(type).html(message).fadeIn();
        
        // Scroll to message
        $('html, body').animate({
            scrollTop: $messageContainer.offset().top - 100
        }, 300);
        
        // Hide message after 5 seconds for success messages
        if (type === 'success') {
            setTimeout(function() {
                $messageContainer.fadeOut();
            }, 5000);
        }
    }

})(jQuery);