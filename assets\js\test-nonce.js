/**
 * Test script for nonce verification
 */
(function($) {
    'use strict';

    $(document).ready(function() {
        console.log('Test nonce script loaded');
        
        // Add a test button to the form
        const $form = $('#application-form');
        if ($form.length) {
            const $testButton = $('<button type="button" class="test-nonce-btn">Test Nonce</button>');
            $form.find('.form-message').after($testButton);
            
            $testButton.on('click', function(e) {
                e.preventDefault();
                testNonce();
            });
        }
    });
    
    function testNonce() {
        console.log('Testing nonce...');
        
        // Create a simple test request
        const formData = new FormData();
        formData.append('action', 'nkhwazi_test_nonce');
        
        // Use the nonce from the hidden field if available
        const $form = $('#application-form');
        const hiddenNonce = $form.find('input[name="nonce"]').val();
        if (hiddenNonce) {
            formData.append('nonce', hiddenNonce);
            console.log('Using nonce from hidden field for test:', hiddenNonce);
        } else {
            formData.append('nonce', nkhwazi_personal_details_form.nonce);
            console.log('Using nonce from localized script for test:', nkhwazi_personal_details_form.nonce);
        }
        
        $.ajax({
            url: nkhwazi_personal_details_form.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                console.log('Test nonce response:', response);
                alert('Nonce test result: ' + (response.success ? 'Success' : 'Failed') + '\n' + 
                      'Message: ' + (response.data ? response.data.message : 'No message'));
            },
            error: function(xhr, status, error) {
                console.log('Test nonce error:', status, error);
                alert('Nonce test error: ' + error);
            }
        });
    }
})(jQuery);