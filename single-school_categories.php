<?php
/**
 * Single School Category Template
 * Description: Template for displaying individual school category posts.
 * Author: <PERSON>
 */

get_header();

// Get the current school category post
if (have_posts()) :
    while (have_posts()) : the_post();
        
        // Get the category data from ACF fields
        $category_title = get_the_title();
        $category_description = get_the_content();
        $category_age_range = get_field('age_range');
        $category_grades = get_field('grade_range');
        $card_img = get_field('card_img');
        $daily_schedule = get_field('daily_schedule');
        
        $category_image = '';
        if ($card_img) {
            $category_image = wp_get_attachment_image_url($card_img['ID'], 'page-image');
        }
        
    endwhile;
endif;
?>

<!-- MAIN -->
<main role="main">
  <!-- third section -->
  <article>
    <div class="line">
      <header class="rounded-div section background-primary background-transparent text-center"
        data-image-src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/parallax-03.webp">
        <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1"><?php echo esc_html($category_title); ?></h1>
      </header>
    </div>
    
    <!-- Full-width Category Image (only shown if image exists) -->
    <?php if (!empty($category_image)): ?>
    <div class="line margin-top-30 margin-bottom-30">
      <div class="s-12">
        <img src="<?php echo esc_url($category_image); ?>" alt="<?php echo esc_attr($category_title); ?>" class="full-width-img rounded-image">
      </div>
    </div>
    <?php endif; ?>

    <div class="section background-white">
      <div class="line">
        <div class="margin">
          <div class="s-12 m-8 l-9">
            <!-- put page content below, do not edit above this comment -->

            <div class="line">
              <div class="margin">
                
                <?php 
                // Check if any content exists
                $has_content = !empty($category_age_range) || !empty($category_grades) || 
                               !empty($category_description) || !empty($daily_schedule);
                
                if ($has_content): 
                ?>
                
                <!-- Category Overview -->
                <div class="s-12 margin-bottom-30">
                  <h2 class="text-padding-small background-primary text-white text-strong text-uppercase margin-bottom-30">
                    <i class="icon-book margin-right-10"></i><?php echo esc_html($category_title); ?> Details
                  </h2>
                  
                  <!-- Key Information Box -->
                  <div class="margin-bottom-30">
                    <div class="category-info-box background-white border-radius box-shadow padding">
                      <div class="grid margin">
                        <?php if (!empty($category_grades)): ?>
                        <div class="s-12 m-4 l-2">
                          <h3 class="text-strong margin-bottom-5">Grades</h3>
                          <p><?php echo esc_html($category_grades); ?></p>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($category_age_range)): ?>
                        <div class="s-12 m-4 l-2">
                          <h3 class="text-strong margin-bottom-5">Ages</h3>
                          <p><?php echo esc_html($category_age_range); ?> years</p>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($daily_schedule)): ?>
                        <div class="s-12 m-4 l-8">
                          <h3 class="text-strong margin-bottom-5">Daily Schedule</h3>
                          <?php echo wpautop($daily_schedule); ?>
                        </div>
                        <?php endif; ?>
                      </div>
                    </div>
                  </div>
                  

                </div>

                <?php else: ?>
                <!-- No Content Message -->
                <div class="s-12 margin-bottom-30">
                  <div class="text-center background-light-gray padding-2x border-radius">
                    <i class="icon-info_outline icon-3x text-primary margin-bottom-10"></i>
                    <h3 class="text-strong text-size-20 margin-bottom-10">No Information Available</h3>
                    <p>The details for this school category are currently being updated. Please check back later for complete information.</p>
                  </div>
                </div>
                <?php endif; ?>
                
                <!-- Post Content -->
                <?php 
                if (have_posts()) :
                    while (have_posts()) : the_post();
                        if (get_the_content()) {
                            echo '<div class="s-12 margin-bottom-30">';
                            the_content();
                            echo '</div>';
                        }
                    endwhile;
                endif;
                ?>
                
                <!-- Teachers in this Category -->
                <div class="s-12 margin-bottom-30">
                  <h2 class="text-padding-small background-primary text-white text-strong text-uppercase margin-bottom-30">
                    <i class="icon-users margin-right-10"></i>Teachers in <?php echo esc_html($category_title); ?>
                  </h2>
                  
                  <?php
                  // Query teachers in this category
                  $current_category_id = get_the_ID();
                  $teachers_args = array(
                      'post_type' => 'teacher',
                      'posts_per_page' => -1,
                      'meta_query' => array(
                          array(
                              'key' => 'school_category',
                              'value' => '"' . $current_category_id . '"',
                              'compare' => 'LIKE'
                          )
                      ),
                      'meta_key' => 'priority',
                      'orderby' => array(
                          'meta_value_num' => 'ASC',
                          'title' => 'ASC'
                      )
                  );
                  
                  $teachers_query = new WP_Query($teachers_args);
                  
                  if ($teachers_query->have_posts()) : ?>
                      <div class="grid margin">
                          <?php while ($teachers_query->have_posts()) : $teachers_query->the_post();
                              $first_name = get_field('first_name');
                              $last_name = get_field('last_name');
                              $job_title = get_field('job_title');
                              $portrait_img = get_field('portrait_img');
                              $teacher_name = $first_name . ' ' . $last_name;
                          ?>
                              <div class="s-12 m-6 l-3 margin-bottom-20">
                                  <div class="teacher-mini-card background-white border-radius box-shadow padding text-center">
                                      <?php if ($portrait_img && !empty($portrait_img)) : ?>
                                          <?php 
                                          $mini_image = wp_get_attachment_image(
                                              $portrait_img['ID'],
                                              'thumbnail',
                                              false,
                                              array('class' => 'teacher-mini-photo', 'alt' => $teacher_name)
                                          );
                                          echo $mini_image;
                                          ?>
                                      <?php endif; ?>
                                      <h4 class="margin-top-10 margin-bottom-5"><?php echo esc_html($teacher_name); ?></h4>
                                      <p class="text-size-14"><?php echo esc_html($job_title); ?></p>
                                  </div>
                              </div>
                          <?php endwhile; ?>
                      </div>
                      
                      <div class="text-center margin-top-30">
                          <a href="<?php echo esc_url(get_permalink(376)); ?>" class="button border-radius background-primary">
                              View All Teaching Staff
                          </a>
                      </div>
                  <?php else : ?>
                      <div class="text-center background-light-gray padding border-radius">
                          <p>No teachers are currently assigned to this category.</p>
                      </div>
                  <?php endif;
                  
                  wp_reset_postdata();
                  ?>
                </div>
              </div>
            </div>

            <!-- put page content above, do not edit below this comment -->
          </div><!-- Ends content -->

          <!-- Category Sidebar -->
          <?php get_template_part('includes/category-sidebar'); ?>

        </div>
      </div>
    </div>
  </article>
</main>

<?php get_footer(); ?>