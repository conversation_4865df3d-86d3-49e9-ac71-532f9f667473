/* Application Form Styles */

/* Form validation styles */
.customform input.error,
.customform select.error,
.customform textarea.error {
    border-color: #e74c3c !important;
    border-left-width: 4px !important;
}

.customform input.valid,
.customform select.valid,
.customform textarea.valid {
    border-color: #2ecc71 !important;
    border-left-width: 4px !important;
}

.field-error-message {
    color: #e74c3c;
    font-size: 0.85em;
    margin-top: 5px;
    display: block;
}

/* Form feedback messages */
.form-message {
    padding: 15px;
    margin: 20px 0;
    border-radius: 4px;
    display: none;
}

.form-message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.form-message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Loading indicator */
.form-loading {
    display: none;
    text-align: center;
    padding: 20px;
}

.form-loading .spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: #3649e2;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Form sections */
.form-section {
    margin-bottom: 30px;
}

.form-section-title {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e5e5e5;
}

/* Required field indicator */
.required-field {
    color: #e74c3c;
    margin-left: 3px;
}

/* Form field focus styles */
.customform input:focus,
.customform select:focus,
.customform textarea:focus {
    border-color: #3649e2;
    box-shadow: 0 0 0 2px rgba(54, 73, 226, 0.25);
    outline: none;
}

/* Submit button styles */
.submit-btn {
    transition: all 0.3s ease;
}

.submit-btn:hover {
    background-color: #2a3ab9 !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.submit-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Datepicker customization */
.ui-datepicker {
    padding: 10px;
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.ui-datepicker .ui-datepicker-header {
    background: #3649e2;
    color: white;
    border: none;
}

.ui-datepicker .ui-datepicker-prev,
.ui-datepicker .ui-datepicker-next {
    background: transparent;
    cursor: pointer;
}

.ui-datepicker .ui-datepicker-prev span,
.ui-datepicker .ui-datepicker-next span {
    filter: brightness(0) invert(1);
}

.ui-datepicker .ui-datepicker-title {
    font-weight: bold;
}

.ui-datepicker .ui-datepicker-calendar th {
    font-weight: normal;
    color: #666;
}

.ui-datepicker .ui-datepicker-calendar td {
    padding: 3px;
}

.ui-datepicker .ui-datepicker-calendar td a {
    text-align: center;
    border-radius: 4px;
}

.ui-datepicker .ui-datepicker-calendar td a.ui-state-default {
    background: #f5f5f5;
    border: none;
    color: #333;
}

.ui-datepicker .ui-datepicker-calendar td a.ui-state-default:hover {
    background: #e0e0e0;
}

.ui-datepicker .ui-datepicker-calendar td a.ui-state-active {
    background: #3649e2;
    color: white;
}

/* Form progress indicator */
.form-progress {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    position: relative;
}

.form-progress:before {
    content: '' !important;
    position: absolute !important;
    top: 15px !important;
    left: 0 !important;
    right: 0 !important;
    height: 2px !important;
    background: #e5e5e5 !important;
    z-index: 1 !important;
}

.form-progress-step {
    position: relative;
    z-index: 2;
    text-align: center;
}

.form-progress-step-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #e5e5e5;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 10px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.form-progress-step.active .form-progress-step-number {
    background: #3649e2;
    color: white;
}

.form-progress-step.completed .form-progress-step-number {
    background: #2ecc71;
    color: white;
}

.form-progress-step-label {
    font-size: 0.85em;
    color: #666;
    transition: all 0.3s ease;
}

.form-progress-step.active .form-progress-step-label {
    color: #3649e2;
    font-weight: bold;
}

.form-progress-step.completed .form-progress-step-label {
    color: #2ecc71;
}

/* Form step navigation */
.form-navigation {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
}

.form-navigation button {
    padding: 10px 20px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.form-navigation .prev-step {
    background: #f5f5f5;
    color: #333;
}

.form-navigation .prev-step:hover {
    background: #e0e0e0;
}

.form-navigation .next-step {
    background: #3649e2;
    color: white;
}

.form-navigation .next-step:hover {
    background: #2a3ab9;
}

/* Form steps */
.form-step {
    display: none;
}

.form-step.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Any error message */
.any-error {
    color: #e74c3c;
    font-weight: bold;
}

/* reCAPTCHA styles */
.g-recaptcha-container {
    display: inline-block;
    margin: 20px 0;
    padding: 10px;
    border-radius: 4px;
}

.g-recaptcha-container.error {
    border: 1px solid #e74c3c;
    background-color: rgba(231, 76, 60, 0.05);
}

.recaptcha-error-message {
    color: #e74c3c;
    font-size: 0.85em;
    margin-top: 5px;
    display: block;
}

/* Center reCAPTCHA */
.g-recaptcha {
    display: inline-block;
}