<?php
/**
 * Simple AJAX Handler
 * 
 * This file contains a simplified AJAX handler for testing purposes.
 */

// Add the AJAX action for both logged in and non-logged in users
add_action('wp_ajax_simple_test_action', 'simple_test_action_callback');
add_action('wp_ajax_nopriv_simple_test_action', 'simple_test_action_callback');

/**
 * Simple test action callback
 */
function simple_test_action_callback() {
    // Log the request
    error_log('Simple test action called');
    error_log('POST data: ' . print_r($_POST, true));
    
    // Always return success for testing
    wp_send_json_success(array(
        'message' => 'Success! Your test was received.',
        'data' => $_POST
    ));
    
    // This is required to terminate properly
    wp_die();
}

// Enqueue the script
function enqueue_simple_test_script() {
    wp_enqueue_script(
        'simple-test-script',
        get_template_directory_uri() . '/simple-test-script.js',
        array('jquery'),
        time(),
        true
    );
    
    wp_localize_script(
        'simple-test-script',
        'simple_test_vars',
        array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('simple_test_nonce')
        )
    );
}
add_action('wp_enqueue_scripts', 'enqueue_simple_test_script');