<?php
/**
 * Direct AJAX Handler
 * 
 * This file handles AJAX requests directly without using WordPress hooks.
 */

// Load WordPress
require_once('../../../../wp-load.php');

// Set the content type to JSON
header('Content-Type: application/json');

// Log the request
error_log('Direct AJAX handler called');
error_log('POST data: ' . print_r($_POST, true));

// Check if this is an AJAX request
if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
    // This is an AJAX request
    $response = array(
        'success' => true,
        'message' => 'Direct AJAX handler received your request',
        'data' => $_POST
    );
    
    echo json_encode($response);
} else {
    // This is not an AJAX request
    $response = array(
        'success' => false,
        'message' => 'This endpoint only accepts AJAX requests'
    );
    
    echo json_encode($response);
}

// Exit to prevent WordPress from continuing
exit;