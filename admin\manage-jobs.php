<?php
/**
 * Admin - Manage Jobs
 * 
 * This page allows administrators to manage job postings on the Nkhwazi Primary School website.
 * It includes functionality to view, create, edit, and delete job postings.
 * 
 * <AUTHOR>
 */

// Include necessary files
require_once('../includes/config.php');
require_once('../includes/auth.php');
require_once('../includes/jobs-functions.php');

// Check if user is logged in and has admin privileges
if (!isLoggedIn() || !isAdmin()) {
    header('Location: login.php');
    exit;
}

// Initialize variables
$action = isset($_GET['action']) ? $_GET['action'] : 'list';
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$errors = [];
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $jobData = [
        'title' => trim($_POST['title'] ?? ''),
        'reference' => trim($_POST['reference'] ?? ''),
        'excerpt' => trim($_POST['excerpt'] ?? ''),
        'description' => $_POST['description'] ?? '',
        'deadline' => trim($_POST['deadline'] ?? ''),
        'how_to_apply' => trim($_POST['how_to_apply'] ?? '')
    ];
    
    // Validate form data
    $errors = validateJobData($jobData);
    
    if (empty($errors)) {
        // Process form based on action
        switch ($action) {
            case 'add':
                if (createJob($jobData)) {
                    $success = 'Job posting created successfully';
                    // Redirect to list view after successful creation
                    header('Location: manage-jobs.php?success=' . urlencode($success));
                    exit;
                } else {
                    $errors['general'] = 'Failed to create job posting';
                }
                break;
                
            case 'edit':
                if (updateJob($id, $jobData)) {
                    $success = 'Job posting updated successfully';
                    // Redirect to list view after successful update
                    header('Location: manage-jobs.php?success=' . urlencode($success));
                    exit;
                } else {
                    $errors['general'] = 'Failed to update job posting';
                }
                break;
                
            case 'delete':
                if (deleteJob($id)) {
                    $success = 'Job posting deleted successfully';
                    // Redirect to list view after successful deletion
                    header('Location: manage-jobs.php?success=' . urlencode($success));
                    exit;
                } else {
                    $errors['general'] = 'Failed to delete job posting';
                }
                break;
                
            case 'archive':
                if (archiveJob($id)) {
                    $success = 'Job posting archived successfully';
                    // Redirect to list view after successful archiving
                    header('Location: manage-jobs.php?success=' . urlencode($success));
                    exit;
                } else {
                    $errors['general'] = 'Failed to archive job posting';
                }
                break;
        }
    }
}

// Get job data for editing
$job = null;
if ($action === 'edit' && $id > 0) {
    $job = getJobById($id);
    if (!$job) {
        header('Location: manage-jobs.php?error=' . urlencode('Job posting not found'));
        exit;
    }
}

// Get all jobs for listing
$jobs = [];
if ($action === 'list') {
    // Get all jobs, including inactive ones for admin view
    $sql = "SELECT * FROM jobs ORDER BY created_at DESC";
    $result = $conn->query($sql);
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $jobs[] = $row;
        }
    }
}

// Include header
require_once('../includes/admin-header.php');
?>

<div class="admin-container">
    <h1>Manage Job Postings</h1>
    
    <?php if (!empty($success) || isset($_GET['success'])): ?>
        <div class="alert alert-success">
            <?php echo htmlspecialchars($success ?: $_GET['success']); ?>
        </div>
    <?php endif; ?>
    
    <?php if (!empty($errors['general']) || isset($_GET['error'])): ?>
        <div class="alert alert-danger">
            <?php echo htmlspecialchars($errors['general'] ?? $_GET['error']); ?>
        </div>
    <?php endif; ?>
    
    <?php if ($action === 'list'): ?>
        <!-- Job Listings -->
        <div class="admin-actions">
            <a href="?action=add" class="btn btn-primary">Add New Job</a>
        </div>
        
        <table class="admin-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Title</th>
                    <th>Reference</th>
                    <th>Deadline</th>
                    <th>Status</th>
                    <th>Created</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($jobs)): ?>
                    <tr>
                        <td colspan="7">No job postings found</td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($jobs as $job): ?>
                        <tr class="<?php echo $job['is_active'] ? '' : 'inactive'; ?>">
                            <td><?php echo $job['id']; ?></td>
                            <td><?php echo htmlspecialchars($job['title']); ?></td>
                            <td><?php echo htmlspecialchars($job['reference']); ?></td>
                            <td><?php echo htmlspecialchars($job['deadline']); ?></td>
                            <td><?php echo $job['is_active'] ? 'Active' : 'Archived'; ?></td>
                            <td><?php echo date('Y-m-d', strtotime($job['created_at'])); ?></td>
                            <td class="actions">
                                <a href="?action=edit&id=<?php echo $job['id']; ?>" class="btn btn-sm btn-primary">Edit</a>
                                <?php if ($job['is_active']): ?>
                                    <a href="?action=archive&id=<?php echo $job['id']; ?>" class="btn btn-sm btn-warning" onclick="return confirm('Are you sure you want to archive this job posting?')">Archive</a>
                                <?php endif; ?>
                                <a href="?action=delete&id=<?php echo $job['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this job posting? This action cannot be undone.')">Delete</a>
                                <a href="../page-job-detail.php?id=<?php echo $job['id']; ?>" class="btn btn-sm btn-info" target="_blank">View</a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    <?php elseif ($action === 'add' || $action === 'edit'): ?>
        <!-- Job Form -->
        <div class="admin-actions">
            <a href="manage-jobs.php" class="btn btn-secondary">Back to List</a>
        </div>
        
        <form method="post" action="?action=<?php echo $action; ?><?php echo $action === 'edit' ? '&id=' . $id : ''; ?>" class="admin-form">
            <div class="form-group">
                <label for="title">Job Title (3-30 characters):</label>
                <input type="text" id="title" name="title" class="form-control <?php echo isset($errors['title']) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($job['title'] ?? $_POST['title'] ?? ''); ?>" required minlength="3" maxlength="30">
                <?php if (isset($errors['title'])): ?>
                    <div class="invalid-feedback"><?php echo $errors['title']; ?></div>
                <?php endif; ?>
            </div>
            
            <div class="form-group">
                <label for="reference">Job Reference (2-20 characters):</label>
                <input type="text" id="reference" name="reference" class="form-control <?php echo isset($errors['reference']) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($job['reference'] ?? $_POST['reference'] ?? ''); ?>" required minlength="2" maxlength="20">
                <?php if (isset($errors['reference'])): ?>
                    <div class="invalid-feedback"><?php echo $errors['reference']; ?></div>
                <?php endif; ?>
            </div>
            
            <div class="form-group">
                <label for="excerpt">Job Excerpt (100-160 characters):</label>
                <textarea id="excerpt" name="excerpt" class="form-control <?php echo isset($errors['excerpt']) ? 'is-invalid' : ''; ?>" required minlength="100" maxlength="160" rows="3"><?php echo htmlspecialchars($job['excerpt'] ?? $_POST['excerpt'] ?? ''); ?></textarea>
                <small class="form-text text-muted">Character count: <span id="excerpt-count">0</span>/160</small>
                <?php if (isset($errors['excerpt'])): ?>
                    <div class="invalid-feedback"><?php echo $errors['excerpt']; ?></div>
                <?php endif; ?>
            </div>
            
            <div class="form-group">
                <label for="deadline">Application Deadline (6-20 characters):</label>
                <input type="text" id="deadline" name="deadline" class="form-control <?php echo isset($errors['deadline']) ? 'is-invalid' : ''; ?>" value="<?php echo htmlspecialchars($job['deadline'] ?? $_POST['deadline'] ?? ''); ?>" required minlength="6" maxlength="20">
                <?php if (isset($errors['deadline'])): ?>
                    <div class="invalid-feedback"><?php echo $errors['deadline']; ?></div>
                <?php endif; ?>
            </div>
            
            <div class="form-group">
                <label for="how_to_apply">How to Apply (5-200 characters):</label>
                <textarea id="how_to_apply" name="how_to_apply" class="form-control <?php echo isset($errors['how_to_apply']) ? 'is-invalid' : ''; ?>" required minlength="5" maxlength="200" rows="3"><?php echo htmlspecialchars($job['how_to_apply'] ?? $_POST['how_to_apply'] ?? ''); ?></textarea>
                <small class="form-text text-muted">Character count: <span id="how-to-apply-count">0</span>/200</small>
                <?php if (isset($errors['how_to_apply'])): ?>
                    <div class="invalid-feedback"><?php echo $errors['how_to_apply']; ?></div>
                <?php endif; ?>
            </div>
            
            <div class="form-group">
                <label for="description">Job Description:</label>
                <textarea id="description" name="description" class="form-control wysiwyg-editor <?php echo isset($errors['description']) ? 'is-invalid' : ''; ?>" required rows="10"><?php echo htmlspecialchars($job['description'] ?? $_POST['description'] ?? ''); ?></textarea>
                <?php if (isset($errors['description'])): ?>
                    <div class="invalid-feedback"><?php echo $errors['description']; ?></div>
                <?php endif; ?>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn btn-primary"><?php echo $action === 'add' ? 'Create Job' : 'Update Job'; ?></button>
                <a href="manage-jobs.php" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
        
        <script>
            // Character counter for excerpt
            document.getElementById('excerpt').addEventListener('input', function() {
                document.getElementById('excerpt-count').textContent = this.value.length;
            });
            
            // Character counter for how to apply
            document.getElementById('how_to_apply').addEventListener('input', function() {
                document.getElementById('how-to-apply-count').textContent = this.value.length;
            });
            
            // Trigger initial count
            document.getElementById('excerpt').dispatchEvent(new Event('input'));
            document.getElementById('how_to_apply').dispatchEvent(new Event('input'));
        </script>
    <?php endif; ?>
</div>

<?php
// Include footer
require_once('../includes/admin-footer.php');
?>