# Legal Page Template

This template is designed for legal pages such as Privacy Policy, Terms of Use, Cookies Policy, and Disclaimer. It provides a consistent layout with a main content area (8 columns) and a sidebar (4 columns) that allows users to navigate between different legal pages.

## How to Use This Template

1. **Create Legal Pages**:
   - Go to WordPress Admin > Pages > Add New
   - Create separate pages for each legal document (Privacy Policy, Terms of Use, etc.)
   - Make sure to use the following slugs for consistency:
     - `privacy-policy`
     - `terms-of-use`
     - `cookies`
     - `disclaimer`

2. **Apply the Template**:
   - In the Page Editor, look for the "Page Attributes" section in the sidebar
   - From the "Template" dropdown, select "Legal Page"
   - Update/Publish the page

3. **Add Content**:
   - Add your legal content to each page
   - You can use the sample content provided in the theme's root directory as a starting point:
     - `sample-legal-content.html` (Privacy Policy example)
     - `sample-terms-content.html` (Terms of Use example)

4. **Update Footer Links**:
   - The footer already has links to these pages
   - If you've created the pages with the recommended slugs, the links will work automatically
   - Alternatively, you can create a menu and assign it to the "Legal Links (Footer)" location

## Template Features

- **Responsive Layout**: Works on all device sizes
- **Navigation Sidebar**: Allows users to switch between legal pages
- **Consistent Styling**: Maintains a professional appearance across all legal documents
- **Active Link Highlighting**: The current page is highlighted in the sidebar

## Customization

If you need to customize the template:

- The template file is located at: `page-templates/legal-page.php`
- The styles are in: `assets/css/custom-style.scss` (look for "Legal Pages Template Styles" section)

## Support

If you have any questions or need assistance with this template, please contact the theme developer.