<?php
/**
 * Template for displaying single extracurricular item posts
 * This template is specifically for the 'extracurricular_item' post type
 * Features a two-column layout with activity details and gallery.
 *
 * @package Nkhwazi_Primary_School
 */

// Debug - only visible to administrators
if (current_user_can('administrator')) {
    echo '<!-- DEBUG: Using single-extracurricular-item.php template -->';
}

get_header();

// Get the current post data
global $post;

// Get activity metadata
$activity_name = get_the_title();
$activity_excerpt = get_field('excerpt');
$activity_scope = get_field('activity_scope');
$activity_location = get_field('location');
$activity_date = get_field('date');
$header_image = get_the_post_thumbnail_url($post->ID, 'full');

// Get the extracurricular type (parent extracurricular)
$extracurricular_relation = get_field('extracurricular_type');
$extracurricular_type_id = '';
$extracurricular_type = '';
$extracurricular_type_slug = '';

// Relationship field returns an array of post objects
if (!empty($extracurricular_relation) && is_array($extracurricular_relation)) {
    // Get the first related post (since we limited to max 1 in the field settings)
    $extracurricular_post = reset($extracurricular_relation);
    if ($extracurricular_post && is_object($extracurricular_post)) {
        $extracurricular_type_id = $extracurricular_post->ID;
        $extracurricular_type = $extracurricular_post->post_title;
        $extracurricular_type_slug = $extracurricular_post->post_name;
    }
}

// Get gallery images
$gallery_images = get_field('item_gallery');
$activity_gallery = [];

if (!empty($gallery_images)) {
    foreach ($gallery_images as $image) {
        $activity_gallery[] = $image['url'];
    }
}

// Get page image
$page_image = get_field('page_image');

// Get related activities (other items from the same extracurricular type)
$related_activities = [];

if (!empty($extracurricular_type_id)) {
    $related_args = array(
        'post_type' => 'extracurricular_item',
        'posts_per_page' => 3,
        'post__not_in' => array($post->ID),
        'meta_query' => array(
            array(
                'key' => 'extracurricular_type',
                'value' => '"' . $extracurricular_type_id . '"',
                'compare' => 'LIKE'
            )
        )
    );
    
    $related_query = new WP_Query($related_args);
    
    if ($related_query->have_posts()) {
        while ($related_query->have_posts()) {
            $related_query->the_post();
            $related_id = get_the_ID();
            $related_activities[] = array(
                'id' => $related_id,
                'slug' => get_post_field('post_name', $related_id),
                'name' => get_the_title(),
                'card_image' => get_the_post_thumbnail_url($related_id, 'medium'),
                'activity_scope' => get_field('activity_scope', $related_id),
                'activity_date' => get_field('date', $related_id),
                'activity_excerpt' => get_field('excerpt', $related_id)
            );
        }
    }
    wp_reset_postdata();
}
?>

<!-- MAIN -->
<main role="main">
  <?php while (have_posts()) : the_post(); ?>
  <!-- Header Section -->
  <article>
    <div class="line">
      <header class="rounded-div section background-primary background-transparent text-center position-relative"
        data-image-src="<?php echo get_template_directory_uri(); ?>/assets/img/parallax-03.webp" style="padding-bottom: 40px;">
        <h1 class="text-uppercase text-white margin-bottom-20 text-thin text-line-height-1"><?php echo esc_html($activity_name); ?></h1>
        
      </header>
    </div>
    
    <!-- Page Image (displayed below title, spanning full 12 columns) -->
    <?php if (!empty($page_image)): ?>
    <div class="line margin-top-30 margin-bottom-30">
      <div class="s-12">
        <img src="<?php echo esc_url($page_image['url']); ?>" alt="<?php echo esc_attr($activity_name); ?> Page Image" class="full-width-img rounded-image extracurricular-page-image">
      </div>
    </div>
    <?php endif; ?>
    
    <!-- Full-width Featured Image (only shown if image exists) -->
    <?php if (!empty($header_image)): ?>
    <div class="line margin-top-30 margin-bottom-30">
      <div class="s-12">
        <img src="<?php echo esc_url($header_image); ?>" alt="<?php echo esc_attr($activity_name); ?>" class="full-width-img rounded-image">
      </div>
    </div>
    <?php endif; ?>

    <!-- Main Content Section -->
    <div class="section background-white">
      <div class="line">
        <!-- Two-column layout -->
        <div class="grid margin">
          <!-- Left Column: Activity Details -->
          <div class="s-12 m-6 l-7">
            <!-- Activity Overview -->
            <div class="margin-bottom-30">
              <h2 class="text-strong text-uppercase margin-bottom-20"><?php echo esc_html($activity_name); ?></h2>
              
              <!-- Activity Summary Table with alternating row backgrounds -->
              <div class="activity-summary-container rounded-div margin-bottom-15 box-shadow">
                <table class="activity-summary-table">
                  <tbody>
                    <!-- Excerpt as first row -->
                    <?php if (!empty($activity_excerpt)) : ?>
                    <tr class="table-row-odd">
                      <td colspan="2" class="activity-excerpt-cell">
                        <div class="text-dark activity-excerpt"><?php echo esc_html($activity_excerpt); ?></div>
                      </td>
                    </tr>
                    <?php endif; ?>
                    
                    <!-- Location -->
                    <?php if (!empty($activity_location)): ?>
                    <tr class="table-row-even">
                      <th>Held At:</th>
                      <td><?php echo esc_html($activity_location); ?></td>
                    </tr>
                    <?php endif; ?>
                    
                    <!-- Extracurricular Category -->
                    <?php if (!empty($extracurricular_type)): ?>
                    <tr class="table-row-odd">
                      <th>Extracurricular Category:</th>
                      <td>
                        <a href="<?php echo esc_url(get_permalink($extracurricular_type_id)); ?>" class="category-link">
                          <?php echo esc_html($extracurricular_type); ?>
                        </a>
                      </td>
                    </tr>
                    <?php endif; ?>
                    
                    <!-- Date -->
                    <?php if (!empty($activity_date)): ?>
                    <tr class="table-row-even">
                      <th>Date:</th>
                      <td><?php echo esc_html($activity_date); ?></td>
                    </tr>
                    <?php endif; ?>
                    
                    <!-- Activity Scope -->
                    <?php if (!empty($activity_scope)): ?>
                    <tr class="table-row-odd">
                      <th>Activity Type:</th>
                      <td>
                        <?php 
                        // Display 'Outside School' for external, 'Inside School' for internal
                        echo esc_html(strtolower($activity_scope) === 'external' ? 'Outside School' : 'Inside School'); 
                        ?>
                      </td>
                    </tr>
                    <?php endif; ?>
                  </tbody>
                </table>
              </div>
              
              <!-- Main Content Area -->
              <div class="activity-content margin-top-30">
                <?php the_content(); ?>
              </div>
              
              <!-- Action Buttons -->
              <div class="s-12 m-12 l-12 margin-top-30">
                <?php if (!empty($extracurricular_type_id)) : ?>
                <a class="s-12 m-6 l-4 center button rounded-btn s-12 margin-bottom text-white background-primary" href="<?php echo esc_url(get_permalink($extracurricular_type_id)); ?>">Back to <?php echo esc_html($extracurricular_type); ?></a>
                <?php else : ?>
                <a class="s-12 m-6 l-4 center button rounded-btn s-12 margin-bottom text-white background-primary" href="<?php echo esc_url(get_permalink(361)); ?>">Back to All Activities</a>
                <?php endif; ?>
                     
              </div>
            </div>
          </div>
          
          <!-- Right Column: Gallery -->
          <div class="s-12 m-6 l-5 text-center">
            <!-- Gallery Section -->
            <div class="margin-bottom-30">
              <h2 class="text-padding-small rounded-div background-primary text-white text-strong text-uppercase margin-bottom-20">
                Activity Gallery
              </h2>
              
              <?php if (!empty($activity_gallery)): ?>
              <div class="grid margin">
                <?php foreach ($activity_gallery as $index => $image): ?>
                <div class="s-12 m-6 l-6 margin-m-bottom">
                  <a href="<?php echo esc_url($image); ?>" class="lightbox">
                    <img src="<?php echo esc_url($image); ?>" alt="<?php echo esc_attr($activity_name); ?> Image <?php echo $index + 1; ?>" class="rounded-image">
                  </a>
                </div>
                <?php endforeach; ?>
              </div>
              <?php else: ?>
              <p class="text-center">No gallery images available for this activity.</p>
              <?php endif; ?>
            </div>
            
            <!-- Related Activities -->
            <?php if (!empty($related_activities)): ?>
            <div>
              <h3 class="margin-bottom-20 margin-top-20 text-strong text-center">Related Activities</h3>
              
              <!-- Single rounded corner div containing all related activities -->
              <div class="activities-list rounded-div background-primary-hightlight box-shadow padding-10">
                <?php foreach ($related_activities as $index => $related_activity): ?>
                  <!-- Simplified Activity Link (Title and Date only) -->
                  <a href="<?php echo esc_url(get_permalink($related_activity['id'])); ?>" class="activity-link <?php echo ($index < count($related_activities) - 1) ? 'border-bottom' : ''; ?>">
                    <h4 class="text-strong margin-bottom-5"><?php echo esc_html($related_activity['name']); ?></h4>
                    <?php if (!empty($related_activity['activity_date'])) : ?>
                    <p class="text-size-12 text-grey margin-bottom-0"><?php echo esc_html($related_activity['activity_date']); ?></p>
                    <?php endif; ?>
                  </a>
                <?php endforeach; ?>
              </div>
            </div>
            <?php endif; ?>
          </div>
        </div>
      </div>
    </div>
  </article>
  <?php endwhile; ?>
</main>

<?php
// We no longer need the custom JavaScript since we're using direct links
function nkhwazi_activity_card_script() {
  // JavaScript removed as it's no longer needed with the new structure
  // The links are now direct and don't require JavaScript to make them clickable
}
add_action('wp_footer', 'nkhwazi_activity_card_script');

get_footer();
?>