<?php
/**
 * Template Name: Single School Category
 * Template Post Type: page
 * Description: A template for displaying detailed information about a specific school category.
 * Includes a sidebar with links to other categories.
 * Author: <PERSON>
 */

get_header();

// Get the category from URL parameter
$category = isset($_GET['category']) ? $_GET['category'] : 'lower';

// Set default values
$category_title = 'Lower School';
$category_image = get_template_directory_uri() . '/assets/img/img-08.jpg';
$category_age_range = '5-7';
$category_grades = 'Reception to Grade 2';
$category_description = 'Our Lower School provides a nurturing environment for our youngest learners, focusing on foundational skills and social development.';

// Update values based on category parameter
if ($category == 'middle') {
  $category_title = 'Middle School';
  $category_image = get_template_directory_uri() . '/assets/img/img-09.jpg';
  $category_age_range = '8-9';
  $category_grades = 'Grade 3 to 4';
  $category_description = 'The Middle School builds on foundational skills and introduces more complex concepts while supporting students through this important developmental stage.';
} elseif ($category == 'upper') {
  $category_title = 'Upper School';
  $category_image = get_template_directory_uri() . '/assets/img/img-09.jpg';
  $category_age_range = '10-12';
  $category_grades = 'Grade 5 to 7';
  $category_description = 'Our Upper School prepares students for secondary education with a focus on academic excellence, critical thinking, and leadership skills.';
}
?>

<!-- MAIN -->
<main role="main">
  <!-- third section -->
  <article>
    <div class="line">
      <header class="rounded-div section background-primary background-transparent text-center"
        data-image-src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/parallax-03.webp">
        <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1"><?php echo esc_html($category_title); ?></h1>
      </header>
    </div>
    
    <!-- Full-width Category Image (only shown if image exists) -->
    <?php 
    $category_page_image = get_template_directory_uri() . '/assets/img/page-images/single-category.jpg';
    if (!empty($category_page_image)): 
    ?>
    <div class="line margin-top-30 margin-bottom-30">
      <div class="s-12">
        <img src="<?php echo esc_url($category_page_image); ?>" alt="<?php echo esc_attr($category_title); ?>" class="full-width-img rounded-image">
      </div>
    </div>
    <?php endif; ?>

    <div class="section background-white">
      <div class="line">
        <div class="margin">
          <div class="s-12 m-8 l-9">
            <!-- Category content -->
            <div class="line">
              <div class="margin">
                
                <!-- Category Overview -->
                <div class="s-12 margin-bottom-30">
                  <h2 class="text-padding-small background-primary text-white text-strong text-uppercase margin-bottom-30">
                    <i class="icon-book margin-right-10"></i><?php echo esc_html($category_title); ?> Details
                  </h2>
                  
                  <div class="margin-bottom-15">
                    <p><strong>Age Range:</strong> <?php echo esc_html($category_age_range); ?> years</p>
                    <p><strong>Grades:</strong> <?php echo esc_html($category_grades); ?></p>
                  </div>
                  
                  <p><?php echo esc_html($category_description); ?></p>
                </div>
                
                <!-- Curriculum -->
                <div class="s-12 margin-bottom-30">
                  <h3 class="text-strong">Curriculum</h3>
                  <p>Our curriculum is designed to meet the specific developmental needs of students in this age group. We focus on:</p>
                  <ul>
                    <li>Core subjects: Mathematics, English, Science, and Social Studies</li>
                    <li>Specialized subjects: Art, Music, Physical Education, and Technology</li>
                    <li>Character development and social skills</li>
                    <li>Age-appropriate projects and activities</li>
                  </ul>
                </div>
                
                <!-- Teaching Approach -->
                <div class="s-12 margin-bottom-30">
                  <h3 class="text-strong">Teaching Approach</h3>
                  <p>Our experienced teachers use a variety of teaching methods to engage students and support their learning:</p>
                  <ul>
                    <li>Hands-on activities and experiential learning</li>
                    <li>Collaborative group work</li>
                    <li>Individual instruction and support</li>
                    <li>Technology integration</li>
                    <li>Regular assessment and feedback</li>
                  </ul>
                </div>
                
                <!-- Daily Schedule -->
                <div class="s-12 margin-bottom-30">
                  <h3 class="text-strong">Daily Schedule</h3>
                  <p>A typical day in the <?php echo esc_html($category_title); ?> includes:</p>
                  <ul>
                    <li>Morning assembly</li>
                    <li>Core subject instruction</li>
                    <li>Recess and lunch breaks</li>
                    <li>Specialized subject classes</li>
                    <li>Homework review and preparation</li>
                    <li>After-school activities (optional)</li>
                  </ul>
                </div>
                
                <!-- Meet the Teachers -->
                <div class="s-12 margin-bottom-30">
                  <h3 class="text-strong">Meet the Teachers</h3>
                  <p>Our <?php echo esc_html($category_title); ?> teachers are highly qualified professionals dedicated to providing the best education for your child.</p>
                  <div class="margin-top-bottom-20 center">
                    <a class="text-more-info" href="<?php echo esc_url(home_url('/teaching-staff/')); ?>">View Teaching Staff</a>
                  </div>
                </div>
              </div>
            </div>
          </div><!-- Ends content -->

          <!-- Category Sidebar -->
          <?php get_template_part('includes/category-sidebar'); ?>

        </div>
      </div>
    </div>
  </article>
</main>

<?php get_footer(); ?>