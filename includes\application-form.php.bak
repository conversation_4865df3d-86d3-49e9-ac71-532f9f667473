<?php
/**
 * Application Form Functionality
 *
 * @package NkhwaziSchool
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * Register Application Custom Post Type
 */
function nkhwazi_register_application_cpt() {
    // Log the registration attempt
    error_log('Registering school_application custom post type');
    
    $args = array(
        'labels' => array(
            'name'               => __('Applications', 'nkhwazischool'),
            'singular_name'      => __('Application', 'nkhwazischool'),
            'add_new'            => __('Add New', 'nkhwazischool'),
            'add_new_item'       => __('Add New Application', 'nkhwazischool'),
            'edit_item'          => __('Edit Application', 'nkhwazischool'),
            'new_item'           => __('New Application', 'nkhwazischool'),
            'view_item'          => __('View Application', 'nkhwazischool'),
            'search_items'       => __('Search Applications', 'nkhwazischool'),
            'not_found'          => __('No applications found', 'nkhwazischool'),
            'not_found_in_trash' => __('No applications found in Trash', 'nkhwazischool'),
            'menu_name'          => __('Applications', 'nkhwazischool'),
        ),
        'public'              => true,  // Changed to true for testing
        'publicly_queryable'  => true,  // Changed to true for testing
        'show_ui'             => true,
        'show_in_menu'        => true,
        'show_in_admin_bar'   => true,  // Added for visibility
        'show_in_rest'        => true,  // Changed to true for better compatibility
        'query_var'           => true,  // Changed to true for testing
        'rewrite'             => array('slug' => 'applications'),  // Added proper rewrite
        'capability_type'     => array('school_application', 'school_applications'),
        'map_meta_cap'        => true,  // Important for proper capability mapping
        'has_archive'         => true,  // Changed to true for testing
        'hierarchical'        => false,
        'menu_position'       => 30,
        'menu_icon'           => 'dashicons-welcome-learn-more',
        'supports'            => array('title', 'custom-fields'),  // Added custom-fields support
    );
    
    $result = register_post_type('school_application', $args);
    
    if (is_wp_error($result)) {
        error_log('Error registering post type: ' . $result->get_error_message());
    } else {
        error_log('Post type registered successfully');
    }
}
add_action('init', 'nkhwazi_register_application_cpt');

/**
 * Check if the application post type is registered
 */
function nkhwazi_check_application_cpt() {
    $post_types = get_post_types(array(), 'names');
    error_log('Registered post types: ' . print_r($post_types, true));
    
    if (isset($post_types['school_application'])) {
        error_log('school_application post type is registered');
    } else {
        error_log('school_application post type is NOT registered');
    }
}
add_action('admin_init', 'nkhwazi_check_application_cpt');

/**
 * Check if there are any application posts
 */
function nkhwazi_check_application_posts() {
    $query = new WP_Query(array(
        'post_type' => 'school_application',
        'posts_per_page' => -1,
    ));
    
    error_log('Found ' . $query->found_posts . ' school_application posts');
    
    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            error_log('Application post: ' . get_the_title() . ' (ID: ' . get_the_ID() . ')');
        }
        wp_reset_postdata();
    }
}
add_action('admin_init', 'nkhwazi_check_application_posts');

/**
 * Create a test application post
 */
function nkhwazi_create_test_application() {
    // Only run this once
    if (get_option('nkhwazi_test_application_created')) {
        return;
    }
    
    error_log('Creating test application post');
    
    $post_id = wp_insert_post(array(
        'post_title' => 'Test Application - ' . date('Y-m-d H:i:s'),
        'post_type' => 'school_application',
        'post_status' => 'publish'
    ));
    
    if (is_wp_error($post_id)) {
        error_log('Error creating test application: ' . $post_id->get_error_message());
    } else {
        error_log('Test application created with ID: ' . $post_id);
        
        // Add some test meta data
        update_post_meta($post_id, '_pupil_fname', 'Test');
        update_post_meta($post_id, '_pupil_lname', 'Student');
        update_post_meta($post_id, '_pupil_grade', 'Grade 5');
        update_post_meta($post_id, '_father_fname', 'Test');
        update_post_meta($post_id, '_father_lname', 'Father');
        update_post_meta($post_id, '_mother_fname', 'Test');
        update_post_meta($post_id, '_mother_lname', 'Mother');
        update_post_meta($post_id, '_emergency_name', 'Emergency Contact');
        
        // Mark as created
        update_option('nkhwazi_test_application_created', true);
    }
}
add_action('admin_init', 'nkhwazi_create_test_application');

/**
 * Flush rewrite rules when needed
 */
function nkhwazi_flush_rewrite_rules() {
    // Only flush once
    if (get_option('nkhwazi_rewrite_rules_flushed')) {
        return;
    }
    
    error_log('Flushing rewrite rules');
    flush_rewrite_rules();
    update_option('nkhwazi_rewrite_rules_flushed', true);
}
add_action('admin_init', 'nkhwazi_flush_rewrite_rules');

/**
 * Check current user capabilities
 */
function nkhwazi_check_user_capabilities() {
    if (!current_user_can('administrator')) {
        return;
    }
    
    $user = wp_get_current_user();
    error_log('Current user: ' . $user->user_login . ' (ID: ' . $user->ID . ')');
    error_log('User roles: ' . implode(', ', $user->roles));
    
    $capabilities = array(
        'edit_posts',
        'edit_others_posts',
        'publish_posts',
        'read_private_posts',
        'edit_school_application',
        'edit_school_applications',
        'edit_others_school_applications',
        'publish_school_applications',
        'read_private_school_applications'
    );
    
    foreach ($capabilities as $cap) {
        error_log('User can ' . $cap . ': ' . (current_user_can($cap) ? 'Yes' : 'No'));
    }
}
add_action('admin_init', 'nkhwazi_check_user_capabilities');

/**
 * Reset test flags (for debugging)
 */
function nkhwazi_reset_test_flags() {
    // Only run this if the reset parameter is present
    if (!isset($_GET['reset_nkhwazi_tests']) || $_GET['reset_nkhwazi_tests'] !== '1') {
        return;
    }
    
    if (!current_user_can('administrator')) {
        return;
    }
    
    error_log('Resetting test flags');
    delete_option('nkhwazi_test_application_created');
    delete_option('nkhwazi_rewrite_rules_flushed');
    delete_option('nkhwazi_admin_capabilities_added');
    
    // Redirect to remove the parameter from the URL
    wp_redirect(remove_query_arg('reset_nkhwazi_tests'));
    exit;
}
add_action('admin_init', 'nkhwazi_reset_test_flags');

/**
 * Add custom capabilities to administrator role
 */
function nkhwazi_add_admin_capabilities() {
    // Only run this once
    if (get_option('nkhwazi_admin_capabilities_added')) {
        return;
    }
    
    error_log('Adding custom capabilities to administrator role');
    
    $admin_role = get_role('administrator');
    
    if (!$admin_role) {
        error_log('Administrator role not found');
        return;
    }
    
    // Add custom capabilities
    $capabilities = array(
        'edit_school_application',
        'edit_school_applications',
        'edit_others_school_applications',
        'publish_school_applications',
        'read_school_application',
        'read_private_school_applications',
        'delete_school_application',
        'delete_school_applications'
    );
    
    foreach ($capabilities as $cap) {
        $admin_role->add_cap($cap);
        error_log('Added capability: ' . $cap);
    }
    
    update_option('nkhwazi_admin_capabilities_added', true);
}
add_action('admin_init', 'nkhwazi_add_admin_capabilities');

/**
 * Add meta boxes for applications
 */
function nkhwazi_application_meta_boxes() {
    add_meta_box(
        'nkhwazi_application_details',
        __('Application Details', 'nkhwazischool'),
        'nkhwazi_application_details_callback',
        'school_application',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'nkhwazi_application_meta_boxes');

/**
 * Application details meta box callback
 */
function nkhwazi_application_details_callback($post) {
    // Get application details - Pupil's details
    $lname = get_post_meta($post->ID, '_pupil_lname', true);
    $fname = get_post_meta($post->ID, '_pupil_fname', true);
    $sex = get_post_meta($post->ID, '_pupil_sex', true);
    $dob = get_post_meta($post->ID, '_pupil_dob', true);
    $nationality = get_post_meta($post->ID, '_pupil_nationality', true);
    $grade = get_post_meta($post->ID, '_pupil_grade', true);
    $previous_school = get_post_meta($post->ID, '_pupil_previous_school', true);
    $handicap = get_post_meta($post->ID, '_pupil_handicap', true);
    
    // Father's details
    $lname_f = get_post_meta($post->ID, '_father_lname', true);
    $fname_f = get_post_meta($post->ID, '_father_fname', true);
    $nationality_f = get_post_meta($post->ID, '_father_nationality', true);
    $identity_f = get_post_meta($post->ID, '_father_identity', true);
    $occupation_f = get_post_meta($post->ID, '_father_occupation', true);
    $employer_f = get_post_meta($post->ID, '_father_employer', true);
    $address_f = get_post_meta($post->ID, '_father_address', true);
    $address_postal_f = get_post_meta($post->ID, '_father_address_postal', true);
    $email_f = get_post_meta($post->ID, '_father_email', true);
    $phone_f = get_post_meta($post->ID, '_father_phone', true);
    $phone_work_f = get_post_meta($post->ID, '_father_phone_work', true);
    $res_status_f = get_post_meta($post->ID, '_father_res_status', true);
    
    // Mother's details
    $lname_m = get_post_meta($post->ID, '_mother_lname', true);
    $fname_m = get_post_meta($post->ID, '_mother_fname', true);
    $nationality_m = get_post_meta($post->ID, '_mother_nationality', true);
    $identity_m = get_post_meta($post->ID, '_mother_identity', true);
    $occupation_m = get_post_meta($post->ID, '_mother_occupation', true);
    $employer_m = get_post_meta($post->ID, '_mother_employer', true);
    $address_m = get_post_meta($post->ID, '_mother_address', true);
    $address_postal_m = get_post_meta($post->ID, '_mother_address_postal', true);
    $email_m = get_post_meta($post->ID, '_mother_email', true);
    $phone_m = get_post_meta($post->ID, '_mother_phone', true);
    $phone_work_m = get_post_meta($post->ID, '_mother_phone_work', true);
    $res_status_m = get_post_meta($post->ID, '_mother_res_status', true);
    
    // Emergency contact details
    $emergency_name = get_post_meta($post->ID, '_emergency_name', true);
    $emergency_relationship = get_post_meta($post->ID, '_emergency_relationship', true);
    $emergency_phone = get_post_meta($post->ID, '_emergency_phone', true);
    
    // Submission details
    $date = get_the_date('F j, Y \a\t g:i a', $post->ID);
    $ip = get_post_meta($post->ID, '_application_ip', true);
    
    // Output application details in tabs
    ?>
    <style>
        .application-tabs {
            border-bottom: 1px solid #ccc;
            margin-bottom: 20px;
            overflow: hidden;
        }
        .application-tabs button {
            background-color: #f1f1f1;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 10px 16px;
            transition: 0.3s;
            font-size: 14px;
            border-radius: 4px 4px 0 0;
            margin-right: 5px;
        }
        .application-tabs button:hover {
            background-color: #ddd;
        }
        .application-tabs button.active {
            background-color: #0073aa;
            color: white;
        }
        .tab-content {
            display: none;
            padding: 15px;
            border: 1px solid #ccc;
            border-top: none;
            animation: fadeEffect 1s;
        }
        @keyframes fadeEffect {
            from {opacity: 0;}
            to {opacity: 1;}
        }
        .tab-content.active {
            display: block;
        }
        .application-field {
            margin-bottom: 15px;
        }
        .application-field label {
            font-weight: bold;
            display: block;
            margin-bottom: 5px;
        }
        .application-field-value {
            padding: 8px;
            background: #f9f9f9;
            border: 1px solid #e5e5e5;
        }
        .application-meta {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px dashed #ccc;
            font-size: 12px;
            color: #666;
        }
        
        /* Additional styles for better tab handling */
        .application-tabs {
            position: relative;
            z-index: 10;
        }
        .tab-content {
            position: relative;
            z-index: 5;
        }
    </style>
    
    <div class="application-tabs">
        <button class="tablinks" data-tab="pupil-details">Pupil's Details</button>
        <button class="tablinks" data-tab="father-details">Father's Details</button>
        <button class="tablinks" data-tab="mother-details">Mother's Details</button>
        <button class="tablinks" data-tab="emergency-details">Emergency Contact</button>
    </div>
    
    <!-- Pupil's Details Tab -->
    <div id="pupil-details" class="tab-content active">
        <h3>Pupil's Details</h3>
        
        <div class="application-field">
            <label>Full Name:</label>
            <div class="application-field-value"><?php echo esc_html($fname . ' ' . $lname); ?></div>
        </div>
        
        <div class="application-field">
            <label>Sex:</label>
            <div class="application-field-value"><?php echo esc_html($sex === 'M' ? 'Male' : 'Female'); ?></div>
        </div>
        
        <div class="application-field">
            <label>Date of Birth:</label>
            <div class="application-field-value"><?php echo esc_html($dob); ?></div>
        </div>
        
        <div class="application-field">
            <label>Nationality:</label>
            <div class="application-field-value"><?php echo esc_html($nationality); ?></div>
        </div>
        
        <div class="application-field">
            <label>Grade Applied For:</label>
            <div class="application-field-value"><?php echo esc_html($grade); ?></div>
        </div>
        
        <div class="application-field">
            <label>Previous School:</label>
            <div class="application-field-value"><?php echo esc_html($previous_school); ?></div>
        </div>
        
        <div class="application-field">
            <label>Special Needs/Handicap:</label>
            <div class="application-field-value"><?php echo !empty($handicap) ? esc_html($handicap) : 'None'; ?></div>
        </div>
    </div>
    
    <!-- Father's Details Tab -->
    <div id="father-details" class="tab-content">
        <h3>Father's Details</h3>
        
        <div class="application-field">
            <label>Full Name:</label>
            <div class="application-field-value"><?php echo esc_html($fname_f . ' ' . $lname_f); ?></div>
        </div>
        
        <div class="application-field">
            <label>Nationality:</label>
            <div class="application-field-value"><?php echo esc_html($nationality_f); ?></div>
        </div>
        
        <div class="application-field">
            <label>ID/Passport Number:</label>
            <div class="application-field-value"><?php echo esc_html($identity_f); ?></div>
        </div>
        
        <div class="application-field">
            <label>Occupation:</label>
            <div class="application-field-value"><?php echo esc_html($occupation_f); ?></div>
        </div>
        
        <div class="application-field">
            <label>Employer:</label>
            <div class="application-field-value"><?php echo esc_html($employer_f); ?></div>
        </div>
        
        <div class="application-field">
            <label>Physical Address:</label>
            <div class="application-field-value"><?php echo esc_html($address_f); ?></div>
        </div>
        
        <?php if (!empty($address_postal_f)) : ?>
        <div class="application-field">
            <label>Postal Address:</label>
            <div class="application-field-value"><?php echo esc_html($address_postal_f); ?></div>
        </div>
        <?php endif; ?>
        
        <div class="application-field">
            <label>Email Address:</label>
            <div class="application-field-value"><?php echo esc_html($email_f); ?></div>
        </div>
        
        <div class="application-field">
            <label>Phone Number:</label>
            <div class="application-field-value"><?php echo esc_html($phone_f); ?></div>
        </div>
        
        <?php if (!empty($phone_work_f)) : ?>
        <div class="application-field">
            <label>Work Phone:</label>
            <div class="application-field-value"><?php echo esc_html($phone_work_f); ?></div>
        </div>
        <?php endif; ?>
        
        <div class="application-field">
            <label>Residence Status:</label>
            <div class="application-field-value"><?php echo esc_html($res_status_f); ?></div>
        </div>
    </div>
    
    <!-- Mother's Details Tab -->
    <div id="mother-details" class="tab-content">
        <h3>Mother's Details</h3>
        
        <div class="application-field">
            <label>Full Name:</label>
            <div class="application-field-value"><?php echo esc_html($fname_m . ' ' . $lname_m); ?></div>
        </div>
        
        <div class="application-field">
            <label>Nationality:</label>
            <div class="application-field-value"><?php echo esc_html($nationality_m); ?></div>
        </div>
        
        <div class="application-field">
            <label>ID/Passport Number:</label>
            <div class="application-field-value"><?php echo esc_html($identity_m); ?></div>
        </div>
        
        <div class="application-field">
            <label>Occupation:</label>
            <div class="application-field-value"><?php echo esc_html($occupation_m); ?></div>
        </div>
        
        <div class="application-field">
            <label>Employer:</label>
            <div class="application-field-value"><?php echo esc_html($employer_m); ?></div>
        </div>
        
        <div class="application-field">
            <label>Physical Address:</label>
            <div class="application-field-value"><?php echo esc_html($address_m); ?></div>
        </div>
        
        <?php if (!empty($address_postal_m)) : ?>
        <div class="application-field">
            <label>Postal Address:</label>
            <div class="application-field-value"><?php echo esc_html($address_postal_m); ?></div>
        </div>
        <?php endif; ?>
        
        <div class="application-field">
            <label>Email Address:</label>
            <div class="application-field-value"><?php echo esc_html($email_m); ?></div>
        </div>
        
        <div class="application-field">
            <label>Phone Number:</label>
            <div class="application-field-value"><?php echo esc_html($phone_m); ?></div>
        </div>
        
        <?php if (!empty($phone_work_m)) : ?>
        <div class="application-field">
            <label>Work Phone:</label>
            <div class="application-field-value"><?php echo esc_html($phone_work_m); ?></div>
        </div>
        <?php endif; ?>
        
        <div class="application-field">
            <label>Residence Status:</label>
            <div class="application-field-value"><?php echo esc_html($res_status_m); ?></div>
        </div>
    </div>
    
    <!-- Emergency Contact Tab -->
    <div id="emergency-details" class="tab-content">
        <h3>Emergency Contact</h3>
        
        <div class="application-field">
            <label>Name:</label>
            <div class="application-field-value"><?php echo esc_html($emergency_name); ?></div>
        </div>
        
        <div class="application-field">
            <label>Relationship to Pupil:</label>
            <div class="application-field-value"><?php echo esc_html($emergency_relationship); ?></div>
        </div>
        
        <div class="application-field">
            <label>Phone Number:</label>
            <div class="application-field-value"><?php echo esc_html($emergency_phone); ?></div>
        </div>
    </div>
    
    <!-- Application Meta Information -->
    <div class="application-meta">
        <p><strong>Submitted:</strong> <?php echo esc_html($date); ?></p>
        <p><strong>IP Address:</strong> <?php echo esc_html($ip); ?></p>
    </div>
    
    <script>
    jQuery(document).ready(function($) {
        // Store the active tab in localStorage if available
        function setActiveTab(tabName) {
            if (typeof(Storage) !== "undefined") {
                localStorage.setItem('nkhwazi_active_tab', tabName);
            }
        }
        
        // Get the active tab from localStorage if available
        function getActiveTab() {
            if (typeof(Storage) !== "undefined") {
                return localStorage.getItem('nkhwazi_active_tab') || 'pupil-details';
            }
            return 'pupil-details';
        }
        
        // Function to open a tab
        function openTab(tabName) {
            // Hide all tab content
            $('.tab-content').removeClass('active');
            
            // Remove active class from all tab buttons
            $('.tablinks').removeClass('active');
            
            // Show the current tab and add active class to the button
            $('#' + tabName).addClass('active');
            
            // Add active class to the button
            $('.tablinks').each(function() {
                if ($(this).attr('data-tab') === tabName) {
                    $(this).addClass('active');
                }
            });
            
            // Store the active tab
            setActiveTab(tabName);
        }
        
        // Set data-tab attribute on buttons
        $('.tablinks').each(function() {
            var onclick = $(this).attr('onclick');
            if (onclick) {
                var tabName = onclick.split("'")[1];
                $(this).attr('data-tab', tabName);
            }
        });
        
        // Remove onclick attributes
        $('.tablinks').removeAttr('onclick');
        
        // Add click event listeners
        $('.tablinks').on('click', function(e) {
            e.preventDefault();
            var tabName = $(this).attr('data-tab');
            openTab(tabName);
        });
        
        // Open the active tab
        openTab(getActiveTab());
    });
    </script>
    <?php
}

/**
 * Add settings page for application form
 */
function nkhwazi_application_form_settings_menu() {
    add_submenu_page(
        'edit.php?post_type=school_application',
        __('Application Form Settings', 'nkhwazischool'),
        __('Settings', 'nkhwazischool'),
        'manage_options',
        'application-form-settings',
        'nkhwazi_application_form_settings_page'
    );
}
add_action('admin_menu', 'nkhwazi_application_form_settings_menu');

/**
 * Application form settings page callback
 */
function nkhwazi_application_form_settings_page() {
    // Save settings
    if (isset($_POST['nkhwazi_application_settings_nonce']) && wp_verify_nonce($_POST['nkhwazi_application_settings_nonce'], 'nkhwazi_application_settings')) {
        if (isset($_POST['nkhwazi_application_email'])) {
            $email = sanitize_email($_POST['nkhwazi_application_email']);
            if (!empty($email)) {
                update_option('nkhwazi_application_email', $email);
                echo '<div class="notice notice-success is-dismissible"><p>' . __('Settings saved successfully.', 'nkhwazischool') . '</p></div>';
            }
        }
    }
    
    // Get current email
    $current_email = get_option('nkhwazi_application_email', get_option('admin_email'));
    
    ?>
    <div class="wrap">
        <h1><?php _e('Application Form Settings', 'nkhwazischool'); ?></h1>
        <form method="post" action="">
            <?php wp_nonce_field('nkhwazi_application_settings', 'nkhwazi_application_settings_nonce'); ?>
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="nkhwazi_application_email"><?php _e('Notification Email', 'nkhwazischool'); ?></label>
                    </th>
                    <td>
                        <input type="email" name="nkhwazi_application_email" id="nkhwazi_application_email" value="<?php echo esc_attr($current_email); ?>" class="regular-text">
                        <p class="description"><?php _e('Email address where application form submissions will be sent.', 'nkhwazischool'); ?></p>
                    </td>
                </tr>
            </table>
            <?php submit_button(); ?>
        </form>
    </div>
    <?php
}

/**
 * Enqueue application form script and styles
 */
function nkhwazi_enqueue_application_form_script() {
    // For testing purposes, load on all pages
    // In production, you would only load on specific pages
    // if (is_page_template('page-apply.php') || is_page('apply-online') || is_page('apply') || has_block('acf/application-form')) {
    
    // Debug
    error_log('Enqueuing application form scripts on page: ' . get_the_title());
        // Enqueue CSS
        wp_enqueue_style(
            'nkhwazischool-application-form',
            get_template_directory_uri() . '/assets/css/application-form.css',
            array(),
            filemtime(get_template_directory() . '/assets/css/application-form.css')
        );

        // Enqueue JavaScript
        wp_enqueue_script(
            'nkhwazischool-application-form',
            get_template_directory_uri() . '/assets/js/application-form.js',
            array('jquery', 'jquery-ui-datepicker'),
            filemtime(get_template_directory() . '/assets/js/application-form.js'),
            true
        );

        // Pass AJAX URL to script
        wp_localize_script(
            'nkhwazischool-application-form',
            'nkhwazi_application_form',
            array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('nkhwazi_application_form_nonce')
            )
        );
    // }
}
add_action('wp_enqueue_scripts', 'nkhwazi_enqueue_application_form_script');

/**
 * Handle application form submission via AJAX
 */
function nkhwazi_submit_application_form() {
    // Log the AJAX request for debugging
    error_log('AJAX request received for nkhwazi_submit_application_form');
    error_log('POST data: ' . print_r($_POST, true));
    
    // Check if this is a test request
    if (isset($_POST['test'])) {
        error_log('Test request received');
        wp_send_json_success(array(
            'message' => 'AJAX endpoint is working correctly',
            'test_data' => $_POST['test']
        ));
        wp_die();
    }
    
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'nkhwazi_application_form_nonce')) {
        error_log('Nonce verification failed');
        wp_send_json_error(array(
            'message' => __('Security check failed. Please refresh the page and try again.', 'nkhwazischool')
        ));
        wp_die();
    }
    
    // For debugging purposes, let's accept partial form submissions during testing
    // In production, you would validate all required fields
    
    // Check if we have at least the basic pupil information
    if (empty($_POST['fname']) || empty($_POST['lname'])) {
        wp_send_json_error(array(
            'message' => __('Please provide at least the pupil\'s first and last name.', 'nkhwazischool')
        ));
        return;
    }
    
    // In a real implementation, you would validate all required fields like this:
    /*
    $required_fields = array(
        // Pupil's details
        'lname' => __('Pupil\'s Last Name', 'nkhwazischool'),
        'fname' => __('Pupil\'s First Name', 'nkhwazischool'),
        // ... other fields ...
    );
    
    $missing_fields = array();
    foreach ($required_fields as $field => $label) {
        if (empty($_POST[$field])) {
            $missing_fields[] = $label;
        }
    }
    
    if (!empty($missing_fields)) {
        wp_send_json_error(array(
            'message' => __('Please fill in all required fields.', 'nkhwazischool'),
            'fields' => $missing_fields
        ));
        return;
    }
    */
    
    // For debugging purposes, we'll skip email validation
    // In production, you would validate emails like this:
    /*
    if (!empty($_POST['email_f']) && !is_email($_POST['email_f'])) {
        wp_send_json_error(array(
            'message' => __('Please enter a valid email address for Father.', 'nkhwazischool')
        ));
        return;
    }
    
    if (!empty($_POST['email_m']) && !is_email($_POST['email_m'])) {
        wp_send_json_error(array(
            'message' => __('Please enter a valid email address for Mother.', 'nkhwazischool')
        ));
        return;
    }
    */
    
    // Debug the POST data
    error_log('POST data: ' . print_r($_POST, true));
    
    // Sanitize basic input
    $lname = isset($_POST['lname']) ? sanitize_text_field($_POST['lname']) : '';
    $fname = isset($_POST['fname']) ? sanitize_text_field($_POST['fname']) : '';
    $grade = isset($_POST['grade']) ? sanitize_text_field($_POST['grade']) : 'Unknown';
    
    // Create post title
    $post_title = sprintf(__('Application for %s %s - %s', 'nkhwazischool'), $fname, $lname, $grade);
    
    // Log the post creation attempt
    error_log('Attempting to create application post with title: ' . $post_title);
    
    // Create new application post
    $post_args = array(
        'post_title' => $post_title,
        'post_type' => 'school_application',
        'post_status' => 'publish'
    );
    
    error_log('Post arguments: ' . print_r($post_args, true));
    
    $post_id = wp_insert_post($post_args);
    
    if (is_wp_error($post_id)) {
        error_log('Error creating post: ' . $post_id->get_error_message());
        wp_send_json_error(array(
            'message' => __('Failed to save your application. Please try again.', 'nkhwazischool')
        ));
        wp_die();
    } else {
        error_log('Post created successfully with ID: ' . $post_id);
    }
    
    // Save all POST data as post meta
    foreach ($_POST as $key => $value) {
        if ($key !== 'action' && $key !== 'nonce' && $key !== 'subject') {
            // Initialize meta key
            $meta_key = '';
            
            // Handle father's details
            if (strpos($key, '_f') !== false) {
                // Convert lname_f to _father_lname
                $field_name = str_replace('_f', '', $key);
                $meta_key = '_father_' . $field_name;
            }
            // Handle mother's details
            else if (strpos($key, '_m') !== false) {
                // Convert lname_m to _mother_lname
                $field_name = str_replace('_m', '', $key);
                $meta_key = '_mother_' . $field_name;
            }
            // Handle emergency contact
            else if (strpos($key, 'emergency_') !== false) {
                $meta_key = '_' . $key;
            }
            // Handle pupil details (everything else)
            else {
                $meta_key = '_pupil_' . $key;
            }
            
            // Log the meta key and value
            error_log("Saving meta: {$meta_key} = {$value}");
            
            // Save the meta
            update_post_meta($post_id, $meta_key, sanitize_text_field($value));
        }
    }
    
    // Save IP address
    update_post_meta($post_id, '_application_ip', $_SERVER['REMOTE_ADDR']);
    
    // Send email notification
    $to = get_option('nkhwazi_application_email', get_option('admin_email'));
    $email_subject = sprintf(__('[%s] New School Application: %s %s', 'nkhwazischool'), get_bloginfo('name'), $fname, $lname);
    
    $email_body = sprintf(__("You have received a new application from your website application form.\n\n", 'nkhwazischool'));
    $email_body .= sprintf(__("Pupil's Name: %s %s\n", 'nkhwazischool'), $fname, $lname);
    $email_body .= sprintf(__("Grade Applied For: %s\n\n", 'nkhwazischool'), $grade);
    
    $email_body .= sprintf(__("You can view this application in your WordPress dashboard: %s", 'nkhwazischool'), admin_url('post.php?post=' . $post_id . '&action=edit'));
    
    $headers = array(
        'Content-Type: text/plain; charset=UTF-8',
        'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>'
    );
    
    // For testing, we'll log the email instead of sending it
    error_log('Email would be sent to: ' . $to);
    error_log('Email subject: ' . $email_subject);
    error_log('Email body: ' . $email_body);
    
    // Uncomment this line to actually send the email
    // $email_sent = wp_mail($to, $email_subject, $email_body, $headers);
    
    // Verify the post was created
    $post = get_post($post_id);
    if ($post) {
        error_log('Post verification successful. Post exists with ID: ' . $post_id);
        error_log('Post title: ' . $post->post_title);
        error_log('Post type: ' . $post->post_type);
        error_log('Post status: ' . $post->post_status);
        
        // Check if the post is visible in the admin
        $edit_link = get_edit_post_link($post_id);
        error_log('Post edit link: ' . $edit_link);
        
        // Return success response
        wp_send_json_success(array(
            'message' => __('Thank you! Your application has been submitted successfully. We will contact you soon.', 'nkhwazischool'),
            'post_id' => $post_id,
            'post_title' => $post->post_title,
            'edit_link' => $edit_link
        ));
    } else {
        error_log('Post verification failed. No post found with ID: ' . $post_id);
        
        // Try to create the post again with a different approach
        $fallback_id = wp_insert_post(array(
            'post_title' => 'Fallback: ' . $post_title,
            'post_type' => 'school_application',
            'post_status' => 'publish'
        ), true);
        
        if (is_wp_error($fallback_id)) {
            error_log('Fallback post creation failed: ' . $fallback_id->get_error_message());
            wp_send_json_error(array(
                'message' => __('Failed to save your application. Please try again later.', 'nkhwazischool')
            ));
        } else {
            error_log('Fallback post created with ID: ' . $fallback_id);
            wp_send_json_success(array(
                'message' => __('Thank you! Your application has been submitted successfully. We will contact you soon.', 'nkhwazischool'),
                'post_id' => $fallback_id,
                'fallback' => true
            ));
        }
    }
    
    // Make sure to terminate properly
    wp_die();
}
add_action('wp_ajax_nkhwazi_submit_application_form', 'nkhwazi_submit_application_form');
add_action('wp_ajax_nopriv_nkhwazi_submit_application_form', 'nkhwazi_submit_application_form');