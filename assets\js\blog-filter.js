/**
 * Blog Category Filter Script
 * Handles client-side filtering of blog posts by category
 */
(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('Blog filter script loaded');
        initBlogFilter();
    });

    function initBlogFilter() {
        // Get all blog posts
        const blogPosts = $('.blog-post');
        console.log('Found ' + blogPosts.length + ' blog posts');

        // Debug: Log all posts and their categories
        blogPosts.each(function() {
            const categories = $(this).attr('data-categories') || '';
            console.log('Post: ' + $(this).find('h3').text() + ' - Categories: ' + categories);
        });

        // Get category filter links
        const categoryLinks = $('.blog-category-link');
        console.log('Found ' + categoryLinks.length + ' category links');
        
        // Add event listener to pagination links to preserve category parameter
        $('.pagination-nav a').on('click', function(e) {
            // Always prevent default to handle pagination ourselves
            e.preventDefault();
            
            // Get the href attribute of the pagination link
            const href = $(this).attr('href');
            
            // Create a URL object from the href
            const url = new URL(href, window.location.origin);
            
            // Get the current category from the URL
            const currentCategory = new URLSearchParams(window.location.search).get('category');
            
            // If we have a category parameter and it's not 'all' or 'undefined', add it to the URL
            if (currentCategory && currentCategory !== 'undefined' && currentCategory !== 'all') {
                url.searchParams.set('category', currentCategory);
            }
            
            // Navigate to the new URL
            window.location.href = url.toString();
        });

        // Debug: Log all category links
        categoryLinks.each(function() {
            const category = $(this).data('category');
            console.log('Category link: ' + $(this).text() + ' - Data category: ' + category);
        });

        // Set initial state based on URL parameter
        const urlParams = new URLSearchParams(window.location.search);
        let initialCategory = urlParams.get('category') || 'all';
        
        // Handle the case when category is 'undefined' or empty
        if (initialCategory === 'undefined' || initialCategory === '') {
            initialCategory = 'all';
        }
        
        console.log('Initial category: ' + initialCategory);

        // Set active class on page load
        categoryLinks.removeClass('active');
        if (initialCategory === 'all') {
            categoryLinks.filter('[data-category="all"]').addClass('active');
        } else {
            categoryLinks.filter('[data-category="' + initialCategory + '"]').addClass('active');
        }
        
        // Filter posts on page load
        filterPosts(initialCategory);

        // Add click event to category links
        categoryLinks.on('click', function(e) {
            e.preventDefault();

            // Get selected category
            const category = $(this).data('category');
            console.log('Category clicked: ' + category);
            
            // If category is undefined, use the href attribute instead
            if (category === undefined) {
                console.log('No data-category attribute found, navigating to href: ' + $(this).attr('href'));
                window.location.href = $(this).attr('href');
                return;
            }

            // Always update URL without reloading the page
            const url = new URL(window.location);
            
            // Set the category parameter
            if (category === 'all') {
                // Remove the category parameter for 'all'
                url.searchParams.delete('category');
            } else {
                url.searchParams.set('category', category);
            }
            
            // Always remove paged parameter to go back to first page when changing categories
            url.searchParams.delete('paged');
            
            // Update the URL
            window.history.pushState({}, '', url);

            // Update active class
            categoryLinks.removeClass('active');
            $(this).addClass('active');

            // Filter posts
            filterPosts(category);
        });

        // Function to filter posts by category
        function filterPosts(category) {
            console.log('Filtering posts by category: ' + category);

            // If 'all' is selected or category is undefined/empty, show all posts
            if (category === 'all' || category === undefined || category === 'undefined' || category === '') {
                console.log('Showing all posts');
                blogPosts.show();
                $('.no-posts-message').hide();
                return;
            }

            // Hide all posts first
            blogPosts.hide();

            // Show posts that match the selected category
            const matchingPosts = blogPosts.filter(function() {
                const postCategoriesAttr = $(this).attr('data-categories') || '';
                console.log('Post categories attribute: ' + postCategoriesAttr);

                if (!postCategoriesAttr) {
                    console.log('No categories attribute found for post');
                    return false;
                }

                // Split by spaces to get individual category slugs
                const postCategories = postCategoriesAttr.split(' ');
                console.log('Post categories array: ' + JSON.stringify(postCategories));

                // Check if the post has the selected category
                const matches = postCategories.includes(category);
                console.log('Post has category ' + category + ': ' + matches);

                return matches;
            });

            console.log('Found ' + matchingPosts.length + ' matching posts');
            matchingPosts.show();

            // If no posts are visible, show a message
            if (matchingPosts.length === 0) {
                console.log('No posts found in category: ' + category);
                if ($('.no-posts-message').length === 0) {
                    $('.grid.margin').append('<div class="s-12 no-posts-message"><p>No posts found in this category.</p></div>');
                } else {
                    $('.no-posts-message').show();
                }
            } else {
                $('.no-posts-message').hide();
            }
        }
    }

})(jQuery);