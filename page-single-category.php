<?php
/**
 * Template Name: Single School Category
 * Template Post Type: page
 * Description: A template for displaying detailed information about a specific school category.
 * Includes a sidebar with links to other categories.
 * Author: <PERSON>
 */

get_header();

// Get the category from URL parameter or from the URL path
$category_slug = isset($_GET['category']) ? sanitize_title($_GET['category']) : '';

// If no category is found in the query parameter, try to get it from the URL path
if (empty($category_slug)) {
    $uri = $_SERVER['REQUEST_URI'];
    if (preg_match('/^\/school-categories\/([^\/]+)\/?$/', $uri, $matches)) {
        $category_slug = $matches[1];
    }
}

// Set default values
$category_title = 'School Category';
$category_age_range = '';
$category_grades = '';
$category_description = '';
$category_image = '';

// Query for the school category post by slug
$args = array(
    'post_type' => 'school_categories',
    'name' => $category_slug,
    'posts_per_page' => 1,
    'post_status' => 'publish'
);

$category_query = new WP_Query($args);

if ($category_query->have_posts()) {
    $category_query->the_post();
    
    // Get the category data from ACF fields
    $category_title = get_the_title();
    $category_description = get_the_content();
    $category_age_range = get_field('age_range');
    $category_grades = get_field('grade_range');
    $card_img = get_field('card_img');
    $daily_schedule = get_field('daily_schedule');
    
    if ($card_img) {
        $category_image = wp_get_attachment_image_url($card_img['ID'], 'page-image');
    }
    
    wp_reset_postdata();
}
?>

<!-- MAIN -->
<main role="main">
  <!-- third section -->
  <article>
    <div class="line">
      <header class="rounded-div section background-primary background-transparent text-center"
        data-image-src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/parallax-03.webp">
        <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1"><?php echo $category_title; ?></h1>
      </header>
    </div>
    
    <!-- Full-width Category Image (only shown if image exists) -->
    <?php if (!empty($category_image)): ?>
    <div class="line margin-top-30 margin-bottom-30">
      <div class="s-12">
        <img src="<?php echo esc_url($category_image); ?>" alt="<?php echo esc_attr($category_title); ?>" class="full-width-img rounded-image">
      </div>
    </div>
    <?php endif; ?>

    <div class="section background-white">
      <div class="line">
        <div class="margin">
          <div class="s-12 m-8 l-9">
            <!-- put page content below, do not edit above this comment -->

            <?php 
            while (have_posts()) :
                the_post();
                the_content();
            endwhile;
            ?>

            <div class="line">
              <div class="margin">
                
                <?php 
                // Check if any content exists
                $has_content = !empty($category_age_range) || !empty($category_grades) || 
                               !empty($category_description) || !empty($daily_schedule);
                
                if ($has_content): 
                ?>
                
                <!-- Category Overview -->
                <div class="s-12 margin-bottom-30">
                  <h2 class="text-padding-small background-primary text-white text-strong text-uppercase margin-bottom-30">
                    <i class="icon-book margin-right-10"></i><?php echo $category_title; ?> Details
                  </h2>
                  
                  <!-- Key Information Box -->
                  <div class="margin-bottom-30">
                    <div class="category-info-box background-white border-radius box-shadow padding">
                      <div class="grid margin">
                        <?php if (!empty($category_grades)): ?>
                        <div class="s-12 m-4 l-2">
                          <h3 class="text-strong margin-bottom-5">Grades</h3>
                          <p><?php echo $category_grades; ?></p>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($category_age_range)): ?>
                        <div class="s-12 m-4 l-2">
                          <h3 class="text-strong margin-bottom-5">Ages</h3>
                          <p><?php echo $category_age_range; ?> years</p>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($daily_schedule)): ?>
                        <div class="s-12 m-4 l-8">
                          <h3 class="text-strong margin-bottom-5">Daily Schedule</h3>
                          <?php echo wpautop($daily_schedule); ?>
                        </div>
                        <?php endif; ?>
                      </div>
                    </div>
                  </div>
                  
                  <?php if (!empty($category_description)): ?>
                  <div><?php echo wpautop($category_description); ?></div>
                  <?php endif; ?>
                </div>
                
                <?php else: ?>
                <!-- No Content Message -->
                <div class="s-12 margin-bottom-30">
                  <div class="text-center background-light-gray padding-2x border-radius">
                    <i class="icon-info_outline icon-3x text-primary margin-bottom-10"></i>
                    <h3 class="text-strong text-size-20 margin-bottom-10">No Information Available</h3>
                    <p>The details for this school category are currently being updated. Please check back later for complete information.</p>
                  </div>
                </div>
                <?php endif; ?>
              </div>
            </div>

            <!-- put page content above, do not edit below this comment -->
          </div><!-- Ends content -->

          <!-- Category Sidebar -->
          <?php get_template_part('includes/category-sidebar'); ?>

        </div>
      </div>
    </div>
  </article>
</main>

<?php get_footer(); ?>