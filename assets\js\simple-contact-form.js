/**
 * Simple Contact Form JavaScript
 *
 * Handles form validation, AJAX submission, and UI interactions
 */
jQuery(document).ready(function($) {
    'use strict';
    
    console.log('Simple contact form script loaded');
    
    // Define ajaxurl if not already defined
    if (typeof ajaxurl === 'undefined') {
        var ajaxurl = nkhwazi_contact_form.ajax_url || '/wp-admin/admin-ajax.php';
        console.log('Setting ajaxurl to:', ajaxurl);
    }
    
    // Initialize form on document ready
    initContactForm();
    
    // Also initialize on window load to catch forms loaded via AJAX
    $(window).on('load', function() {
        initContactForm();
    });
    
    /**
     * Initialize the contact form
     */
    function initContactForm() {
        // Find the contact form
        const $form = $('.simple-contact-form');
        
        if (!$form.length) {
            console.log('Simple contact form not found');
            return;
        }
        
        // Skip if already initialized
        if ($form.data('initialized')) {
            console.log('Form already initialized');
            return;
        }
        
        console.log('Simple contact form found - initializing');
        
        // Mark as initialized
        $form.data('initialized', true);
        
        // Handle form submission
        $form.on('submit', function(e) {
            e.preventDefault();
            
            console.log('Form submitted');
            
            // Get form elements
            const $submitButton = $form.find('button[type="submit"]');
            const $formMessage = $('#form-message');
            const originalButtonText = $submitButton.text();
            
            // Disable submit button and show loading state
            $submitButton.prop('disabled', true).addClass('loading').text('Sending...');
            
            // Clear previous messages
            $formMessage.removeClass('success error').hide().html('');
            
            // Check reCAPTCHA
            const recaptchaResponse = grecaptcha.getResponse();
            if (!recaptchaResponse.length) {
                showMessage('Please complete the reCAPTCHA verification.', 'error');
                $submitButton.prop('disabled', false).removeClass('loading').text(originalButtonText);
                return;
            }
            
            // Create form data
            const formData = new FormData($form[0]);
            formData.append('action', 'nkhwazi_submit_contact_form');
            formData.append('nonce', nkhwazi_contact_form.nonce);
            formData.append('g-recaptcha-response', recaptchaResponse);
            
            console.log('Sending AJAX request to:', ajaxurl);
            
            // Send AJAX request
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    console.log('AJAX response:', response);
                    
                    if (response.success) {
                        // Show success message
                        showMessage(response.data.message, 'success');
                        
                        // Reset form
                        $form[0].reset();
                        
                        // Reset reCAPTCHA
                        grecaptcha.reset();
                    } else {
                        // Show error message
                        showMessage(response.data.message, 'error');
                    }
                    
                    // Re-enable submit button
                    $submitButton.prop('disabled', false).removeClass('loading').text(originalButtonText);
                },
                error: function(xhr, status, error) {
                    console.error('AJAX error:', status, error);
                    console.log('Response text:', xhr.responseText);
                    
                    // Show error message
                    showMessage('An error occurred. Please try again later.', 'error');
                    
                    // Re-enable submit button
                    $submitButton.prop('disabled', false).removeClass('loading').text(originalButtonText);
                }
            });
        });
    }
    
    /**
     * Show a message in the form message container
     */
    function showMessage(message, type) {
        const $formMessage = $('#form-message');
        
        console.log('Showing message:', message, 'Type:', type);
        
        // Set message content and class
        $formMessage.html(message).removeClass('success error').addClass(type).show();
        
        // Scroll to message
        $('html, body').animate({
            scrollTop: $formMessage.offset().top - 100
        }, 300);
    }
});