# WordPress specific files to ignore
*.log
wp-config.php
wp-content/advanced-cache.php
wp-content/backup-db/
wp-content/backups/
wp-content/blogs.dir/
wp-content/cache/
wp-content/upgrade/
wp-content/uploads/
wp-content/wp-cache-config.php
wp-content/plugins/hello.php

# Ignore large image files
assets/img/**/*.jpg
assets/img/**/*.JPG
assets/img/**/*.jpeg
assets/img/**/*.png
assets/img/**/*.gif
assets/img/**/*.svg
assets/img/**/*.mp4
assets/img/**/*.psd

# But keep these specific files
!assets/img/nkhwazi-logo.svg
!assets/img/parallax-01.jpg
!assets/img/parallax-02.jpg
!assets/img/parallax-03.jpg
!assets/img/parallax-04.jpg
!assets/img/parallax-05.jpg
!assets/img/parallax-06.jpg

# Ignore system files
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini

# Ignore node dependency directories
node_modules/

# Ignore log files and databases
*.log
*.sql
*.sqlite

# Ignore compiled files
*.com
*.class
*.dll
*.exe
*.o
*.so

# Ignore packaged files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Ignore environment specific files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Ignore IDE and editor folders
.idea/
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace
.history/

# Ignore SASS cache files
.sass-cache/
*.css.map
*.sass.map
*.scss.map

# Ignore composer files
vendor/
composer.phar