<?php
/**
 * Application Form Functionality
 *
 * @package NkhwaziSchool
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * Register Application Custom Post Type
 */
function nkhwazi_register_application_cpt() {
    register_post_type('school_application', array(
        'labels' => array(
            'name'               => __('Applications', 'nkhwazischool'),
            'singular_name'      => __('Application', 'nkhwazischool'),
            'add_new'            => __('Add New', 'nkhwazischool'),
            'add_new_item'       => __('Add New Application', 'nkhwazischool'),
            'edit_item'          => __('Edit Application', 'nkhwazischool'),
            'new_item'           => __('New Application', 'nkhwazischool'),
            'view_item'          => __('View Application', 'nkhwazischool'),
            'search_items'       => __('Search Applications', 'nkhwazischool'),
            'not_found'          => __('No applications found', 'nkhwazischool'),
            'not_found_in_trash' => __('No applications found in Trash', 'nkhwazischool'),
            'menu_name'          => __('Applications', 'nkhwazischool'),
        ),
        'public'              => false,
        'publicly_queryable'  => false,
        'show_ui'             => true,
        'show_in_menu'        => true,
        'show_in_rest'        => false,
        'query_var'           => false,
        'rewrite'             => false,
        'capability_type'     => 'post',
        'has_archive'         => false,
        'hierarchical'        => false,
        'menu_position'       => 30,
        'menu_icon'           => 'dashicons-welcome-learn-more',
        'supports'            => array('title'),
    ));
}
add_action('init', 'nkhwazi_register_application_cpt');



/**
 * Add meta boxes for applications
 */
function nkhwazi_application_meta_boxes() {
    add_meta_box(
        'nkhwazi_application_details',
        __('Application Details', 'nkhwazischool'),
        'nkhwazi_application_details_callback',
        'school_application',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'nkhwazi_application_meta_boxes');

/**
 * Application details meta box callback
 */
function nkhwazi_application_details_callback($post) {
    // Get application details - Pupil's details
    $lname = get_post_meta($post->ID, '_pupil_lname', true);
    $fname = get_post_meta($post->ID, '_pupil_fname', true);
    $sex = get_post_meta($post->ID, '_pupil_sex', true);
    $dob = get_post_meta($post->ID, '_pupil_dob', true);
    $nationality = get_post_meta($post->ID, '_pupil_nationality', true);
    $grade = get_post_meta($post->ID, '_pupil_grade', true);
    $previous_school = get_post_meta($post->ID, '_pupil_previous_school', true);
    $handicap = get_post_meta($post->ID, '_pupil_handicap', true);
    
    // Father's details
    $lname_f = get_post_meta($post->ID, '_father_lname', true);
    $fname_f = get_post_meta($post->ID, '_father_fname', true);
    $nationality_f = get_post_meta($post->ID, '_father_nationality', true);
    $identity_f = get_post_meta($post->ID, '_father_identity', true);
    $occupation_f = get_post_meta($post->ID, '_father_occupation', true);
    $employer_f = get_post_meta($post->ID, '_father_employer', true);
    $address_f = get_post_meta($post->ID, '_father_address', true);
    $address_postal_f = get_post_meta($post->ID, '_father_address_postal', true);
    $email_f = get_post_meta($post->ID, '_father_email', true);
    $phone_f = get_post_meta($post->ID, '_father_phone', true);
    $phone_work_f = get_post_meta($post->ID, '_father_phone_work', true);
    $res_status_f = get_post_meta($post->ID, '_father_res_status', true);
    
    // Mother's details
    $lname_m = get_post_meta($post->ID, '_mother_lname', true);
    $fname_m = get_post_meta($post->ID, '_mother_fname', true);
    $nationality_m = get_post_meta($post->ID, '_mother_nationality', true);
    $identity_m = get_post_meta($post->ID, '_mother_identity', true);
    $occupation_m = get_post_meta($post->ID, '_mother_occupation', true);
    $employer_m = get_post_meta($post->ID, '_mother_employer', true);
    $address_m = get_post_meta($post->ID, '_mother_address', true);
    $address_postal_m = get_post_meta($post->ID, '_mother_address_postal', true);
    $email_m = get_post_meta($post->ID, '_mother_email', true);
    $phone_m = get_post_meta($post->ID, '_mother_phone', true);
    $phone_work_m = get_post_meta($post->ID, '_mother_phone_work', true);
    $res_status_m = get_post_meta($post->ID, '_mother_res_status', true);
    
    // Submission details
    $date = get_the_date('F j, Y \a\t g:i a', $post->ID);
    $ip = get_post_meta($post->ID, '_application_ip', true);
    
    // Output application details in tabs
    ?>
    <style>
        .application-tabs {
            border-bottom: 1px solid #ccc;
            margin-bottom: 20px;
            overflow: hidden;
        }
        .application-tabs button {
            background-color: #f1f1f1;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 10px 16px;
            transition: 0.3s;
            font-size: 14px;
            border-radius: 4px 4px 0 0;
            margin-right: 5px;
        }
        .application-tabs button:hover {
            background-color: #ddd;
        }
        .application-tabs button.active {
            background-color: #0073aa;
            color: white;
        }
        .tab-content {
            display: none;
            padding: 15px;
            border: 1px solid #ccc;
            border-top: none;
            animation: fadeEffect 1s;
        }
        @keyframes fadeEffect {
            from {opacity: 0;}
            to {opacity: 1;}
        }
        .tab-content.active {
            display: block;
        }
        .application-field {
            margin-bottom: 15px;
        }
        .application-field label {
            font-weight: bold;
            display: block;
            margin-bottom: 5px;
        }
        .application-field-value {
            padding: 8px;
            background: #f9f9f9;
            border: 1px solid #e5e5e5;
        }
        .application-meta {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px dashed #ccc;
            font-size: 12px;
            color: #666;
        }
        
        /* Additional styles for better tab handling */
        .application-tabs {
            position: relative;
            z-index: 10;
        }
        .tab-content {
            position: relative;
            z-index: 5;
        }
    </style>
    
    <div class="application-tabs">
        <button class="tablinks" data-tab="pupil-details">Pupil's Details</button>
        <button class="tablinks" data-tab="father-details">Father's Details</button>
        <button class="tablinks" data-tab="mother-details">Mother's Details</button>
        <button class="tablinks" data-tab="additional-details">Additional Information</button>
    </div>
    
    <!-- Pupil's Details Tab -->
    <div id="pupil-details" class="tab-content active">
        <h3>Pupil's Details</h3>
        
        <div class="application-field">
            <label>Full Name:</label>
            <div class="application-field-value"><?php echo esc_html($fname . ' ' . $lname); ?></div>
        </div>
        
        <div class="application-field">
            <label>Sex:</label>
            <div class="application-field-value"><?php echo esc_html($sex === 'M' ? 'Male' : 'Female'); ?></div>
        </div>
        
        <div class="application-field">
            <label>Date of Birth:</label>
            <div class="application-field-value"><?php echo esc_html($dob); ?></div>
        </div>
        
        <div class="application-field">
            <label>Nationality:</label>
            <div class="application-field-value"><?php echo esc_html($nationality); ?></div>
        </div>
        
        <div class="application-field">
            <label>Grade Applied For:</label>
            <div class="application-field-value"><?php echo esc_html($grade); ?></div>
        </div>
        
        <div class="application-field">
            <label>Previous School:</label>
            <div class="application-field-value"><?php echo esc_html($previous_school); ?></div>
        </div>
        
        <div class="application-field">
            <label>Special Needs/Handicap:</label>
            <div class="application-field-value"><?php echo !empty($handicap) ? esc_html($handicap) : 'None'; ?></div>
        </div>
    </div>
    
    <!-- Father's Details Tab -->
    <div id="father-details" class="tab-content">
        <h3>Father's Details</h3>
        
        <div class="application-field">
            <label>Full Name:</label>
            <div class="application-field-value"><?php echo esc_html($fname_f . ' ' . $lname_f); ?></div>
        </div>
        
        <div class="application-field">
            <label>Nationality:</label>
            <div class="application-field-value"><?php echo esc_html($nationality_f); ?></div>
        </div>
        
        <div class="application-field">
            <label>ID/Passport Number:</label>
            <div class="application-field-value"><?php echo esc_html($identity_f); ?></div>
        </div>
        
        <div class="application-field">
            <label>Occupation:</label>
            <div class="application-field-value"><?php echo esc_html($occupation_f); ?></div>
        </div>
        
        <div class="application-field">
            <label>Employer:</label>
            <div class="application-field-value"><?php echo esc_html($employer_f); ?></div>
        </div>
        
        <div class="application-field">
            <label>Physical Address:</label>
            <div class="application-field-value"><?php echo esc_html($address_f); ?></div>
        </div>
        
        <?php if (!empty($address_postal_f)) : ?>
        <div class="application-field">
            <label>Postal Address:</label>
            <div class="application-field-value"><?php echo esc_html($address_postal_f); ?></div>
        </div>
        <?php endif; ?>
        
        <div class="application-field">
            <label>Email Address:</label>
            <div class="application-field-value"><?php echo esc_html($email_f); ?></div>
        </div>
        
        <div class="application-field">
            <label>Phone Number:</label>
            <div class="application-field-value"><?php echo esc_html($phone_f); ?></div>
        </div>
        
        <?php if (!empty($phone_work_f)) : ?>
        <div class="application-field">
            <label>Work Phone:</label>
            <div class="application-field-value"><?php echo esc_html($phone_work_f); ?></div>
        </div>
        <?php endif; ?>
        
        <div class="application-field">
            <label>Residence Status:</label>
            <div class="application-field-value"><?php echo esc_html($res_status_f); ?></div>
        </div>
    </div>
    
    <!-- Mother's Details Tab -->
    <div id="mother-details" class="tab-content">
        <h3>Mother's Details</h3>
        
        <div class="application-field">
            <label>Full Name:</label>
            <div class="application-field-value"><?php echo esc_html($fname_m . ' ' . $lname_m); ?></div>
        </div>
        
        <div class="application-field">
            <label>Nationality:</label>
            <div class="application-field-value"><?php echo esc_html($nationality_m); ?></div>
        </div>
        
        <div class="application-field">
            <label>ID/Passport Number:</label>
            <div class="application-field-value"><?php echo esc_html($identity_m); ?></div>
        </div>
        
        <div class="application-field">
            <label>Occupation:</label>
            <div class="application-field-value"><?php echo esc_html($occupation_m); ?></div>
        </div>
        
        <div class="application-field">
            <label>Employer:</label>
            <div class="application-field-value"><?php echo esc_html($employer_m); ?></div>
        </div>
        
        <div class="application-field">
            <label>Physical Address:</label>
            <div class="application-field-value"><?php echo esc_html($address_m); ?></div>
        </div>
        
        <?php if (!empty($address_postal_m)) : ?>
        <div class="application-field">
            <label>Postal Address:</label>
            <div class="application-field-value"><?php echo esc_html($address_postal_m); ?></div>
        </div>
        <?php endif; ?>
        
        <div class="application-field">
            <label>Email Address:</label>
            <div class="application-field-value"><?php echo esc_html($email_m); ?></div>
        </div>
        
        <div class="application-field">
            <label>Phone Number:</label>
            <div class="application-field-value"><?php echo esc_html($phone_m); ?></div>
        </div>
        
        <?php if (!empty($phone_work_m)) : ?>
        <div class="application-field">
            <label>Work Phone:</label>
            <div class="application-field-value"><?php echo esc_html($phone_work_m); ?></div>
        </div>
        <?php endif; ?>
        
        <div class="application-field">
            <label>Residence Status:</label>
            <div class="application-field-value"><?php echo esc_html($res_status_m); ?></div>
        </div>
    </div>
    
    <!-- Additional Information Tab -->
    <div id="additional-details" class="tab-content">
        <h3>Additional Information</h3>
        
        <div class="application-field">
            <label>Fee Payer:</label>
            <div class="application-field-value"><?php echo esc_html(get_post_meta($post->ID, '_pupil_fees_payer', true)); ?></div>
        </div>
        
        <div class="application-field">
            <label>Siblings at School:</label>
            <div class="application-field-value"><?php 
                $siblings = get_post_meta($post->ID, '_pupil_siblings', true);
                echo !empty($siblings) ? esc_html($siblings) : 'None'; 
            ?></div>
        </div>
        
        <div class="application-field">
            <label>Waiting List Option:</label>
            <div class="application-field-value"><?php echo esc_html(get_post_meta($post->ID, '_pupil_waiting_list', true)); ?></div>
        </div>
        
        <div class="application-field">
            <label>Additional Information:</label>
            <div class="application-field-value"><?php 
                $more_info = get_post_meta($post->ID, '_pupil_more_info', true);
                echo !empty($more_info) ? esc_html($more_info) : 'None provided'; 
            ?></div>
        </div>
        
        <div class="application-field">
            <label>Declaration:</label>
            <div class="application-field-value">The applicant agreed to the terms and conditions upon submission.</div>
        </div>
    </div>
    
    <!-- Application Meta Information -->
    <div class="application-meta">
        <p><strong>Submitted:</strong> <?php echo esc_html($date); ?></p>
        <p><strong>IP Address:</strong> <?php echo esc_html($ip); ?></p>
    </div>
    
    <script>
    jQuery(document).ready(function($) {
        // Store the active tab in localStorage if available
        function setActiveTab(tabName) {
            if (typeof(Storage) !== "undefined") {
                localStorage.setItem('nkhwazi_active_tab', tabName);
            }
        }
        
        // Get the active tab from localStorage if available
        function getActiveTab() {
            if (typeof(Storage) !== "undefined") {
                return localStorage.getItem('nkhwazi_active_tab') || 'pupil-details';
            }
            return 'pupil-details';
        }
        
        // Function to open a tab
        function openTab(tabName) {
            // Hide all tab content
            $('.tab-content').removeClass('active');
            
            // Remove active class from all tab buttons
            $('.tablinks').removeClass('active');
            
            // Show the current tab and add active class to the button
            $('#' + tabName).addClass('active');
            
            // Add active class to the button
            $('.tablinks').each(function() {
                if ($(this).attr('data-tab') === tabName) {
                    $(this).addClass('active');
                }
            });
            
            // Store the active tab
            setActiveTab(tabName);
        }
        
        // Set data-tab attribute on buttons
        $('.tablinks').each(function() {
            var onclick = $(this).attr('onclick');
            if (onclick) {
                var tabName = onclick.split("'")[1];
                $(this).attr('data-tab', tabName);
            }
        });
        
        // Remove onclick attributes
        $('.tablinks').removeAttr('onclick');
        
        // Add click event listeners
        $('.tablinks').on('click', function(e) {
            e.preventDefault();
            var tabName = $(this).attr('data-tab');
            openTab(tabName);
        });
        
        // Open the active tab
        openTab(getActiveTab());
    });
    </script>
    <?php
}

/**
 * Add settings page for application form
 */
function nkhwazi_application_form_settings_menu() {
    add_submenu_page(
        'edit.php?post_type=school_application',
        __('Application Form Settings', 'nkhwazischool'),
        __('Settings', 'nkhwazischool'),
        'manage_options',
        'application-form-settings',
        'nkhwazi_application_form_settings_page'
    );
}
add_action('admin_menu', 'nkhwazi_application_form_settings_menu');

/**
 * Application form settings page callback
 */
function nkhwazi_application_form_settings_page() {
    // Save settings
    if (isset($_POST['nkhwazi_application_settings_nonce']) && wp_verify_nonce($_POST['nkhwazi_application_settings_nonce'], 'nkhwazi_application_settings')) {
        $settings_updated = false;
        
        // Save notification email
        if (isset($_POST['nkhwazi_application_email'])) {
            $email = sanitize_email($_POST['nkhwazi_application_email']);
            if (!empty($email)) {
                update_option('nkhwazi_application_email', $email);
                $settings_updated = true;
            }
        }
        
        // Save CC emails
        if (isset($_POST['nkhwazi_application_cc_emails'])) {
            $cc_emails = sanitize_textarea_field($_POST['nkhwazi_application_cc_emails']);
            
            // Validate and clean CC emails
            $cc_emails_array = explode("\n", $cc_emails);
            $valid_cc_emails = array();
            $invalid_cc_emails = array();
            
            foreach ($cc_emails_array as $cc_email) {
                $cc_email = trim($cc_email);
                if (!empty($cc_email)) {
                    if (is_email($cc_email)) {
                        $valid_cc_emails[] = $cc_email;
                    } else {
                        $invalid_cc_emails[] = $cc_email;
                    }
                }
            }
            
            // Save only valid emails
            $cleaned_cc_emails = implode("\n", $valid_cc_emails);
            update_option('nkhwazi_application_cc_emails', $cleaned_cc_emails);
            $settings_updated = true;
            
            // Show warning for invalid emails
            if (!empty($invalid_cc_emails)) {
                echo '<div class="notice notice-warning is-dismissible"><p>' . 
                    sprintf(__('The following email addresses were invalid and have been removed: %s', 'nkhwazischool'), 
                    '<code>' . implode('</code>, <code>', $invalid_cc_emails) . '</code>') . 
                    '</p></div>';
            }
        }
        
        // Save email subject prefix
        if (isset($_POST['nkhwazi_application_subject_prefix'])) {
            $subject_prefix = sanitize_text_field($_POST['nkhwazi_application_subject_prefix']);
            update_option('nkhwazi_application_subject_prefix', $subject_prefix);
            $settings_updated = true;
        }
        
        // Save email footer text
        if (isset($_POST['nkhwazi_application_email_footer'])) {
            $email_footer = wp_kses_post($_POST['nkhwazi_application_email_footer']);
            update_option('nkhwazi_application_email_footer', $email_footer);
            $settings_updated = true;
        }
        
        if ($settings_updated) {
            echo '<div class="notice notice-success is-dismissible"><p>' . __('Settings saved successfully.', 'nkhwazischool') . '</p></div>';
        }
    }
    
    // Get current settings
    $current_email = get_option('nkhwazi_application_email', get_option('admin_email'));
    $cc_emails = get_option('nkhwazi_application_cc_emails', '');
    $subject_prefix = get_option('nkhwazi_application_subject_prefix', '[' . get_bloginfo('name') . ']');
    $email_footer = get_option('nkhwazi_application_email_footer', 'This is an automated email from ' . get_bloginfo('name') . ' website.');
    
    ?>
    <div class="wrap">
        <h1><?php _e('Application Form Settings', 'nkhwazischool'); ?></h1>
        
        <form method="post" action="">
            <?php wp_nonce_field('nkhwazi_application_settings', 'nkhwazi_application_settings_nonce'); ?>
            
            <h2><?php _e('Email Notification Settings', 'nkhwazischool'); ?></h2>
            <p><?php _e('Configure how application form submissions are sent via email.', 'nkhwazischool'); ?></p>
            
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="nkhwazi_application_email"><?php _e('Primary Notification Email', 'nkhwazischool'); ?></label>
                    </th>
                    <td>
                        <input type="email" name="nkhwazi_application_email" id="nkhwazi_application_email" value="<?php echo esc_attr($current_email); ?>" class="regular-text">
                        <p class="description"><?php _e('Main email address where application form submissions will be sent.', 'nkhwazischool'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="nkhwazi_application_cc_emails"><?php _e('Additional CC Recipients', 'nkhwazischool'); ?></label>
                    </th>
                    <td>
                        <textarea name="nkhwazi_application_cc_emails" id="nkhwazi_application_cc_emails" rows="3" class="large-text"><?php echo esc_textarea($cc_emails); ?></textarea>
                        <p class="description"><?php _e('Optional. Enter additional email addresses (one per line). These addresses will receive a copy of each application submission.', 'nkhwazischool'); ?></p>
                        <p class="description"><strong><?php _e('Important Notes:', 'nkhwazischool'); ?></strong></p>
                        <ul class="description" style="margin-left: 20px; list-style-type: disc;">
                            <li><?php _e('Email delivery depends on your server\'s mail configuration and recipient mail server settings.', 'nkhwazischool'); ?></li>
                            <li><?php _e('If recipients are not receiving emails, ask them to check their spam folder.', 'nkhwazischool'); ?></li>
                            <li><?php _e('Recipients should whitelist the sender address: ', 'nkhwazischool'); ?><code><?php echo esc_html(get_option('admin_email')); ?></code></li>
                            <li><?php _e('Use the "Send Test Email" button below to verify your configuration.', 'nkhwazischool'); ?></li>
                        </ul>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="nkhwazi_application_subject_prefix"><?php _e('Email Subject Prefix', 'nkhwazischool'); ?></label>
                    </th>
                    <td>
                        <input type="text" name="nkhwazi_application_subject_prefix" id="nkhwazi_application_subject_prefix" value="<?php echo esc_attr($subject_prefix); ?>" class="regular-text">
                        <p class="description"><?php _e('Text to prepend to the email subject line. Default is your site name in brackets.', 'nkhwazischool'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="nkhwazi_application_email_footer"><?php _e('Email Footer Text', 'nkhwazischool'); ?></label>
                    </th>
                    <td>
                        <textarea name="nkhwazi_application_email_footer" id="nkhwazi_application_email_footer" rows="3" class="large-text"><?php echo esc_textarea($email_footer); ?></textarea>
                        <p class="description"><?php _e('Custom text to display in the footer of notification emails.', 'nkhwazischool'); ?></p>
                    </td>
                </tr>
            </table>
            
            <h3><?php _e('Email Testing', 'nkhwazischool'); ?></h3>
            
            <?php
            // Handle test email
            if (isset($_POST['send_test_email']) && isset($_POST['nkhwazi_application_settings_nonce']) && wp_verify_nonce($_POST['nkhwazi_application_settings_nonce'], 'nkhwazi_application_settings')) {
                $to = get_option('nkhwazi_application_email', get_option('admin_email'));
                $subject = get_option('nkhwazi_application_subject_prefix', '[' . get_bloginfo('name') . ']') . ' Test Email';
                
                // Create a simple HTML email
                $message = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Test Email</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; }
        .header { background-color: #0073aa; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; }
        .footer { background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 12px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Test Email</h1>
    </div>
    <div class="content">
        <p>This is a test email from the Nkhwazi School Application Form.</p>
        <p>If you are receiving this email, your email configuration is working correctly.</p>
        <p>This email was sent to: ' . $to . '</p>';
                
                // Add CC recipients if configured
                $cc_emails = get_option('nkhwazi_application_cc_emails', '');
                $cc_list = array();
                if (!empty($cc_emails)) {
                    $cc_emails_array = explode("\n", $cc_emails);
                    foreach ($cc_emails_array as $cc_email) {
                        $cc_email = trim($cc_email);
                        if (!empty($cc_email) && is_email($cc_email)) {
                            $cc_list[] = $cc_email;
                        }
                    }
                    
                    if (!empty($cc_list)) {
                        $message .= '<p>CC recipients: ' . implode(', ', $cc_list) . '</p>';
                    }
                }
                
                $message .= '</div>
    <div class="footer">
        <p>' . get_option('nkhwazi_application_email_footer', 'This is an automated email from ' . get_bloginfo('name') . ' website.') . '</p>
    </div>
</body>
</html>';
                
                // Set up email headers
                $headers = array(
                    'Content-Type: text/html; charset=UTF-8',
                    'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>'
                );
                
                // Create a list of all recipients
                $all_recipients = array($to);
                if (!empty($cc_list)) {
                    $all_recipients = array_merge($all_recipients, $cc_list);
                }
                
                // Send the test email to all recipients
                $email_sent = wp_mail($all_recipients, $subject, $message, $headers);
                
                if ($email_sent) {
                    echo '<div class="notice notice-success is-dismissible"><p>' . 
                        sprintf(__('Test email sent successfully to %s', 'nkhwazischool'), '<strong>' . $to . '</strong>') . 
                        (!empty($cc_list) ? sprintf(__(', with CC to: %s', 'nkhwazischool'), '<strong>' . implode(', ', $cc_list) . '</strong>') : '') .
                        '</p></div>';
                } else {
                    echo '<div class="notice notice-error is-dismissible"><p>' . 
                        __('Failed to send test email. Please check your server\'s email configuration.', 'nkhwazischool') . 
                        '</p></div>';
                    
                    // Check for WordPress mail errors
                    global $phpmailer;
                    if (isset($phpmailer) && is_object($phpmailer) && isset($phpmailer->ErrorInfo) && !empty($phpmailer->ErrorInfo)) {
                        echo '<div class="notice notice-error is-dismissible"><p><strong>' . 
                            __('Error details:', 'nkhwazischool') . '</strong> ' . 
                            esc_html($phpmailer->ErrorInfo) . 
                            '</p></div>';
                    }
                }
            }
            ?>
            
            <p><?php _e('You can test your email configuration by clicking the button below or by submitting a test application through the form on your website.', 'nkhwazischool'); ?></p>
            
            <p>
                <?php submit_button(__('Send Test Email', 'nkhwazischool'), 'secondary', 'send_test_email', false); ?>
                <?php submit_button(__('Save Settings', 'nkhwazischool'), 'primary', 'submit', false); ?>
            </p>
        </form>
    </div>
    <?php
}

/**
 * Debug PHPMailer configuration
 * This function logs the PHPMailer configuration before sending
 */
function nkhwazi_debug_phpmailer($phpmailer) {
    error_log('PHPMailer Configuration:');
    error_log('From: ' . $phpmailer->From . ' (' . $phpmailer->FromName . ')');
    error_log('To: ' . print_r($phpmailer->getToAddresses(), true));
    error_log('CC: ' . print_r($phpmailer->getCcAddresses(), true));
    error_log('BCC: ' . print_r($phpmailer->getBccAddresses(), true));
    error_log('Subject: ' . $phpmailer->Subject);
    error_log('Content-Type: ' . $phpmailer->ContentType);
}
add_action('phpmailer_init', 'nkhwazi_debug_phpmailer');

/**
 * Enqueue application form script and styles
 */
function nkhwazi_enqueue_application_form_script() {
    // Only load on the apply page
    if (is_page_template('page-apply.php') || is_page('apply-online') || is_page('apply') || is_page(380) || has_block('acf/application-form')) {
        
        // Enqueue CSS
        wp_enqueue_style(
            'nkhwazischool-application-form',
            get_template_directory_uri() . '/assets/css/application-form.css',
            array(),
            filemtime(get_template_directory() . '/assets/css/application-form.css')
        );
        
        // Enqueue JavaScript
        wp_enqueue_script(
            'nkhwazischool-application-form',
            get_template_directory_uri() . '/assets/js/application-form.js',
            array('jquery', 'jquery-ui-datepicker'),
            filemtime(get_template_directory() . '/assets/js/application-form.js'),
            true
        );

        // Pass AJAX URL to script
        wp_localize_script(
            'nkhwazischool-application-form',
            'nkhwazi_application_form',
            array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('nkhwazi_application_form_nonce'),
                'recaptcha_site_key' => '6Lf79rcaAAAAALdqWY74mw_n6DtTxR_C5AEL6cfL'
            )
        );
    }
}
add_action('wp_enqueue_scripts', 'nkhwazi_enqueue_application_form_script');

/**
 * Handle application form submission via AJAX
 */
function nkhwazi_submit_application_form() {

    
    // Check if this is a test request
    if (isset($_POST['test'])) {
        wp_send_json_success(array(
            'message' => 'AJAX endpoint is working correctly',
            'test_data' => $_POST['test']
        ));
        wp_die();
    }
    
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'nkhwazi_application_form_nonce')) {
        wp_send_json_error(array(
            'message' => __('Security check failed. Please refresh the page and try again.', 'nkhwazischool')
        ));
        wp_die();
    }
    
    // Verify reCAPTCHA
    if (!isset($_POST['g-recaptcha-response']) || empty($_POST['g-recaptcha-response'])) {
        wp_send_json_error(array(
            'message' => __('Please complete the reCAPTCHA verification.', 'nkhwazischool')
        ));
        wp_die();
    }
    
    // Verify with Google reCAPTCHA API
    $recaptcha_secret = '6Lf79rcaAAAAACnBRBWJW8M7XLGq4o-tPhvn7r-J';
    $recaptcha_response = $_POST['g-recaptcha-response'];
    
    $verify_response = wp_remote_post('https://www.google.com/recaptcha/api/siteverify', array(
        'body' => array(
            'secret' => $recaptcha_secret,
            'response' => $recaptcha_response,
            'remoteip' => $_SERVER['REMOTE_ADDR']
        )
    ));
    
    if (is_wp_error($verify_response)) {
        wp_send_json_error(array(
            'message' => __('reCAPTCHA verification failed. Please try again.', 'nkhwazischool')
        ));
        wp_die();
    }
    
    $response_body = wp_remote_retrieve_body($verify_response);
    $response_data = json_decode($response_body, true);
    
    if (!isset($response_data['success']) || !$response_data['success']) {
        wp_send_json_error(array(
            'message' => __('reCAPTCHA verification failed. Please try again.', 'nkhwazischool')
        ));
        wp_die();
    }
    
    // For debugging purposes, let's accept partial form submissions during testing
    // In production, you would validate all required fields
    
    // Check if we have at least the basic pupil information
    if (empty($_POST['fname']) || empty($_POST['lname'])) {
        wp_send_json_error(array(
            'message' => __('Please provide at least the pupil\'s first and last name.', 'nkhwazischool')
        ));
        return;
    }
    
    // In a real implementation, you would validate all required fields like this:
    /*
    $required_fields = array(
        // Pupil's details
        'lname' => __('Pupil\'s Last Name', 'nkhwazischool'),
        'fname' => __('Pupil\'s First Name', 'nkhwazischool'),
        // ... other fields ...
    );
    
    $missing_fields = array();
    foreach ($required_fields as $field => $label) {
        if (empty($_POST[$field])) {
            $missing_fields[] = $label;
        }
    }
    
    if (!empty($missing_fields)) {
        wp_send_json_error(array(
            'message' => __('Please fill in all required fields.', 'nkhwazischool'),
            'fields' => $missing_fields
        ));
        return;
    }
    */
    
    // For debugging purposes, we'll skip email validation
    // In production, you would validate emails like this:
    /*
    if (!empty($_POST['email_f']) && !is_email($_POST['email_f'])) {
        wp_send_json_error(array(
            'message' => __('Please enter a valid email address for Father.', 'nkhwazischool')
        ));
        return;
    }
    
    if (!empty($_POST['email_m']) && !is_email($_POST['email_m'])) {
        wp_send_json_error(array(
            'message' => __('Please enter a valid email address for Mother.', 'nkhwazischool')
        ));
        return;
    }
    */
 
    // Sanitize basic input
    $lname = isset($_POST['lname']) ? sanitize_text_field($_POST['lname']) : '';
    $fname = isset($_POST['fname']) ? sanitize_text_field($_POST['fname']) : '';
    $grade = isset($_POST['grade']) ? sanitize_text_field($_POST['grade']) : 'Unknown';
    
    // Create post title
    $post_title = sprintf(__('Application for %s %s - %s', 'nkhwazischool'), $fname, $lname, $grade);
    
    // Create new application post
    $post_id = wp_insert_post(array(
        'post_title' => $post_title,
        'post_type' => 'school_application',
        'post_status' => 'publish'
    ));
    
    if (is_wp_error($post_id)) {
        error_log('Error creating post: ' . $post_id->get_error_message());
        wp_send_json_error(array(
            'message' => __('Failed to save your application. Please try again.', 'nkhwazischool')
        ));
        return;
    }
    
    // Save all POST data as post meta
    foreach ($_POST as $key => $value) {
        if ($key !== 'action' && $key !== 'nonce' && $key !== 'subject') {
            // Initialize meta key
            $meta_key = '';
            
            // Handle father's details
            if (strpos($key, '_f') !== false) {
                // Convert lname_f to _father_lname
                $field_name = str_replace('_f', '', $key);
                $meta_key = '_father_' . $field_name;
            }
            // Handle mother's details
            else if (strpos($key, '_m') !== false) {
                // Convert lname_m to _mother_lname
                $field_name = str_replace('_m', '', $key);
                $meta_key = '_mother_' . $field_name;
            }
            // Handle additional information fields
            else if (in_array($key, array('fees_payer', 'siblings', 'waiting_list', 'more_info'))) {
                $meta_key = '_pupil_' . $key;
            }
            // Handle pupil details (everything else)
            else {
                $meta_key = '_pupil_' . $key;
            }
            

            
            // Save the meta
            update_post_meta($post_id, $meta_key, sanitize_text_field($value));
        }
    }
    
    // Save IP address
    update_post_meta($post_id, '_application_ip', $_SERVER['REMOTE_ADDR']);
    
    // Send email notification
    $to = get_option('nkhwazi_application_email', get_option('admin_email'));
    
    // Get email settings
    $subject_prefix = get_option('nkhwazi_application_subject_prefix', '[' . get_bloginfo('name') . ']');
    $email_subject = sprintf(__('%s New School Application: %s %s', 'nkhwazischool'), $subject_prefix, $fname, $lname);
    
    // Create a detailed HTML email
    $email_body = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>New School Application</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; }
        .header { background-color: #0073aa; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; }
        .section { margin-bottom: 25px; border-bottom: 1px solid #eee; padding-bottom: 15px; }
        .section h2 { color: #0073aa; margin-top: 0; }
        .field { margin-bottom: 10px; }
        .field-label { font-weight: bold; }
        .footer { background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 12px; }
        .button { display: inline-block; background-color: #0073aa; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>New School Application Received</h1>
    </div>
    <div class="content">
        <p>You have received a new application from your website application form.</p>
        
        <div class="section">
            <h2>Pupil\'s Details</h2>';
    
    // Pupil's details
    $pupil_fields = array(
        'fname' => 'First Name',
        'lname' => 'Last Name',
        'sex' => 'Sex',
        'dob' => 'Date of Birth',
        'nationality' => 'Nationality',
        'grade' => 'Grade Applied For',
        'previous_school' => 'Previous School',
        'handicap' => 'Special Needs/Handicap'
    );
    
    foreach ($pupil_fields as $field => $label) {
        $value = isset($_POST[$field]) ? sanitize_text_field($_POST[$field]) : 'Not provided';
        if ($field === 'sex' && !empty($value)) {
            $value = ($value === 'M') ? 'Male' : 'Female';
        }
        if ($field === 'handicap' && empty($value)) {
            $value = 'None';
        }
        $email_body .= '<div class="field"><span class="field-label">' . $label . ':</span> ' . $value . '</div>';
    }
    
    // Father's details
    $email_body .= '</div><div class="section"><h2>Father\'s Details</h2>';
    $father_fields = array(
        'lname_f' => 'Last Name',
        'fname_f' => 'First Name',
        'nationality_f' => 'Nationality',
        'identity_f' => 'ID/Passport Number',
        'occupation_f' => 'Occupation',
        'employer_f' => 'Employer',
        'address_f' => 'Physical Address',
        'address_postal_f' => 'Postal Address',
        'email_f' => 'Email Address',
        'phone_f' => 'Phone Number',
        'phone_work_f' => 'Work Phone',
        'res_status_f' => 'Residence Status'
    );
    
    foreach ($father_fields as $field => $label) {
        $value = isset($_POST[$field]) ? sanitize_text_field($_POST[$field]) : '';
        if (!empty($value)) {
            $email_body .= '<div class="field"><span class="field-label">' . $label . ':</span> ' . $value . '</div>';
        }
    }
    
    // Mother's details
    $email_body .= '</div><div class="section"><h2>Mother\'s Details</h2>';
    $mother_fields = array(
        'lname_m' => 'Last Name',
        'fname_m' => 'First Name',
        'nationality_m' => 'Nationality',
        'identity_m' => 'ID/Passport Number',
        'occupation_m' => 'Occupation',
        'employer_m' => 'Employer',
        'address_m' => 'Physical Address',
        'address_postal_m' => 'Postal Address',
        'email_m' => 'Email Address',
        'phone_m' => 'Phone Number',
        'phone_work_m' => 'Work Phone',
        'res_status_m' => 'Residence Status'
    );
    
    foreach ($mother_fields as $field => $label) {
        $value = isset($_POST[$field]) ? sanitize_text_field($_POST[$field]) : '';
        if (!empty($value)) {
            $email_body .= '<div class="field"><span class="field-label">' . $label . ':</span> ' . $value . '</div>';
        }
    }
    
    // Additional Information
    $email_body .= '</div><div class="section"><h2>Additional Information</h2>';
    $additional_fields = array(
        'fees_payer' => 'Fee Payer',
        'siblings' => 'Siblings at School',
        'waiting_list' => 'Waiting List Option',
        'more_info' => 'Additional Information'
    );
    
    foreach ($additional_fields as $field => $label) {
        $value = isset($_POST[$field]) ? sanitize_text_field($_POST[$field]) : '';
        if (empty($value) && $field === 'siblings') {
            $value = 'None';
        }
        if (empty($value) && $field === 'more_info') {
            $value = 'None provided';
        }
        $email_body .= '<div class="field"><span class="field-label">' . $label . ':</span> ' . $value . '</div>';
    }
    
    // Add declaration
    $email_body .= '<div class="field"><span class="field-label">Declaration:</span> The applicant agreed to the terms and conditions upon submission.</div>';
    
    // Add link to view in dashboard
    $email_body .= '</div>
        <div class="section">
            <h2>Application Details</h2>
            <div class="field"><span class="field-label">Submission Date:</span> ' . current_time('F j, Y \a\t g:i a') . '</div>
            <div class="field"><span class="field-label">IP Address:</span> ' . $_SERVER['REMOTE_ADDR'] . '</div>
            <p><a href="' . admin_url('post.php?post=' . $post_id . '&action=edit') . '" class="button">View Full Application in Dashboard</a></p>
        </div>
    </div>
    <div class="footer">
        <p>' . get_option('nkhwazi_application_email_footer', 'This is an automated email from ' . get_bloginfo('name') . ' website.') . '</p>
    </div>
</body>
</html>';
    
    // Set up email headers for HTML email
    $headers = array(
        'Content-Type: text/html; charset=UTF-8',
        'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>'
    );
    
    // Process CC recipients
    $cc_emails = get_option('nkhwazi_application_cc_emails', '');
    $all_recipients = array($to); // Start with the main recipient
    
    if (!empty($cc_emails)) {
        // Log CC emails for debugging
        error_log('CC emails configured: ' . $cc_emails);
        
        // Process CC emails
        $cc_emails_array = explode("\n", $cc_emails);
        $valid_cc_emails = array();
        
        foreach ($cc_emails_array as $cc_email) {
            $cc_email = trim($cc_email);
            if (!empty($cc_email) && is_email($cc_email)) {
                $valid_cc_emails[] = $cc_email;
                $all_recipients[] = $cc_email; // Add to the recipients list
                error_log('Adding recipient: ' . $cc_email);
            } else if (!empty($cc_email)) {
                error_log('Invalid email format: ' . $cc_email);
            }
        }
    }
    
    // Log email details for debugging
    error_log('Sending application email to: ' . implode(', ', $all_recipients));
    error_log('Email subject: ' . $email_subject);
    error_log('Email headers: ' . print_r($headers, true));
    
    // Send the email to all recipients
    $email_sent = false;
    
    // First try sending to all recipients at once
    try {
        $email_sent = wp_mail($all_recipients, $email_subject, $email_body, $headers);
        error_log('Email sent to all recipients at once: ' . ($email_sent ? 'Success' : 'Failed'));
    } catch (Exception $e) {
        error_log('Exception when sending email: ' . $e->getMessage());
    }
    
    // If that fails, try sending individually
    if (!$email_sent && count($all_recipients) > 1) {
        error_log('Trying to send emails individually...');
        $email_sent = wp_mail($to, $email_subject, $email_body, $headers);
        error_log('Email sent to primary recipient: ' . ($email_sent ? 'Success' : 'Failed'));
        
        // Send to each CC recipient individually
        if (!empty($valid_cc_emails)) {
            foreach ($valid_cc_emails as $cc_email) {
                $cc_sent = wp_mail($cc_email, $email_subject, $email_body, $headers);
                error_log('Email sent to CC recipient ' . $cc_email . ': ' . ($cc_sent ? 'Success' : 'Failed'));
            }
        }
    }
    
    // Log email status
    if ($email_sent) {
        error_log('Application email sent successfully to: ' . $to);
    } else {
        error_log('Failed to send application email to: ' . $to);
        // Check for WordPress mail errors
        global $phpmailer;
        if (isset($phpmailer) && is_object($phpmailer) && isset($phpmailer->ErrorInfo) && !empty($phpmailer->ErrorInfo)) {
            error_log('PHPMailer error: ' . $phpmailer->ErrorInfo);
        }
    }
    
    // Return success response with email status
    $success_message = __('Thank you! Your application has been submitted successfully. We will contact you soon.', 'nkhwazischool');
    
    // Add email notification status if applicable
    if (isset($email_sent)) {
        if ($email_sent) {
            $success_message .= ' ' . __('A confirmation email has been sent to our admissions team.', 'nkhwazischool');
        } else {
            // Log email failure but don't tell the user
            error_log('Failed to send application email notification for application ID: ' . $post_id);
        }
    }
    
    wp_send_json_success(array(
        'message' => $success_message,
        'post_id' => $post_id
    ));
    
    // Make sure to terminate properly
    wp_die();
}
add_action('wp_ajax_nkhwazi_submit_application_form', 'nkhwazi_submit_application_form');
add_action('wp_ajax_nopriv_nkhwazi_submit_application_form', 'nkhwazi_submit_application_form');