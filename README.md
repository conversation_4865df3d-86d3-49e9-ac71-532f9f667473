# Nkhwazi Primary School WordPress Theme

A custom WordPress theme for Nkhwazi Primary School website.

## Description

This theme is built using the Responsee CSS Framework and includes custom post types and fields for school-specific content such as:
- Extracurricular activities
- School management
- Teaching staff
- Photo galleries
- Notifications
- Online applications

## CSS Framework

The theme uses the following CSS files:
- responsee.css - Base Responsee framework
- template-style.css - Template specific styles
- components.css - Component styles
- custom-style.scss - Custom SCSS file that compiles to custom-style.css

## JavaScript

The theme uses the following JavaScript files:
- responsee.js - Base Responsee framework
- template-styles.js - Template specific scripts

## Custom Post Types and Fields

Custom post types and fields are defined in:
- inc/custom-post-types.php
- inc/scf-fields.php

## Important Page IDs

The following page IDs are used throughout the site:
- Extracurricular: 335
- Extracurricular Item: 228
- Photo Gallery: 363
- Frontpage (homepage): 238
- Blog (home): 241
- School Category: 328
- Teaching Staff: 376
- School Management: 378
- Contact Us: 407
- Update Personal Details: 398
- Apply Online: 380
- Your Notifications: 426
- Upcoming Events: 434

## Development

This theme is developed locally using Local by Flywheel and version controlled with Git.