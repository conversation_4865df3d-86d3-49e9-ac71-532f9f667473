<?php
/**
 * Template Name: Management
 * Description: Displays only management members that have the governance toggle set to false or not set.
 *              Uses a balanced layout with a main content area (9 columns) and a sidebar (3 columns).
 * Author: <PERSON>
 */

// Add page-specific styles
echo '<style>
main.page-management .gallery-card .gallery-content { padding: 10px 10px 5px !important; }
main.page-management .gallery-card .gallery-content .title { margin-bottom: 5px !important; }
main.page-management .gallery-card .gallery-content .read-all { margin-bottom: 5px !important; margin-top: 5px !important; }
main.page-management .gallery-card .gallery-content h3 { margin-bottom: 5px !important; }
</style>';

get_header();

// Get the current page ID (using the provided ID 378 for reliability)
$page_id = 378;

// Check if a specific management type is requested via URL parameter
$requested_type = isset($_GET['type']) ? sanitize_text_field($_GET['type']) : '';

// Get management types to organize members by type (only non-governance types)
$management_types = get_posts(array(
    'post_type' => 'management_type',
    'posts_per_page' => -1,
    'orderby' => 'meta_value_num',
    'meta_key' => 'priority',
    'order' => 'ASC',
    'meta_query' => array(
        'relation' => 'OR',
        array(
            'key' => 'governance',
            'value' => '0',
            'compare' => '='
        ),
        array(
            'key' => 'governance',
            'compare' => 'NOT EXISTS'
        )
    )
));

// If no specific type is requested, find the type with priority 1
$default_type = null;
if (empty($requested_type)) {
    foreach ($management_types as $type) {
        $priority = get_field('priority', $type->ID);
        if ($priority == 1) {
            $default_type = $type;
            break;
        }
    }
    
    // If no type with priority 1 is found, use the first type
    if (!$default_type && !empty($management_types)) {
        $default_type = $management_types[0];
    }
}

?>
<!-- MAIN -->
<main role="main" class="page-management">
    <!-- third section -->
    <article>
        <div class="line">
            <header class="rounded-div section background-primary background-transparent text-center"
                data-image-src="<?php echo get_template_directory_uri(); ?>/assets/img/parallax-04.webp">
                <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1"><?php the_title(); ?></h1>
            </header>
        </div>

        <!-- Breadcrumb Navigation -->
        <?php get_template_part('includes/breadcrumb'); ?>

        <div class="section background-white">
            <div class="line">
                <div class="margin">
                    <div class="s-12 m-12 l-12">
                        <!-- put page content below, do not edit above this comment -->
                        
                        <div class="line">
                            <?php 
                            while (have_posts()) :
                                the_post();
                                the_content();
                            endwhile;
                            ?>
                        </div>
                    <!-- Gutenberg contents-->
                        <div class="line">
                            
                            <?php
                            // Determine which types to display
                            $types_to_display = array();
                            
                            if (!empty($requested_type)) {
                                // If a specific type is requested, find it by slug
                                foreach ($management_types as $type) {
                                    $type_name = get_field('management_type_name', $type->ID);
                                    $type_slug = sanitize_title($type_name);
                                    
                                    if ($type_slug === $requested_type) {
                                        $types_to_display[] = $type;
                                        break;
                                    }
                                }
                            } elseif ($default_type) {
                                // If no specific type is requested, use the default type (priority 1)
                                $types_to_display[] = $default_type;
                            }
                            
                            // If no types were found, show all types (fallback)
                            if (empty($types_to_display)) {
                                $types_to_display = $management_types;
                            }
                            
                            // Loop through each management type to display
                            foreach ($types_to_display as $type) :
                                // Get the management type name
                                $type_name = get_field('management_type_name', $type->ID);
                                $type_slug = sanitize_title($type_name);
                                
                                // Query management members of this type
                                $management_members = get_posts(array(
                                    'post_type' => 'management',
                                    'posts_per_page' => -1,
                                    'meta_query' => array(
                                        array(
                                            'key' => 'management_types',
                                            'value' => $type->ID,
                                            'compare' => '='
                                        )
                                    ),
                                    'orderby' => 'meta_value_num',
                                    'meta_key' => 'priority',
                                    'order' => 'ASC',
                                ));
                                
                                // Only display section if there are members
                                if (!empty($management_members)) :
                            ?>
                            
                            <h2 id="<?php echo esc_attr($type_slug); ?>" class="text-size-30 margin-bottom-10"><?php echo esc_html($type_name); ?></h2>
                            
                            <?php 
                            // Display description if available
                            $description = get_field('description', $type->ID);
                            if (!empty($description)) : 
                            ?>
                            <div class="margin-bottom-20">
                                <?php echo wpautop(esc_html($description)); ?>
                            </div>
                            <?php endif; ?>
                            
                            <!-- Team Cards -->
                            <div class="grid margin">
                                <?php 
                                // Loop through each management member
                                foreach ($management_members as $member) :
                                    // Get member details
                                    $title = get_field('title', $member->ID);
                                    $first_name = get_field('first_name', $member->ID);
                                    $last_name = get_field('last_name', $member->ID);
                                    $position = get_field('position', $member->ID);
                                    $portrait = get_field('portrait_img', $member->ID);
                                    $bio_excerpt = get_field('bio_excerpt', $member->ID);
                                    $full_name = $first_name . ' ' . $last_name;
                                    
                                    // Create a unique modal ID
                                    $modal_id = 'modal-' . $member->ID;
                                ?>
                                
                                <!-- <?php echo esc_html($position); ?> -->
                                <div class="card gallery-card s-12 m-6 l-3">
                                    <a href="#">
                                        <?php if ($portrait) : ?>
                                            <?php 
                                            // Use the team-photo size
                                            $team_image = wp_get_attachment_image(
                                                $portrait['ID'],
                                                'team-photo',
                                                false,
                                                array('class' => 'team-photo rounded-image', 'alt' => $full_name)
                                            );
                                            echo $team_image;
                                            ?>
                                        <?php else : ?>
                                            <img src="<?php echo get_template_directory_uri(); ?>/assets/img/team/placeholder.jpg" 
                                                 alt="<?php echo esc_attr($full_name); ?>" 
                                                 class="team-photo rounded-image">
                                        <?php endif; ?>
                                    </a>
                                    <div class="gallery-content">
                                        <h3><?php echo esc_html($title . ' ' . $first_name . ' ' . $last_name); ?></h3>
                                        <p class="title"><?php echo esc_html($position); ?></p>
                                        <div class="margin-top-20">
                                            <a class="modal-button read-all submit-btn"
                                                data-modal="<?php echo esc_attr($modal_id); ?>">View Bio</a>
                                        </div>
                                    </div>

                                    <!-- Modal content-->
                                    <div class="modal" id="<?php echo esc_attr($modal_id); ?>">
                                        <h3>Biography Container</h3>
                                        <div class="margin-bottom padding">
                                            <h2><?php echo esc_html($title . ' ' . $first_name . ' ' . $last_name); ?></h2>
                                            <h3><?php echo esc_html($position); ?></h3>

                                            <?php echo wpautop($member->post_content); ?>
                                        </div>
                                        <a class="modal-close-button button cancel-btn">Close the Bio</a>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <?php 
                                endif; // End if !empty management_members
                            endforeach; // End foreach management_types 
                            ?>
                        </div>

                        <?php
                        // Check if pagination is needed (future implementation)
                        $total_members = wp_count_posts('management')->publish;
                        if ($total_members > 12) : // Only show pagination if more than 12 members
                        ?>
                        <!-- Pagination -->
                        <div class="line text-center pagination-container">
                            <div class="s-12 margin-bottom">
                                <ul class="pagination">
                                    <li><a href="#" class="previous-page">Prev</a></li>
                                    <li><a href="#" class="active-page">1</a></li>
                                    <li><a href="#">2</a></li>
                                    <li><a href="#" class="next-page">Next</a></li>
                                </ul>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- put page content above, do not edit below this comment -->
                    </div><!-- Ends content -->

                </div>
            </div>
        </div>
    </article>
</main>

<?php get_footer(); ?>