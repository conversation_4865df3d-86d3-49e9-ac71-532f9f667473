/**
 * Admin scripts for Nkhwazi Primary School theme
 */
(function($) {
    'use strict';
    
    // Initialize ACF date pickers with current date
    acf.add_filter('date_picker_args', function(args, $field) {
        // Set default date to today
        args.defaultDate = new Date();
        
        // Create tomorrow's date
        var tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(0, 0, 0, 0);
        
        // Only allow dates from tomorrow onwards
        args.minDate = tomorrow;
        
        // Disable today and past dates
        args.beforeShowDay = function(date) {
            var today = new Date();
            today.setHours(0, 0, 0, 0);
            
            // Return an array with [0] = true/false (enabled/disabled), [1] = css class, [2] = tooltip
            return [date > today, ''];
        };
        
        return args;
    });
    
    // This code was for checking empty management_types field
    // Since we now have management types, we're disabling this functionality
    /*
    $(document).ready(function() {
        // Function to check if management_types select has options
        function checkManagementTypesField() {
            var $field = $('select[name="acf[field_management_types]"]');
            
            if ($field.length) {
                // If the field exists but has no options (or only the empty option)
                if ($field.find('option').length <= 1) {
                    // Add a message above the field
                    if (!$field.parent().prev('.management-types-notice').length) {
                        $field.parent().before('<div class="management-types-notice" style="color: #d63638; margin-bottom: 10px; padding: 8px; background: #fcf0f1; border-left: 4px solid #d63638;"><p><strong>No management types available.</strong> Please <a href="' + acf.get('admin_url') + 'post-new.php?post_type=management_type" target="_blank">create some management types</a> first.</p></div>');
                    }
                }
            }
        }
        
        // Run on page load
        checkManagementTypesField();
        
        // Also run when ACF fields are ready
        acf.add_action('ready', function() {
            checkManagementTypesField();
        });
    });
    */
    
})(jQuery);